import {
    _decorator,
    AudioClip,
    Collider,
    geometry,
    ITriggerEvent,
    Material,
    MeshRenderer,
    Node,
    physics,
    RigidBody,
    SkeletalAnimation,
    tween,
} from 'cc';

import { ClientConst, MusicConf, PHY_GROUP } from '../../common/ClientConst';

import { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';
import { CCComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp';
import { smc } from '../../common/SingletonModuleComp';

import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { PickBox } from '../../prefab/PickBox';
import { ItemEntity } from '../ItemEntity';

const { ccclass, property } = _decorator;

@ccclass('ItemSceneView')
@ecs.register('ItemSceneView')
export class ItemSceneViewComp extends CCComp {
    ent!: ItemEntity;
    ItemModel!: typeof this.ent.ItemModel;

    collider: Collider[] = []; // 所有的碰撞体，包括子节点的碰撞体
    otherColliderNode: Node[] = [];

    // 🌟 简化材质系统 - 直接使用共享材质
    private isGlowing: boolean = false; // 当前是否在发光

    @property({ type: AudioClip })
    // 选择音效
    pickEffect: AudioClip | null = null;
    @property({ type: AudioClip })
    // 闲置音效
    idleEffect: AudioClip | null = null;

    reset() {
        this.node.parent = null;
        this.node.destroy();
    }

    start() {
        this.initializeComponent();
        this.collider = this.node.getComponents(Collider);
        const comp = this.node.getComponent(RigidBody);
        if (comp) {
            comp.linearDamping = ClientConst.LinearDamping;
            comp.angularDamping = ClientConst.AngularDamping;
        }
        if (this.collider) {
            // 追加子节点的collider
            this.node.children.forEach(a => {
                const collider = a.getComponent(Collider);
                if (collider) {
                    this.collider.push(collider);
                }
            });
        }
    }

    /**
     * 🎯 组件初始化逻辑（可重复调用）
     */
    initializeComponent(): void {
        if (!this.ent || !this.ent.ItemModel) {
            oops.log.logError(`❌ [${this.node.name}] 无法初始化：ent或ItemModel仍未设置`);
            return;
        }

        this.ItemModel = this.ent.ItemModel;
        const meshRenderer = this.node.getComponent(MeshRenderer);
        if (meshRenderer) {
            this.ItemModel.meshRenderer = meshRenderer;
        }
        this.addRigidBody();
    }

    // 🌟 发光效果：创建材质实例（基于testSceneComp策略）
    private applyGlowEffect(): void {
        if (!this.ItemModel.meshRenderer || this.isGlowing) return;
        let interactionManager = smc.sceneMgr.getCurrentGameEntity().interactionManager;
        let fromMaterial = interactionManager.sharedMaterial;
        if (!fromMaterial) {
            oops.log.logError(`❌ [${this.node.name}] 无法初始化：fromMaterial 材质未设置`);
            return;
        }
        //目前只有到一个材质。
        let rimLightXrayPbrMaterial = interactionManager.rimLightXrayPbrMaterial;
        this.copyMaterialProperties(fromMaterial, rimLightXrayPbrMaterial);
        this.ItemModel.meshRenderer.setSharedMaterial(rimLightXrayPbrMaterial, 0);
        this.ItemModel.meshRenderer.setInstancedAttribute('a_instanced_rimStrength', [2.0]);
        this.isGlowing = true;
    }

    // 🎨 安全的材质属性复制方法 - 确保不破坏GPU Instancing合批
    private copyMaterialProperties(fromMaterial: Material, toMaterial: Material): void {
        try {
            // ✅ 复制纹理属性（只读取引用，不创建新实例）
            const normalMap = fromMaterial.getProperty('normalMap') as any;
            const pbrMap = fromMaterial.getProperty('pbrMap') as any;
            const albedoMap = fromMaterial.getProperty('albedoMap') as any;

            if (normalMap) toMaterial.setProperty('normalMap', normalMap);
            if (pbrMap) toMaterial.setProperty('pbrMap', pbrMap);
            if (albedoMap) toMaterial.setProperty('albedoMap', albedoMap);

            // 🎨 复制基础颜色和强度属性 - 保持物品原始外观
            const albedoColor = fromMaterial.getProperty('albedo') as any;
            const albedoScale = fromMaterial.getProperty('albedoScale') as any;
            const emissive = fromMaterial.getProperty('emissive') as any;
            const emissiveScale = fromMaterial.getProperty('emissiveScale') as any;
            const normalStrength = fromMaterial.getProperty('normalStrength') as any;

            // 🎯 复制PBR相关属性
            const metallicFactor = fromMaterial.getProperty('metallicFactor') as any;
            const roughnessFactor = fromMaterial.getProperty('roughnessFactor') as any;
            const occlusionStrength = fromMaterial.getProperty('occlusionStrength') as any;

            // 🌟 设置基础材质属性 - 保持物品原色
            if (albedoColor !== undefined) toMaterial.setProperty('albedo', albedoColor);
            if (albedoScale !== undefined) toMaterial.setProperty('albedoScale', albedoScale);
            if (emissive !== undefined) toMaterial.setProperty('emissive', emissive);
            if (emissiveScale !== undefined) toMaterial.setProperty('emissiveScale', emissiveScale);
            if (normalStrength !== undefined)
                toMaterial.setProperty('normalStrength', normalStrength);
            if (metallicFactor !== undefined)
                toMaterial.setProperty('metallicFactor', metallicFactor);
            if (roughnessFactor !== undefined)
                toMaterial.setProperty('roughnessFactor', roughnessFactor);
            if (occlusionStrength !== undefined)
                toMaterial.setProperty('occlusionStrength', occlusionStrength);
        } catch (error) {
            console.warn('⚠️ 材质属性复制失败:', error);
        }
    }

    // 🌟 移除发光效果：恢复到共享材质
    private removeGlowEffect(): void {
        if (!this.isGlowing || !this.ItemModel.meshRenderer) return;
        let interactionManager = smc.sceneMgr.getCurrentGameEntity().interactionManager;
        let sharedMaterial = interactionManager.sharedMaterial;
        this.ItemModel.meshRenderer.setSharedMaterial(sharedMaterial, 0);
        this.isGlowing = false;
    }

    setShadow(cast: boolean, receive: boolean) {
        if (this.ItemModel.meshRenderer) {
            this.ItemModel.meshRenderer.shadowCastingMode = cast ? 1 : 0;
            this.ItemModel.meshRenderer.receiveShadow = receive ? 1 : 0;
        }
    }

    doSkelAnimation() {
        this.node.getComponent(SkeletalAnimation)?.play();
    }

    getItemId() {
        return this.ItemModel.itemId;
    }

    //获取包围盒 只获取第一个mesh的
    getBoundingBox(): geometry.AABB | undefined {
        const meshRenderer = this.ItemModel.meshRenderer;
        if (meshRenderer) {
            return meshRenderer.model?.worldBounds!;
        }
    }

    doOnMove(pickBoxComp: PickBox) {
        this.ItemModel.pickBox = pickBoxComp;
        // 🎯 让PickBox也记录当前物品
        pickBoxComp.pickItem(this.node);
        if (this.ItemModel.rigidBody) {
            this.ItemModel.rigidBody.group = PHY_GROUP.ITEM_BOX;
        }
        this.onCancelTouch();
    }

    playEffectOnChoose() {
        oops.audio.playEffect(this.pickEffect || MusicConf.commonPickEffect);
    }

    set enableRigidBody(b: boolean) {
        if (this.ItemModel.rigidBody) {
            this.ItemModel.rigidBody.enabled = b;
            this.ItemModel.rigidBody.useGravity = b;
        }
        this.collider.map(a => (a.enabled = b));
    }

    changeRigidBodyType(type: physics.RigidBody.Type = physics.RigidBody.Type.STATIC) {
        if (this.ItemModel.rigidBody) {
            this.ItemModel.rigidBody.type = type;
        }
    }

    private addRigidBody() {
        // 🎯 安全检查：确保meshRenderer存在
        if (!this.ItemModel.meshRenderer) {
            oops.log.logError(`⚠️ [${this.node.name}] addRigidBody时meshRenderer为空`);

            return;
        }

        let rigid = this.node.getComponent(RigidBody);
        if (!rigid) {
            oops.log.logError(`⚠️ [${this.node.name}] addRigidBody时rigid为空`);
            return;
        }
        rigid.group = PHY_GROUP.ITEM;
        rigid.allowSleep = false;
        this.ItemModel.rigidBody = rigid;

        // 根据模型的aabb大小设置质量
        const aabb = this.getBoundingBox();
        if (aabb) {
            const halfExtents = aabb.halfExtents;
            const volume = 8 * halfExtents.x * halfExtents.y * halfExtents.z; // 体积 = 2 * hw * 2 * hh * 2 * hl
            rigid.mass = volume; // 假设密度为1
        } else {
            rigid.mass = 1; // 默认质量
        }

        return;
    }

    onTouch() {
        // 🎯 安全检查：确保ItemModel已正确初始化
        if (!this.ItemModel) {
            oops.log.logError(`❌ [${this.node.name}] onTouch时ItemModel未初始化`);
            return;
        }

        this.ItemModel.touching = true;
        this.doSkelAnimation();

        // 🌟 简化发光逻辑：直接应用预加载材质
        this.applyGlowEffect();

        this.collider.forEach(a => a.on('onCollisionStay', this.onTriggerEnter, this));
    }

    onCancelTouch() {
        // 🎯 安全检查：确保ItemModel已正确初始化
        if (!this.ItemModel) {
            oops.log.logWarn(`⚠️ [${this.node.name}] onCancelTouch时ItemModel未初始化`);
            return;
        }

        this.ItemModel.touching = false;

        // 🌟 简化材质恢复
        this.removeGlowEffect();

        this.node.getComponent(SkeletalAnimation)?.stop();
        this.otherColliderNode = [];
        this.collider.forEach(a => a.off('onCollisionStay', this.onTriggerEnter, this));
    }

    onTriggerEnter(event: ITriggerEvent) {
        let otherNode = event.otherCollider.node;
        const rigidBody = event.otherCollider.node.getComponent(RigidBody);
        if (
            this.node !== otherNode &&
            rigidBody?.group == PHY_GROUP.ITEM &&
            this.otherColliderNode.indexOf(otherNode) == -1
        ) {
            this.otherColliderNode.push(otherNode);

            // 计算从当前节点到其他节点的方向向量
            let thisPos = this.node.getWorldPosition();
            const direction = otherNode.getWorldPosition().subtract(thisPos).normalize();

            // 计算目标位置，远离当前节点
            const distance = 0.1; // 移动的距离，可以根据需要调整
            const targetPos = otherNode.getWorldPosition().add(direction.multiplyScalar(distance));

            // 使用 tween 动画移动其他节点
            tween(otherNode)
                .to(0.2, { position: targetPos }) // 0.5 秒内移动到目标位置
                .start();
        }
    }
}
