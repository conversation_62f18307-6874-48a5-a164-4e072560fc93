import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';
import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { PHY_GROUP } from '../../common/ClientConst';
import { smc } from '../../common/SingletonModuleComp';
import { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';

import { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';
import { GameEntity } from './GameEntity';

const { ccclass, property } = _decorator;

/**
 * 物品交互管理器 - 智能触摸组件版本
 * 负责触摸事件的捕获和处理，与类版本协同工作
 */
@ccclass('ItemInteractionManager')
export class ItemInteractionManager extends Component {
    private gameEntity: GameEntity | null = null;
    private currentTouchedItem: ItemSceneViewComp | null = null;

    // 🎯 防抖和状态管理
    private lastClickTime: number = 0;
    private clickDelay: number = 100; // 100ms防抖延迟
    private isProcessingClick: boolean = false;

    // 🚀 性能优化：触摸移动节流 - 增强版
    private lastMoveTime: number = 0;
    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)
    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };
    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测

    // 🎯 射线检测缓存优化
    private raycastCache: Map<string, { items: ItemSceneViewComp[]; timestamp: number }> =
        new Map();
    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算
    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };
    private raycastPositionThreshold: number = 8; // 射线检测位置阈值

    // 🎯 物品切换控制
    private currentBestItem: ItemSceneViewComp | null = null; // 当前最佳物品
    private itemSwitchDelay: number = 400; // 物品切换延迟400ms
    private lastItemSwitchTime: number = 0;
    private candidateItems: Array<{ item: ItemSceneViewComp; score: number }> = []; // 候选物品及评分
    private currentItemIndex: number = 0; // 当前物品索引

    // 🎯 慢速移动优化
    private slowMoveThreshold: number = 1; // 极小移动阈值1px
    private isInSlowMoveMode: boolean = false;
    private slowMoveStartTime: number = 0;
    private maxSlowMoveTime: number = 3000; // 3秒超时
    private slowMoveRequiredTime: number = 800; // 需要持续慢速移动800ms才触发层级切换

    // 🎯 简单性能监控
    private raycastCount: number = 0;
    private cacheHitCount: number = 0;
    private lastPerformanceReport: number = 0;

    onLoad() {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');
    }

    start() {
        // 在start中获取GameEntity，确保ent属性已经设置
        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);
        if (gameSceneViewComp && gameSceneViewComp.ent) {
            this.gameEntity = gameSceneViewComp.ent;
            oops.log.logBusiness(
                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`
            );
        } else {
            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');
        }
    }

    onDestroy() {
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);

        // 🧹 清理射线检测缓存
        this.raycastCache.clear();
        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');
    }

    /**
     * 🔍 检查是否可以处理点击
     */
    private canProcessClick(): boolean {
        const currentTime = Date.now();

        // 防抖检查
        if (currentTime - this.lastClickTime < this.clickDelay) {
            // console.log('🛡️ 点击过快，触发防抖保护');
            return false;
        }

        // 处理状态检查
        if (this.isProcessingClick) {
            // console.log('🛡️ 正在处理点击，跳过重复处理');
            return false;
        }

        return true;
    }

    /**
     * 🎯 智能物品选择逻辑
     */
    private intelligentItemSelection(item: ItemSceneViewComp): boolean {
        if (!this.gameEntity || !this.gameEntity.interactionManager) {
            console.warn('⚠️ GameEntity或InteractionManager未准备好');
            return false;
        }

        const itemNode = item.node;
        const interactionManager = this.gameEntity.interactionManager;

        // 🔍 检查物品是否在收集槽管理器中
        if (interactionManager.slotManager) {
            const slot = interactionManager.slotManager.getSlotByItem(itemNode);

            if (slot) {
                // 物品在收集槽中
                if (slot.index >= 7) {
                    // 在额外槽位(7-9)，可以选中移动到主槽位
                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);
                    return true;
                } else {
                    // 在主槽位(0-6)，不能选中
                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);
                    return false;
                }
            }
        }

        // 🔍 检查物品是否在场景中可选择
        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());
        if (!isSelectable) {
            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);
            return false;
        }

        // 🔍 检查收集槽是否已满
        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {
            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);
            return false;
        }

        return true;
    }

    /**
     * 🚀 简化射线检测 - 专注解决慢速移动问题
     */
    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {
        if (!this.gameEntity) {
            return null;
        }

        const camera = smc.camera.CameraModel.camera;
        if (!camera) {
            return null;
        }

        const currentX = event.getLocationX();
        const currentY = event.getLocationY();

        // 🎯 计算移动距离
        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);
        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // 🎯 总是执行射线检测，获取当前位置的所有物品
        const allItems = this.performSimpleMultiLayerRaycast(currentX, currentY);

        // 更新位置记录
        this.lastRaycastPosition.x = currentX;
        this.lastRaycastPosition.y = currentY;

        // 🎯 如果没有检测到物品，返回null
        if (allItems.length === 0) {
            this.exitSlowMoveMode();
            return null;
        }

        // 🎯 如果只有一个物品，直接返回
        if (allItems.length === 1) {
            this.exitSlowMoveMode();
            return allItems[0];
        }

        // 🎯 多个物品时，处理层级切换
        return this.handleSimpleLayerSwitching(allItems, distance);
    }

    /**
     * 🎯 简化多层射线检测 - 只使用单点射线，逐层检测
     */
    private performSimpleMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {
        const camera = smc.camera.CameraModel.camera;
        const ray = camera.screenPointToRay(x, y);
        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;

        const allItems: ItemSceneViewComp[] = [];
        const maxLayers = 3; // 最多检测3层
        const disabledColliders: any[] = [];

        try {
            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (let layer = 0; layer < maxLayers; layer++) {
                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                    const result = PhysicsSystem.instance.raycastClosestResult;
                    const hitNode = result.collider.node;
                    const itemComp = hitNode.getComponent(ItemSceneViewComp);

                    if (itemComp && itemComp.ItemModel) {
                        // 避免重复添加同一个物品
                        if (!allItems.find(item => item.node === hitNode)) {
                            allItems.push(itemComp);
                        }

                        // 临时禁用这个碰撞体，以便检测下一层
                        const collider = result.collider;
                        if (collider.enabled) {
                            collider.enabled = false;
                            disabledColliders.push(collider);
                        }
                    } else {
                        break; // 没有找到有效物品，停止检测
                    }
                } else {
                    break; // 没有更多碰撞，停止检测
                }
            }
        } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
                if (collider && collider.isValid) {
                    collider.enabled = true;
                }
            });
        }

        return allItems;
    }

    /**
     * 🎯 改进的层级切换 - 需要持续慢速移动才触发
     */
    private handleSimpleLayerSwitching(
        items: ItemSceneViewComp[],
        moveDistance: number
    ): ItemSceneViewComp | null {
        if (items.length <= 1) {
            return items[0] || null;
        }

        const now = Date.now();
        const isSlowMove = moveDistance < this.slowMoveThreshold;

        // 🎯 如果不是慢速移动，直接返回顶层物品
        if (!isSlowMove) {
            this.exitSlowMoveMode();
            return items[0];
        }

        // 🎯 开始记录慢速移动时间
        if (!this.isInSlowMoveMode) {
            this.isInSlowMoveMode = true;
            this.slowMoveStartTime = now;
            this.currentItemIndex = 0;
            this.candidateItems = items.map(item => ({ item, score: 100 }));
            // 不立即开始切换，先返回顶层物品
            return items[0];
        }

        // 🎯 检查是否持续慢速移动足够长时间
        const slowMoveDuration = now - this.slowMoveStartTime;
        if (slowMoveDuration < this.slowMoveRequiredTime) {
            // 还没有持续足够长时间，继续返回顶层物品
            return items[0];
        }

        // 🎯 现在开始层级切换
        if (slowMoveDuration >= this.slowMoveRequiredTime) {
            // 第一次进入切换模式
            if (this.lastItemSwitchTime === 0) {
                this.lastItemSwitchTime = now;
                console.log(
                    `🎯 持续慢速移动${this.slowMoveRequiredTime}ms，开始层级切换 (共${items.length}层)`
                );
            }

            // 检查是否应该切换到下一层
            if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {
                this.currentItemIndex = (this.currentItemIndex + 1) % items.length;
                this.lastItemSwitchTime = now;

                console.log(
                    `🔄 切换到第${this.currentItemIndex + 1}层: ${items[this.currentItemIndex].node?.name || '未知'}`
                );
            }
        }

        // 🎯 检查是否超时
        if (slowMoveDuration > this.maxSlowMoveTime) {
            this.exitSlowMoveMode();
            return items[0]; // 返回顶层物品
        }

        return items[this.currentItemIndex] || items[0];
    }

    /**
     * 🎯 合并和评分物品
     */
    private mergeAndScoreItems(
        raycastItems: ItemSceneViewComp[],
        screenItems: ItemSceneViewComp[],
        touchX: number,
        touchY: number
    ): Array<{ item: ItemSceneViewComp; score: number }> {
        const candidates = new Map<string, { item: ItemSceneViewComp; score: number }>();
        const camera = smc.camera.CameraModel.camera;

        // 处理射线检测结果（高优先级）
        for (const item of raycastItems) {
            const worldPos = item.node.worldPosition;
            const screenPos = camera.worldToScreen(worldPos);
            const distance = Math.sqrt(
                Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)
            );

            candidates.set(item.node.uuid, {
                item,
                score: 100 - distance * 0.5, // 射线检测基础分100，距离越近分数越高
            });
        }

        // 处理屏幕距离检测结果（低优先级）
        for (const item of screenItems) {
            if (!candidates.has(item.node.uuid)) {
                const worldPos = item.node.worldPosition;
                const screenPos = camera.worldToScreen(worldPos);
                const distance = Math.sqrt(
                    Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)
                );

                candidates.set(item.node.uuid, {
                    item,
                    score: 50 - distance * 0.3, // 屏幕距离基础分50
                });
            }
        }

        return Array.from(candidates.values());
    }

    /**
     * 🎯 选择最佳物品
     */
    private selectBestItem(
        candidates: Array<{ item: ItemSceneViewComp; score: number }>
    ): ItemSceneViewComp | null {
        if (candidates.length === 0) {
            return null;
        }

        // 按分数排序，选择最高分的物品
        candidates.sort((a, b) => b.score - a.score);

        const bestCandidate = candidates[0];

        // 如果有多个候选物品，可以在这里实现慢速移动时的切换逻辑
        if (candidates.length > 1) {
            return this.handleMultipleCandidates(candidates);
        }

        return bestCandidate.item;
    }

    /**
     * 🎯 处理多个候选物品
     */
    private handleMultipleCandidates(
        candidates: Array<{ item: ItemSceneViewComp; score: number }>
    ): ItemSceneViewComp | null {
        const now = Date.now();

        // 检查是否为慢速移动模式
        if (!this.isInSlowMoveMode) {
            this.isInSlowMoveMode = true;
            this.slowMoveStartTime = now;
            this.candidateItems = candidates;
            this.currentItemIndex = 0;
            console.log(`🎯 检测到${candidates.length}个候选物品，进入慢速选择模式`);
        }

        // 检查是否超时
        if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {
            this.exitSlowMoveMode();
            return candidates[0].item; // 返回最高分物品
        }

        // 检查是否应该切换
        if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {
            this.currentItemIndex = (this.currentItemIndex + 1) % candidates.length;
            this.lastItemSwitchTime = now;

            const currentItem = candidates[this.currentItemIndex];
            console.log(
                `🔄 切换到候选物品: ${currentItem.item.node?.name || '未知'} (${this.currentItemIndex + 1}/${candidates.length})`
            );
        }

        return candidates[this.currentItemIndex]?.item || candidates[0].item;
    }

    /**
     * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品
     */
    private performMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {
        const startTime = performance.now();
        const camera = smc.camera.CameraModel.camera;
        const ray = camera.screenPointToRay(x, y);
        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;

        const allItems: ItemSceneViewComp[] = [];
        const maxLayers = 3; // 最多检测3层，平衡性能和功能
        const disabledColliders: any[] = [];

        try {
            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (let layer = 0; layer < maxLayers; layer++) {
                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                    const result = PhysicsSystem.instance.raycastClosestResult;
                    const hitNode = result.collider.node;
                    const itemComp = hitNode.getComponent(ItemSceneViewComp);

                    if (itemComp && itemComp.ItemModel) {
                        // 避免重复添加同一个物品
                        if (!allItems.find(item => item.node === hitNode)) {
                            allItems.push(itemComp);
                        }

                        // 临时禁用这个碰撞体，以便检测下一层
                        const collider = result.collider;
                        if (collider.enabled) {
                            collider.enabled = false;
                            disabledColliders.push(collider);
                        }
                    } else {
                        break; // 没有找到有效物品，停止检测
                    }
                } else {
                    break; // 没有更多碰撞，停止检测
                }
            }
        } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
                if (collider && collider.isValid) {
                    collider.enabled = true;
                }
            });
        }

        // 🎯 记录性能数据
        const endTime = performance.now();
        const duration = endTime - startTime;

        if (duration > 3) {
            // 多层检测阈值稍高
            console.warn(
                `⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`
            );
        }

        return allItems;
    }

    /**
     * 🎯 退出慢速移动模式
     */
    private exitSlowMoveMode(): void {
        if (this.isInSlowMoveMode) {
            this.isInSlowMoveMode = false;
            this.currentItemIndex = 0;
            this.candidateItems = [];
            this.lastItemSwitchTime = 0; // 重置切换时间
            console.log('🏁 退出慢速移动模式，回到顶层');
        }
    }

    /**
     * 🎯 暂停物品切换 - 选择物品后暂停一段时间
     */
    private pauseLayerSwitching(): void {
        this.exitSlowMoveMode();
        this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒
        console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);
    }

    /**
     * 🎯 清理过期缓存
     */
    private cleanupCache(): void {
        if (this.raycastCache.size > 50) {
            const now = Date.now();
            const keysToDelete: string[] = [];

            this.raycastCache.forEach((value, key) => {
                if (now - value.timestamp > this.raycastCacheTimeout * 2) {
                    keysToDelete.push(key);
                }
            });

            keysToDelete.forEach(key => this.raycastCache.delete(key));
        }
    }

    /**
     * 🎯 性能报告（如果需要）
     */
    private reportPerformanceIfNeeded(): void {
        const now = Date.now();
        if (now - this.lastPerformanceReport > 10000) {
            // 每10秒报告一次
            const cacheHitRate =
                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;

            if (this.raycastCount > 0) {
                console.log(
                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`
                );

                if (cacheHitRate < 30) {
                    console.warn(
                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`
                    );
                }
            }

            this.lastPerformanceReport = now;
        }
    }

    /**
     * 应用触摸效果到物品
     */
    private applyTouchEffect(itemComp: ItemSceneViewComp): void {
        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = true;
            if (typeof itemComp.onTouch === 'function') {
                itemComp.onTouch();
            }
            // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);
        }
    }

    /**
     * 取消物品的触摸效果
     */
    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {
        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = false;
            if (typeof itemComp.onCancelTouch === 'function') {
                itemComp.onCancelTouch();
            }
            // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);
        }
    }

    /**
     * 处理触摸开始事件
     */
    private onTouchStart(event: EventTouch) {
        // 🚀 初始化移动位置缓存
        this.lastMovePosition.x = event.getLocationX();
        this.lastMovePosition.y = event.getLocationY();
        this.lastMoveTime = Date.now();

        // 🎯 触摸开始时重置慢速移动模式
        this.exitSlowMoveMode();

        // 高频交互日志注释掉，减少日志噪音
        // oops.log.logTrace('🎯 触摸开始');

        const hitItem = this.detectItemAtPosition(event);
        if (hitItem) {
            this.currentTouchedItem = hitItem;
            this.applyTouchEffect(hitItem);
        } else {
            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');
        }
    }

    /**
     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测
     */
    private onTouchMove(event: EventTouch) {
        const currentTime = Date.now();
        const currentX = event.getLocationX();
        const currentY = event.getLocationY();

        // 🎯 时间节流：限制检测频率到30FPS
        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {
            return;
        }

        // 🎯 距离阈值：只有移动足够距离才触发检测
        const deltaX = Math.abs(currentX - this.lastMovePosition.x);
        const deltaY = Math.abs(currentY - this.lastMovePosition.y);
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        if (distance < this.moveDistanceThreshold) {
            return;
        }

        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率
        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {
            // 更新位置但不执行射线检测，减少性能消耗
            this.lastMoveTime = currentTime;
            this.lastMovePosition.x = currentX;
            this.lastMovePosition.y = currentY;
            return;
        }

        // 更新缓存
        this.lastMoveTime = currentTime;
        this.lastMovePosition.x = currentX;
        this.lastMovePosition.y = currentY;

        // 🎯 执行射线检测（现在频率大大降低）
        const hitItem = this.detectItemAtPosition(event);

        // 如果当前触摸的物品和检测到的物品不同，需要切换
        if (this.currentTouchedItem !== hitItem) {
            // 取消之前物品的触摸效果
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);
            }

            // 应用新物品的触摸效果
            if (hitItem) {
                this.applyTouchEffect(hitItem);
                // 高频交互日志注释掉，减少日志噪音
                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);
            }

            this.currentTouchedItem = hitItem;
        }
    }

    /**
     * 处理触摸结束事件 - 智能版本
     */
    private onTouchEnd(event: EventTouch) {
        // 🛡️ 防抖和状态检查
        if (!this.canProcessClick()) {
            // 清除触摸状态但不处理选择
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);
            }
            this.currentTouchedItem = null;
            return;
        }

        // 只有在物品上松手才算选中
        const hitItem = this.detectItemAtPosition(event);

        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {
            // 在同一个物品上松手，检查是否可以选中
            this.cancelTouchEffect(this.currentTouchedItem);

            oops.log.logBusiness(
                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`
            );

            // 🎯 智能选择检查
            if (this.intelligentItemSelection(this.currentTouchedItem)) {
                // 设置处理状态
                this.isProcessingClick = true;
                this.lastClickTime = Date.now();

                // 触发选择逻辑
                if (this.gameEntity && this.gameEntity.interactionManager) {
                    try {
                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);
                        oops.log.logBusiness(
                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`
                        );

                        // 🎯 选择物品后暂停层级切换
                        this.pauseLayerSwitching();
                    } catch (error) {
                        console.error('❌ 物品选择过程中发生错误:', error);
                    }
                }

                // 延迟重置处理状态
                setTimeout(() => {
                    this.isProcessingClick = false;
                }, this.clickDelay);
            } else {
            }
        } else {
            // 不在原物品上松手，取消选择
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);
            } else {
                // oops.log.logTrace('❌ 在空白区域松手，无选择');
            }
        }

        // 清除当前触摸状态
        this.currentTouchedItem = null;
    }
}
