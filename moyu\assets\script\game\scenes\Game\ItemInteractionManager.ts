import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';
import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { PHY_GROUP } from '../../common/ClientConst';
import { smc } from '../../common/SingletonModuleComp';
import { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';

import { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';
import { GameEntity } from './GameEntity';

const { ccclass, property } = _decorator;

/**
 * 物品交互管理器 - 智能触摸组件版本
 * 负责触摸事件的捕获和处理，与类版本协同工作
 */
@ccclass('ItemInteractionManager')
export class ItemInteractionManager extends Component {
    private gameEntity: GameEntity | null = null;
    private currentTouchedItem: ItemSceneViewComp | null = null;

    // 🎯 防抖和状态管理
    private lastClickTime: number = 0;
    private clickDelay: number = 100; // 100ms防抖延迟
    private isProcessingClick: boolean = false;

    // 🚀 性能优化：触摸移动节流 - 增强版
    private lastMoveTime: number = 0;
    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)
    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };
    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测

    // 🎯 射线检测缓存优化
    private raycastCache: Map<string, { item: ItemSceneViewComp | null; timestamp: number }> =
        new Map();
    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算
    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };
    private raycastPositionThreshold: number = 8; // 射线检测位置阈值

    // 🎯 简单性能监控
    private raycastCount: number = 0;
    private cacheHitCount: number = 0;
    private lastPerformanceReport: number = 0;

    onLoad() {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');
    }

    start() {
        // 在start中获取GameEntity，确保ent属性已经设置
        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);
        if (gameSceneViewComp && gameSceneViewComp.ent) {
            this.gameEntity = gameSceneViewComp.ent;
            oops.log.logBusiness(
                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`
            );
        } else {
            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');
        }
    }

    onDestroy() {
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);

        // 🧹 清理射线检测缓存
        this.raycastCache.clear();
        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');
    }

    /**
     * 🔍 检查是否可以处理点击
     */
    private canProcessClick(): boolean {
        const currentTime = Date.now();

        // 防抖检查
        if (currentTime - this.lastClickTime < this.clickDelay) {
            // console.log('🛡️ 点击过快，触发防抖保护');
            return false;
        }

        // 处理状态检查
        if (this.isProcessingClick) {
            // console.log('🛡️ 正在处理点击，跳过重复处理');
            return false;
        }

        return true;
    }

    /**
     * 🎯 智能物品选择逻辑
     */
    private intelligentItemSelection(item: ItemSceneViewComp): boolean {
        if (!this.gameEntity || !this.gameEntity.interactionManager) {
            console.warn('⚠️ GameEntity或InteractionManager未准备好');
            return false;
        }

        const itemNode = item.node;
        const interactionManager = this.gameEntity.interactionManager;

        // 🔍 检查物品是否在收集槽管理器中
        if (interactionManager.slotManager) {
            const slot = interactionManager.slotManager.getSlotByItem(itemNode);

            if (slot) {
                // 物品在收集槽中
                if (slot.index >= 7) {
                    // 在额外槽位(7-9)，可以选中移动到主槽位
                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);
                    return true;
                } else {
                    // 在主槽位(0-6)，不能选中
                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);
                    return false;
                }
            }
        }

        // 🔍 检查物品是否在场景中可选择
        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());
        if (!isSelectable) {
            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);
            return false;
        }

        // 🔍 检查收集槽是否已满
        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {
            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);
            return false;
        }

        return true;
    }

    /**
     * 🚀 优化版射线检测 - 带缓存和智能跳过
     */
    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {
        if (!this.gameEntity) {
            return null;
        }

        const camera = smc.camera.CameraModel.camera;
        if (!camera) {
            return null;
        }

        const currentX = event.getLocationX();
        const currentY = event.getLocationY();

        // 🎯 检查位置是否变化足够大，避免无意义的重复检测
        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);
        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);

        if (deltaX < this.raycastPositionThreshold && deltaY < this.raycastPositionThreshold) {
            // 位置变化很小，使用缓存结果
            const cacheKey = `${Math.floor(currentX / this.raycastPositionThreshold)}_${Math.floor(currentY / this.raycastPositionThreshold)}`;
            const cached = this.raycastCache.get(cacheKey);

            if (cached && Date.now() - cached.timestamp < this.raycastCacheTimeout) {
                this.cacheHitCount++;
                this.reportPerformanceIfNeeded();
                return cached.item;
            }
        }

        // 🎯 执行射线检测（带性能监控）
        const startTime = performance.now();
        const ray = camera.screenPointToRay(currentX, currentY);
        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;

        let result: ItemSceneViewComp | null = null;

        if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
            const raycastResult = PhysicsSystem.instance.raycastClosestResult;
            const hitNode = raycastResult.collider.node;
            const itemSceneViewComp = hitNode.getComponent(ItemSceneViewComp);

            if (itemSceneViewComp && itemSceneViewComp.ItemModel) {
                result = itemSceneViewComp;
            }
        }

        // 🎯 记录性能数据
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.raycastCount++;

        if (duration > 2) {
            // 超过2ms记录警告
            console.warn(`⚠️ 射线检测耗时过长: ${duration.toFixed(2)}ms`);
        }

        // 🎯 更新缓存
        const cacheKey = `${Math.floor(currentX / this.raycastPositionThreshold)}_${Math.floor(currentY / this.raycastPositionThreshold)}`;
        this.raycastCache.set(cacheKey, { item: result, timestamp: Date.now() });

        // 🎯 清理过期缓存（防止内存泄漏）
        if (this.raycastCache.size > 50) {
            const now = Date.now();
            const keysToDelete: string[] = [];

            this.raycastCache.forEach((value, key) => {
                if (now - value.timestamp > this.raycastCacheTimeout * 2) {
                    keysToDelete.push(key);
                }
            });

            keysToDelete.forEach(key => this.raycastCache.delete(key));
        }

        // 更新最后检测位置
        this.lastRaycastPosition.x = currentX;
        this.lastRaycastPosition.y = currentY;

        return result;
    }

    /**
     * 🎯 性能报告（如果需要）
     */
    private reportPerformanceIfNeeded(): void {
        const now = Date.now();
        if (now - this.lastPerformanceReport > 10000) {
            // 每10秒报告一次
            const cacheHitRate =
                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;

            if (this.raycastCount > 0) {
                console.log(
                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`
                );

                if (cacheHitRate < 30) {
                    console.warn(
                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`
                    );
                }
            }

            this.lastPerformanceReport = now;
        }
    }

    /**
     * 应用触摸效果到物品
     */
    private applyTouchEffect(itemComp: ItemSceneViewComp): void {
        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = true;
            if (typeof itemComp.onTouch === 'function') {
                itemComp.onTouch();
            }
            // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);
        }
    }

    /**
     * 取消物品的触摸效果
     */
    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {
        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = false;
            if (typeof itemComp.onCancelTouch === 'function') {
                itemComp.onCancelTouch();
            }
            // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);
        }
    }

    /**
     * 处理触摸开始事件
     */
    private onTouchStart(event: EventTouch) {
        // 🚀 初始化移动位置缓存
        this.lastMovePosition.x = event.getLocationX();
        this.lastMovePosition.y = event.getLocationY();
        this.lastMoveTime = Date.now();

        // 高频交互日志注释掉，减少日志噪音
        // oops.log.logTrace('🎯 触摸开始');

        const hitItem = this.detectItemAtPosition(event);
        if (hitItem) {
            this.currentTouchedItem = hitItem;
            this.applyTouchEffect(hitItem);
        } else {
            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');
        }
    }

    /**
     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测
     */
    private onTouchMove(event: EventTouch) {
        const currentTime = Date.now();
        const currentX = event.getLocationX();
        const currentY = event.getLocationY();

        // 🎯 时间节流：限制检测频率到30FPS
        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {
            return;
        }

        // 🎯 距离阈值：只有移动足够距离才触发检测
        const deltaX = Math.abs(currentX - this.lastMovePosition.x);
        const deltaY = Math.abs(currentY - this.lastMovePosition.y);
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        if (distance < this.moveDistanceThreshold) {
            return;
        }

        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率
        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {
            // 更新位置但不执行射线检测，减少性能消耗
            this.lastMoveTime = currentTime;
            this.lastMovePosition.x = currentX;
            this.lastMovePosition.y = currentY;
            return;
        }

        // 更新缓存
        this.lastMoveTime = currentTime;
        this.lastMovePosition.x = currentX;
        this.lastMovePosition.y = currentY;

        // 🎯 执行射线检测（现在频率大大降低）
        const hitItem = this.detectItemAtPosition(event);

        // 如果当前触摸的物品和检测到的物品不同，需要切换
        if (this.currentTouchedItem !== hitItem) {
            // 取消之前物品的触摸效果
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);
            }

            // 应用新物品的触摸效果
            if (hitItem) {
                this.applyTouchEffect(hitItem);
                // 高频交互日志注释掉，减少日志噪音
                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);
            }

            this.currentTouchedItem = hitItem;
        }
    }

    /**
     * 处理触摸结束事件 - 智能版本
     */
    private onTouchEnd(event: EventTouch) {
        // 🛡️ 防抖和状态检查
        if (!this.canProcessClick()) {
            // 清除触摸状态但不处理选择
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);
            }
            this.currentTouchedItem = null;
            return;
        }

        // 只有在物品上松手才算选中
        const hitItem = this.detectItemAtPosition(event);

        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {
            // 在同一个物品上松手，检查是否可以选中
            this.cancelTouchEffect(this.currentTouchedItem);

            oops.log.logBusiness(
                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`
            );

            // 🎯 智能选择检查
            if (this.intelligentItemSelection(this.currentTouchedItem)) {
                // 设置处理状态
                this.isProcessingClick = true;
                this.lastClickTime = Date.now();

                // 触发选择逻辑
                if (this.gameEntity && this.gameEntity.interactionManager) {
                    try {
                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);
                        oops.log.logBusiness(
                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`
                        );
                    } catch (error) {
                        console.error('❌ 物品选择过程中发生错误:', error);
                    }
                }

                // 延迟重置处理状态
                setTimeout(() => {
                    this.isProcessingClick = false;
                }, this.clickDelay);
            } else {
            }
        } else {
            // 不在原物品上松手，取消选择
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);
            } else {
                // oops.log.logTrace('❌ 在空白区域松手，无选择');
            }
        }

        // 清除当前触摸状态
        this.currentTouchedItem = null;
    }
}
