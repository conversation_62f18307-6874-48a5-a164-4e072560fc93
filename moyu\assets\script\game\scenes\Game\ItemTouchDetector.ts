import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';
import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { PHY_GROUP } from '../../common/ClientConst';
import { smc } from '../../common/SingletonModuleComp';
import { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';
import { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';
import { GameEntity } from './GameEntity';

const { ccclass, property } = _decorator;

/**
 * 物品触摸检测器 - 智能射线检测组件
 * 负责触摸事件的捕获和射线检测，与ItemInteractionManager协同工作
 */
@ccclass('ItemTouchDetector')
export class ItemTouchDetector extends Component {
    private gameEntity: GameEntity | null = null;
    private currentTouchedItem: ItemSceneViewComp | null = null;

    // 🎯 防抖和状态管理
    private lastClickTime: number = 0;
    private clickDebounceDelay: number = 200; // 200ms防抖
    private isProcessingClick: boolean = false;

    // 🎯 移动检测优化
    private lastMoveTime: number = 0;
    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };
    private moveThrottleDelay: number = 33; // 30FPS节流，约33ms
    private moveDistanceThreshold: number = 10; // 移动10px以上才触发检测

    // 🎯 性能优化配置
    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };
    private lastRaycastTime: number = 0;
    private raycastThrottleDelay: number = 16; // 60FPS节流，约16ms
    private moveDistanceThreshold: number = 5; // 移动5px以上才重新检测
    private lastDetectedItem: ItemSceneViewComp | null = null;
    
    // 🎯 长按优化
    private isLongPressing: boolean = false;
    private longPressStartTime: number = 0;
    private longPressThreshold: number = 500; // 500ms算长按
    private longPressRaycastDelay: number = 33; // 长按时30FPS检测

    onLoad() {
        // 注册触摸事件监听
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);

        oops.log.logBusiness('🎯 ItemTouchDetector已注册触摸事件监听');
    }

    onDestroy() {
        // 注销事件监听
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);

        oops.log.logBusiness('🧹 ItemTouchDetector已注销事件监听');
    }

    /**
     * 设置游戏实体引用
     */
    setGameEntity(gameEntity: GameEntity) {
        this.gameEntity = gameEntity;
    }

    /**
     * 🚀 整合优化的射线检测 - 高性能精确检测
     */
    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {
        if (!this.gameEntity) {
            return null;
        }

        const currentX = event.getLocationX();
        const currentY = event.getLocationY();
        const now = Date.now();

        // 🎯 计算移动距离
        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);
        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // 🎯 性能优化：移动距离小且时间间隔短，返回上次结果
        if (distance < this.moveDistanceThreshold && 
            now - this.lastRaycastTime < this.raycastThrottleDelay &&
            this.lastDetectedItem) {
            return this.lastDetectedItem;
        }

        // 🎯 长按检测优化
        const raycastDelay = this.isLongPressing ? this.longPressRaycastDelay : this.raycastThrottleDelay;
        if (now - this.lastRaycastTime < raycastDelay) {
            return this.lastDetectedItem;
        }

        // 🎯 执行射线检测
        const detectedItem = this.performOptimizedRaycast(currentX, currentY);

        // 🎯 更新缓存
        this.lastRaycastPosition.x = currentX;
        this.lastRaycastPosition.y = currentY;
        this.lastRaycastTime = now;
        this.lastDetectedItem = detectedItem;

        return detectedItem;
    }
    
    /**
     * 🎯 优化的射线检测 - 只检测最前面的可见物品
     */
    private performOptimizedRaycast(x: number, y: number): ItemSceneViewComp | null {
        const camera = smc.camera.CameraModel.camera;
        if (!camera) {
            return null;
        }

        const ray = camera.screenPointToRay(x, y);
        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;

        // 🎯 执行精确射线检测 - 只检测最前面的可见物品
        if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
            const result = PhysicsSystem.instance.raycastClosestResult;
            const hitNode = result.collider.node;
            const itemComp = hitNode.getComponent(ItemSceneViewComp);

            if (itemComp && itemComp.ItemModel) {
                return itemComp;
            }
        }

        return null;
    }

    /**
     * 处理触摸开始事件 - 添加长按检测
     */
    private onTouchStart(event: EventTouch) {
        const now = Date.now();
        
        // 🚀 初始化移动位置缓存
        this.lastMovePosition.x = event.getLocationX();
        this.lastMovePosition.y = event.getLocationY();
        this.lastMoveTime = now;

        // 🎯 初始化长按检测
        this.isLongPressing = false;
        this.longPressStartTime = now;

        const hitItem = this.detectItemAtPosition(event);
        if (hitItem) {
            this.currentTouchedItem = hitItem;
            this.applyTouchEffect(hitItem);
        }
    }

    /**
     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测
     */
    private onTouchMove(event: EventTouch) {
        const currentTime = Date.now();
        const currentX = event.getLocationX();
        const currentY = event.getLocationY();

        // 🎯 时间节流：限制检测频率到30FPS
        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {
            return;
        }

        // 🎯 距离阈值：只有移动足够距离才触发检测
        const deltaX = Math.abs(currentX - this.lastMovePosition.x);
        const deltaY = Math.abs(currentY - this.lastMovePosition.y);
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        if (distance < this.moveDistanceThreshold) {
            return;
        }

        // 🎯 检测是否为长按
        if (!this.isLongPressing && 
            currentTime - this.longPressStartTime > this.longPressThreshold) {
            this.isLongPressing = true;
            console.log('🔒 检测到长按，启用优化模式');
        }

        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率
        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {
            // 更新位置但不执行射线检测，减少性能消耗
            this.lastMoveTime = currentTime;
            this.lastMovePosition.x = currentX;
            this.lastMovePosition.y = currentY;
            return;
        }

        // 更新缓存
        this.lastMoveTime = currentTime;
        this.lastMovePosition.x = currentX;
        this.lastMovePosition.y = currentY;

        // 🎯 执行射线检测（现在频率大大降低）
        const hitItem = this.detectItemAtPosition(event);

        // 如果当前触摸的物品和检测到的物品不同，需要切换
        if (this.currentTouchedItem !== hitItem) {
            // 取消之前物品的触摸效果
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);
            }

            // 应用新物品的触摸效果
            if (hitItem) {
                this.applyTouchEffect(hitItem);
            }

            this.currentTouchedItem = hitItem;
        }
    }

    /**
     * 处理触摸结束事件 - 智能版本
     */
    private onTouchEnd(event: EventTouch) {
        // 重置长按状态
        this.isLongPressing = false;

        // 防抖处理
        const currentTime = Date.now();
        if (currentTime - this.lastClickTime < this.clickDebounceDelay || this.isProcessingClick) {
            return;
        }

        this.lastClickTime = currentTime;
        this.isProcessingClick = true;

        try {
            // 取消当前物品的触摸效果
            if (this.currentTouchedItem) {
                this.cancelTouchEffect(this.currentTouchedItem);

                // 触发选择逻辑
                if (this.gameEntity && this.gameEntity.interactionManager) {
                    this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);
                    oops.log.logBusiness(
                        `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`
                    );
                }
            }
        } catch (error) {
            console.error('❌ 物品选择过程中发生错误:', error);
        } finally {
            this.currentTouchedItem = null;
            this.isProcessingClick = false;
        }
    }

    /**
     * 应用触摸效果
     */
    private applyTouchEffect(item: ItemSceneViewComp) {
        // 这里可以添加触摸效果，比如高亮、缩放等
        // 暂时留空，可以根据需要实现
    }

    /**
     * 取消触摸效果
     */
    private cancelTouchEffect(item: ItemSceneViewComp) {
        // 这里可以添加取消触摸效果的逻辑
        // 暂时留空，可以根据需要实现
    }
}
