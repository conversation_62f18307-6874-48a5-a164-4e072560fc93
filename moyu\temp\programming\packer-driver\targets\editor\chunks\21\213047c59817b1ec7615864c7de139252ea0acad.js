System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, _dec, _class, _class2, _crd, ccclass, MultiLayerRaycastConfig;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2eda5W+A5tE+oGTMdtACw5w", "MultiLayerRaycastConfig", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);
      /**
       * 🎯 多层射线检测配置
       * 专为堆叠模型场景优化的配置参数
       */

      _export("MultiLayerRaycastConfig", MultiLayerRaycastConfig = (_dec = ccclass('MultiLayerRaycastConfig'), _dec(_class = (_class2 = class MultiLayerRaycastConfig {
        /**
         * 🎯 根据设备性能调整配置
         */
        static getOptimizedConfig(devicePerformance = 'medium') {
          const baseConfig = { ...this.PERFORMANCE,
            ...this.MULTI_LAYER,
            ...this.STACKED_SCENE,
            ...this.DEBUG
          };

          switch (devicePerformance) {
            case 'low':
              return { ...baseConfig,
                TOUCH_THROTTLE_DELAY: 50,
                // 20FPS
                MAX_LAYERS: 2,
                // 减少层数
                CACHE_TIMEOUT: 150,
                // 增加缓存时间
                PERFORMANCE_WARNING_THRESHOLD: 5,
                ENABLE_PERFORMANCE_MONITORING: false // 关闭监控减少开销

              };

            case 'high':
              return { ...baseConfig,
                TOUCH_THROTTLE_DELAY: 16,
                // 60FPS
                MAX_LAYERS: 5,
                // 增加层数
                CACHE_TIMEOUT: 50,
                // 减少缓存时间提高精度
                PERFORMANCE_WARNING_THRESHOLD: 2,
                SLOW_MOVE_THRESHOLD: 2,
                // 更敏感的慢速检测
                LAYER_SWITCH_DELAY: 150 // 更快的切换

              };

            default:
              // medium
              return baseConfig;
          }
        }
        /**
         * 🎯 针对不同分辨率的优化配置
         */


        static getConfigForResolution(width, height) {
          const config = this.getOptimizedConfig(); // 高分辨率设备调整阈值

          if (width > 1080 || height > 1920) {
            return { ...config,
              MOVE_DISTANCE_THRESHOLD: config.MOVE_DISTANCE_THRESHOLD * 1.5,
              RAYCAST_POSITION_THRESHOLD: config.RAYCAST_POSITION_THRESHOLD * 1.5,
              SLOW_MOVE_THRESHOLD: config.SLOW_MOVE_THRESHOLD * 1.5
            };
          } // 低分辨率设备调整阈值


          if (width < 720 || height < 1280) {
            return { ...config,
              MOVE_DISTANCE_THRESHOLD: config.MOVE_DISTANCE_THRESHOLD * 0.7,
              RAYCAST_POSITION_THRESHOLD: config.RAYCAST_POSITION_THRESHOLD * 0.7,
              SLOW_MOVE_THRESHOLD: config.SLOW_MOVE_THRESHOLD * 0.7
            };
          }

          return config;
        }
        /**
         * 🎯 获取推荐配置 - 根据当前设备自动选择
         */


        static getRecommendedConfig() {
          // 简单的设备性能检测
          const canvas = document.createElement('canvas');
          const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
          let devicePerformance = 'medium';

          if (gl) {
            const renderer = gl.getParameter(gl.RENDERER);
            const vendor = gl.getParameter(gl.VENDOR); // 简单的GPU性能判断

            if (renderer.includes('Mali') || renderer.includes('Adreno 3') || renderer.includes('PowerVR')) {
              devicePerformance = 'low';
            } else if (renderer.includes('Adreno 6') || renderer.includes('Mali-G') || renderer.includes('Apple')) {
              devicePerformance = 'high';
            }
          } // 根据屏幕分辨率调整


          const screenWidth = window.screen.width * (window.devicePixelRatio || 1);
          const screenHeight = window.screen.height * (window.devicePixelRatio || 1);
          return this.getConfigForResolution(screenWidth, screenHeight);
        }
        /**
         * 🎯 验证配置参数的合理性
         */


        static validateConfig(config) {
          const warnings = [];

          if (config.TOUCH_THROTTLE_DELAY < 10) {
            warnings.push('触摸节流延迟过小，可能影响性能');
          }

          if (config.MAX_LAYERS > 5) {
            warnings.push('最大层数过多，可能影响性能');
          }

          if (config.CACHE_TIMEOUT < 50) {
            warnings.push('缓存超时时间过短，可能降低缓存效果');
          }

          if (warnings.length > 0) {
            console.warn('🎯 多层射线检测配置警告:', warnings);
            return false;
          }

          return true;
        }

      }, _class2.PERFORMANCE = {
        // 触摸节流延迟 (ms) - 控制射线检测频率
        TOUCH_THROTTLE_DELAY: 33,
        // 30FPS
        // 移动距离阈值 (px) - 小于此距离不触发检测
        MOVE_DISTANCE_THRESHOLD: 10,
        // 缓存超时时间 (ms)
        CACHE_TIMEOUT: 100,
        // 射线检测位置阈值 (px) - 用于缓存网格化
        RAYCAST_POSITION_THRESHOLD: 8,
        // 性能警告阈值 (ms)
        PERFORMANCE_WARNING_THRESHOLD: 3
      }, _class2.MULTI_LAYER = {
        // 最大检测层数 - 平衡性能和功能
        MAX_LAYERS: 3,
        // 慢速移动阈值 (px) - 小于此值认为是慢速移动
        SLOW_MOVE_THRESHOLD: 3,
        // 层级切换延迟 (ms) - 防止切换过快
        LAYER_SWITCH_DELAY: 200,
        // 慢速移动检测时间 (ms) - 持续慢速移动多久开始层级切换
        SLOW_MOVE_DETECTION_TIME: 500
      }, _class2.STACKED_SCENE = {
        // 是否启用多层检测
        ENABLE_MULTI_LAYER: true,
        // 是否启用智能层级切换
        ENABLE_SMART_LAYER_SWITCHING: true,
        // 是否启用慢速移动检测
        ENABLE_SLOW_MOVE_DETECTION: true,
        // 是否在快速移动时重置到顶层
        RESET_TO_TOP_ON_FAST_MOVE: true
      }, _class2.DEBUG = {
        // 是否启用性能监控
        ENABLE_PERFORMANCE_MONITORING: true,
        // 性能报告间隔 (ms)
        PERFORMANCE_REPORT_INTERVAL: 10000,
        // 是否显示层级切换日志
        SHOW_LAYER_SWITCH_LOGS: true,
        // 是否显示缓存命中日志
        SHOW_CACHE_HIT_LOGS: false
      }, _class2)) || _class)); // 暴露到全局，便于调试


      window.MultiLayerRaycastConfig = MultiLayerRaycastConfig;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=213047c59817b1ec7615864c7de139252ea0acad.js.map