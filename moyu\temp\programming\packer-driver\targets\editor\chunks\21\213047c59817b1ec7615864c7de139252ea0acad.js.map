{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/optimization/MultiLayerRaycastConfig.ts"], "names": ["_decorator", "ccclass", "MultiLayerRaycastConfig", "getOptimizedConfig", "devicePerformance", "baseConfig", "PERFORMANCE", "MULTI_LAYER", "STACKED_SCENE", "DEBUG", "TOUCH_THROTTLE_DELAY", "MAX_LAYERS", "CACHE_TIMEOUT", "PERFORMANCE_WARNING_THRESHOLD", "ENABLE_PERFORMANCE_MONITORING", "SLOW_MOVE_THRESHOLD", "LAYER_SWITCH_DELAY", "getConfigForResolution", "width", "height", "config", "MOVE_DISTANCE_THRESHOLD", "RAYCAST_POSITION_THRESHOLD", "getRecommendedConfig", "canvas", "document", "createElement", "gl", "getContext", "renderer", "getParameter", "RENDERER", "vendor", "VENDOR", "includes", "screenWidth", "window", "screen", "devicePixelRatio", "screenHeight", "validateConfig", "warnings", "push", "length", "console", "warn", "SLOW_MOVE_DETECTION_TIME", "ENABLE_MULTI_LAYER", "ENABLE_SMART_LAYER_SWITCHING", "ENABLE_SLOW_MOVE_DETECTION", "RESET_TO_TOP_ON_FAST_MOVE", "PERFORMANCE_REPORT_INTERVAL", "SHOW_LAYER_SWITCH_LOGS", "SHOW_CACHE_HIT_LOGS"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcD,U;AAEpB;AACA;AACA;AACA;;yCAEaE,uB,WADZD,OAAO,CAAC,yBAAD,C,2BAAR,MACaC,uBADb,CACqC;AAyEjC;AACJ;AACA;AAC6B,eAAlBC,kBAAkB,CAACC,iBAA4C,GAAG,QAAhD,EAA0D;AAC/E,gBAAMC,UAAU,GAAG,EACf,GAAG,KAAKC,WADO;AAEf,eAAG,KAAKC,WAFO;AAGf,eAAG,KAAKC,aAHO;AAIf,eAAG,KAAKC;AAJO,WAAnB;;AAOA,kBAAQL,iBAAR;AACI,iBAAK,KAAL;AACI,qBAAO,EACH,GAAGC,UADA;AAEHK,gBAAAA,oBAAoB,EAAE,EAFnB;AAEuB;AAC1BC,gBAAAA,UAAU,EAAE,CAHT;AAGY;AACfC,gBAAAA,aAAa,EAAE,GAJZ;AAIiB;AACpBC,gBAAAA,6BAA6B,EAAE,CAL5B;AAMHC,gBAAAA,6BAA6B,EAAE,KAN5B,CAMkC;;AANlC,eAAP;;AASJ,iBAAK,MAAL;AACI,qBAAO,EACH,GAAGT,UADA;AAEHK,gBAAAA,oBAAoB,EAAE,EAFnB;AAEuB;AAC1BC,gBAAAA,UAAU,EAAE,CAHT;AAGY;AACfC,gBAAAA,aAAa,EAAE,EAJZ;AAIgB;AACnBC,gBAAAA,6BAA6B,EAAE,CAL5B;AAMHE,gBAAAA,mBAAmB,EAAE,CANlB;AAMqB;AACxBC,gBAAAA,kBAAkB,EAAE,GAPjB,CAOqB;;AAPrB,eAAP;;AAUJ;AAAS;AACL,qBAAOX,UAAP;AAvBR;AAyBH;AAED;AACJ;AACA;;;AACiC,eAAtBY,sBAAsB,CAACC,KAAD,EAAgBC,MAAhB,EAAgC;AACzD,gBAAMC,MAAM,GAAG,KAAKjB,kBAAL,EAAf,CADyD,CAGzD;;AACA,cAAIe,KAAK,GAAG,IAAR,IAAgBC,MAAM,GAAG,IAA7B,EAAmC;AAC/B,mBAAO,EACH,GAAGC,MADA;AAEHC,cAAAA,uBAAuB,EAAED,MAAM,CAACC,uBAAP,GAAiC,GAFvD;AAGHC,cAAAA,0BAA0B,EAAEF,MAAM,CAACE,0BAAP,GAAoC,GAH7D;AAIHP,cAAAA,mBAAmB,EAAEK,MAAM,CAACL,mBAAP,GAA6B;AAJ/C,aAAP;AAMH,WAXwD,CAazD;;;AACA,cAAIG,KAAK,GAAG,GAAR,IAAeC,MAAM,GAAG,IAA5B,EAAkC;AAC9B,mBAAO,EACH,GAAGC,MADA;AAEHC,cAAAA,uBAAuB,EAAED,MAAM,CAACC,uBAAP,GAAiC,GAFvD;AAGHC,cAAAA,0BAA0B,EAAEF,MAAM,CAACE,0BAAP,GAAoC,GAH7D;AAIHP,cAAAA,mBAAmB,EAAEK,MAAM,CAACL,mBAAP,GAA6B;AAJ/C,aAAP;AAMH;;AAED,iBAAOK,MAAP;AACH;AAED;AACJ;AACA;;;AAC+B,eAApBG,oBAAoB,GAAG;AAC1B;AACA,gBAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAf;AACA,gBAAMC,EAAE,GAAGH,MAAM,CAACI,UAAP,CAAkB,OAAlB,KAA8BJ,MAAM,CAACI,UAAP,CAAkB,oBAAlB,CAAzC;AAEA,cAAIxB,iBAA4C,GAAG,QAAnD;;AAEA,cAAIuB,EAAJ,EAAQ;AACJ,kBAAME,QAAQ,GAAGF,EAAE,CAACG,YAAH,CAAgBH,EAAE,CAACI,QAAnB,CAAjB;AACA,kBAAMC,MAAM,GAAGL,EAAE,CAACG,YAAH,CAAgBH,EAAE,CAACM,MAAnB,CAAf,CAFI,CAIJ;;AACA,gBAAIJ,QAAQ,CAACK,QAAT,CAAkB,MAAlB,KAA6BL,QAAQ,CAACK,QAAT,CAAkB,UAAlB,CAA7B,IAA8DL,QAAQ,CAACK,QAAT,CAAkB,SAAlB,CAAlE,EAAgG;AAC5F9B,cAAAA,iBAAiB,GAAG,KAApB;AACH,aAFD,MAEO,IAAIyB,QAAQ,CAACK,QAAT,CAAkB,UAAlB,KAAiCL,QAAQ,CAACK,QAAT,CAAkB,QAAlB,CAAjC,IAAgEL,QAAQ,CAACK,QAAT,CAAkB,OAAlB,CAApE,EAAgG;AACnG9B,cAAAA,iBAAiB,GAAG,MAApB;AACH;AACJ,WAjByB,CAmB1B;;;AACA,gBAAM+B,WAAW,GAAGC,MAAM,CAACC,MAAP,CAAcnB,KAAd,IAAuBkB,MAAM,CAACE,gBAAP,IAA2B,CAAlD,CAApB;AACA,gBAAMC,YAAY,GAAGH,MAAM,CAACC,MAAP,CAAclB,MAAd,IAAwBiB,MAAM,CAACE,gBAAP,IAA2B,CAAnD,CAArB;AAEA,iBAAO,KAAKrB,sBAAL,CAA4BkB,WAA5B,EAAyCI,YAAzC,CAAP;AACH;AAED;AACJ;AACA;;;AACyB,eAAdC,cAAc,CAACpB,MAAD,EAAuB;AACxC,gBAAMqB,QAAkB,GAAG,EAA3B;;AAEA,cAAIrB,MAAM,CAACV,oBAAP,GAA8B,EAAlC,EAAsC;AAClC+B,YAAAA,QAAQ,CAACC,IAAT,CAAc,iBAAd;AACH;;AAED,cAAItB,MAAM,CAACT,UAAP,GAAoB,CAAxB,EAA2B;AACvB8B,YAAAA,QAAQ,CAACC,IAAT,CAAc,eAAd;AACH;;AAED,cAAItB,MAAM,CAACR,aAAP,GAAuB,EAA3B,EAA+B;AAC3B6B,YAAAA,QAAQ,CAACC,IAAT,CAAc,mBAAd;AACH;;AAED,cAAID,QAAQ,CAACE,MAAT,GAAkB,CAAtB,EAAyB;AACrBC,YAAAA,OAAO,CAACC,IAAR,CAAa,gBAAb,EAA+BJ,QAA/B;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;;AAjMgC,O,UAKjBnC,W,GAAc;AAC1B;AACAI,QAAAA,oBAAoB,EAAE,EAFI;AAEA;AAE1B;AACAW,QAAAA,uBAAuB,EAAE,EALC;AAO1B;AACAT,QAAAA,aAAa,EAAE,GARW;AAU1B;AACAU,QAAAA,0BAA0B,EAAE,CAXF;AAa1B;AACAT,QAAAA,6BAA6B,EAAE;AAdL,O,UAoBdN,W,GAAc;AAC1B;AACAI,QAAAA,UAAU,EAAE,CAFc;AAI1B;AACAI,QAAAA,mBAAmB,EAAE,CALK;AAO1B;AACAC,QAAAA,kBAAkB,EAAE,GARM;AAU1B;AACA8B,QAAAA,wBAAwB,EAAE;AAXA,O,UAiBdtC,a,GAAgB;AAC5B;AACAuC,QAAAA,kBAAkB,EAAE,IAFQ;AAI5B;AACAC,QAAAA,4BAA4B,EAAE,IALF;AAO5B;AACAC,QAAAA,0BAA0B,EAAE,IARA;AAU5B;AACAC,QAAAA,yBAAyB,EAAE;AAXC,O,UAiBhBzC,K,GAAQ;AACpB;AACAK,QAAAA,6BAA6B,EAAE,IAFX;AAIpB;AACAqC,QAAAA,2BAA2B,EAAE,KALT;AAOpB;AACAC,QAAAA,sBAAsB,EAAE,IARJ;AAUpB;AACAC,QAAAA,mBAAmB,EAAE;AAXD,O,yBAyI5B;;;AACCjB,MAAAA,MAAD,CAAgBlC,uBAAhB,GAA0CA,uBAA1C", "sourcesContent": ["import { _decorator } from 'cc';\n\nconst { ccclass } = _decorator;\n\n/**\n * 🎯 多层射线检测配置\n * 专为堆叠模型场景优化的配置参数\n */\n@ccclass('MultiLayerRaycastConfig')\nexport class MultiLayerRaycastConfig {\n    \n    /**\n     * 🎯 基础性能配置\n     */\n    static readonly PERFORMANCE = {\n        // 触摸节流延迟 (ms) - 控制射线检测频率\n        TOUCH_THROTTLE_DELAY: 33, // 30FPS\n        \n        // 移动距离阈值 (px) - 小于此距离不触发检测\n        MOVE_DISTANCE_THRESHOLD: 10,\n        \n        // 缓存超时时间 (ms)\n        CACHE_TIMEOUT: 100,\n        \n        // 射线检测位置阈值 (px) - 用于缓存网格化\n        RAYCAST_POSITION_THRESHOLD: 8,\n        \n        // 性能警告阈值 (ms)\n        PERFORMANCE_WARNING_THRESHOLD: 3\n    };\n    \n    /**\n     * 🎯 多层检测配置\n     */\n    static readonly MULTI_LAYER = {\n        // 最大检测层数 - 平衡性能和功能\n        MAX_LAYERS: 3,\n        \n        // 慢速移动阈值 (px) - 小于此值认为是慢速移动\n        SLOW_MOVE_THRESHOLD: 3,\n        \n        // 层级切换延迟 (ms) - 防止切换过快\n        LAYER_SWITCH_DELAY: 200,\n        \n        // 慢速移动检测时间 (ms) - 持续慢速移动多久开始层级切换\n        SLOW_MOVE_DETECTION_TIME: 500\n    };\n    \n    /**\n     * 🎯 堆叠场景优化配置\n     */\n    static readonly STACKED_SCENE = {\n        // 是否启用多层检测\n        ENABLE_MULTI_LAYER: true,\n        \n        // 是否启用智能层级切换\n        ENABLE_SMART_LAYER_SWITCHING: true,\n        \n        // 是否启用慢速移动检测\n        ENABLE_SLOW_MOVE_DETECTION: true,\n        \n        // 是否在快速移动时重置到顶层\n        RESET_TO_TOP_ON_FAST_MOVE: true\n    };\n    \n    /**\n     * 🎯 调试和监控配置\n     */\n    static readonly DEBUG = {\n        // 是否启用性能监控\n        ENABLE_PERFORMANCE_MONITORING: true,\n        \n        // 性能报告间隔 (ms)\n        PERFORMANCE_REPORT_INTERVAL: 10000,\n        \n        // 是否显示层级切换日志\n        SHOW_LAYER_SWITCH_LOGS: true,\n        \n        // 是否显示缓存命中日志\n        SHOW_CACHE_HIT_LOGS: false\n    };\n    \n    /**\n     * 🎯 根据设备性能调整配置\n     */\n    static getOptimizedConfig(devicePerformance: 'low' | 'medium' | 'high' = 'medium') {\n        const baseConfig = {\n            ...this.PERFORMANCE,\n            ...this.MULTI_LAYER,\n            ...this.STACKED_SCENE,\n            ...this.DEBUG\n        };\n        \n        switch (devicePerformance) {\n            case 'low':\n                return {\n                    ...baseConfig,\n                    TOUCH_THROTTLE_DELAY: 50, // 20FPS\n                    MAX_LAYERS: 2, // 减少层数\n                    CACHE_TIMEOUT: 150, // 增加缓存时间\n                    PERFORMANCE_WARNING_THRESHOLD: 5,\n                    ENABLE_PERFORMANCE_MONITORING: false // 关闭监控减少开销\n                };\n                \n            case 'high':\n                return {\n                    ...baseConfig,\n                    TOUCH_THROTTLE_DELAY: 16, // 60FPS\n                    MAX_LAYERS: 5, // 增加层数\n                    CACHE_TIMEOUT: 50, // 减少缓存时间提高精度\n                    PERFORMANCE_WARNING_THRESHOLD: 2,\n                    SLOW_MOVE_THRESHOLD: 2, // 更敏感的慢速检测\n                    LAYER_SWITCH_DELAY: 150 // 更快的切换\n                };\n                \n            default: // medium\n                return baseConfig;\n        }\n    }\n    \n    /**\n     * 🎯 针对不同分辨率的优化配置\n     */\n    static getConfigForResolution(width: number, height: number) {\n        const config = this.getOptimizedConfig();\n        \n        // 高分辨率设备调整阈值\n        if (width > 1080 || height > 1920) {\n            return {\n                ...config,\n                MOVE_DISTANCE_THRESHOLD: config.MOVE_DISTANCE_THRESHOLD * 1.5,\n                RAYCAST_POSITION_THRESHOLD: config.RAYCAST_POSITION_THRESHOLD * 1.5,\n                SLOW_MOVE_THRESHOLD: config.SLOW_MOVE_THRESHOLD * 1.5\n            };\n        }\n        \n        // 低分辨率设备调整阈值\n        if (width < 720 || height < 1280) {\n            return {\n                ...config,\n                MOVE_DISTANCE_THRESHOLD: config.MOVE_DISTANCE_THRESHOLD * 0.7,\n                RAYCAST_POSITION_THRESHOLD: config.RAYCAST_POSITION_THRESHOLD * 0.7,\n                SLOW_MOVE_THRESHOLD: config.SLOW_MOVE_THRESHOLD * 0.7\n            };\n        }\n        \n        return config;\n    }\n    \n    /**\n     * 🎯 获取推荐配置 - 根据当前设备自动选择\n     */\n    static getRecommendedConfig() {\n        // 简单的设备性能检测\n        const canvas = document.createElement('canvas');\n        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');\n        \n        let devicePerformance: 'low' | 'medium' | 'high' = 'medium';\n        \n        if (gl) {\n            const renderer = gl.getParameter(gl.RENDERER);\n            const vendor = gl.getParameter(gl.VENDOR);\n            \n            // 简单的GPU性能判断\n            if (renderer.includes('Mali') || renderer.includes('Adreno 3') || renderer.includes('PowerVR')) {\n                devicePerformance = 'low';\n            } else if (renderer.includes('Adreno 6') || renderer.includes('Mali-G') || renderer.includes('Apple')) {\n                devicePerformance = 'high';\n            }\n        }\n        \n        // 根据屏幕分辨率调整\n        const screenWidth = window.screen.width * (window.devicePixelRatio || 1);\n        const screenHeight = window.screen.height * (window.devicePixelRatio || 1);\n        \n        return this.getConfigForResolution(screenWidth, screenHeight);\n    }\n    \n    /**\n     * 🎯 验证配置参数的合理性\n     */\n    static validateConfig(config: any): boolean {\n        const warnings: string[] = [];\n        \n        if (config.TOUCH_THROTTLE_DELAY < 10) {\n            warnings.push('触摸节流延迟过小，可能影响性能');\n        }\n        \n        if (config.MAX_LAYERS > 5) {\n            warnings.push('最大层数过多，可能影响性能');\n        }\n        \n        if (config.CACHE_TIMEOUT < 50) {\n            warnings.push('缓存超时时间过短，可能降低缓存效果');\n        }\n        \n        if (warnings.length > 0) {\n            console.warn('🎯 多层射线检测配置警告:', warnings);\n            return false;\n        }\n        \n        return true;\n    }\n}\n\n// 暴露到全局，便于调试\n(window as any).MultiLayerRaycastConfig = MultiLayerRaycastConfig;\n"]}