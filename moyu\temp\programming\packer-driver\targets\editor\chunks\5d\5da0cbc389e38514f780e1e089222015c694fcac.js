System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, AudioClip, Collider, MeshRenderer, physics, RigidBody, SkeletalAnimation, tween, ClientConst, MusicConf, PHY_GROUP, ecs, CCComp, smc, oops, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, ItemSceneViewComp;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfClientConst(extras) {
    _reporterNs.report("ClientConst", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMusicConf(extras) {
    _reporterNs.report("MusicConf", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPHY_GROUP(extras) {
    _reporterNs.report("PHY_GROUP", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCCComp(extras) {
    _reporterNs.report("CCComp", "../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPickBox(extras) {
    _reporterNs.report("PickBox", "../../prefab/PickBox", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemEntity(extras) {
    _reporterNs.report("ItemEntity", "../ItemEntity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      AudioClip = _cc.AudioClip;
      Collider = _cc.Collider;
      MeshRenderer = _cc.MeshRenderer;
      physics = _cc.physics;
      RigidBody = _cc.RigidBody;
      SkeletalAnimation = _cc.SkeletalAnimation;
      tween = _cc.tween;
    }, function (_unresolved_2) {
      ClientConst = _unresolved_2.ClientConst;
      MusicConf = _unresolved_2.MusicConf;
      PHY_GROUP = _unresolved_2.PHY_GROUP;
    }, function (_unresolved_3) {
      ecs = _unresolved_3.ecs;
    }, function (_unresolved_4) {
      CCComp = _unresolved_4.CCComp;
    }, function (_unresolved_5) {
      smc = _unresolved_5.smc;
    }, function (_unresolved_6) {
      oops = _unresolved_6.oops;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3eb5fdhNS5Iu4yEQ+dEujjt", "ItemSceneViewComp", undefined);

      __checkObsolete__(['_decorator', 'AudioClip', 'Collider', 'geometry', 'ITriggerEvent', 'Material', 'MeshRenderer', 'Node', 'physics', 'RigidBody', 'SkeletalAnimation', 'tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ItemSceneViewComp", ItemSceneViewComp = (_dec = ccclass('ItemSceneView'), _dec2 = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register('ItemSceneView'), _dec3 = property({
        type: AudioClip
      }), _dec4 = property({
        type: AudioClip
      }), _dec(_class = _dec2(_class = (_class2 = class ItemSceneViewComp extends (_crd && CCComp === void 0 ? (_reportPossibleCrUseOfCCComp({
        error: Error()
      }), CCComp) : CCComp) {
        constructor(...args) {
          super(...args);
          this.ent = void 0;
          this.ItemModel = void 0;
          this.collider = [];
          // 所有的碰撞体，包括子节点的碰撞体
          this.otherColliderNode = [];
          // 🌟 简化材质系统 - 直接使用共享材质
          this.isGlowing = false;

          // 当前是否在发光
          _initializerDefineProperty(this, "pickEffect", _descriptor, this);

          _initializerDefineProperty(this, "idleEffect", _descriptor2, this);
        }

        reset() {
          this.node.parent = null;
          this.node.destroy();
        }

        start() {
          this.initializeComponent();
          this.collider = this.node.getComponents(Collider);
          const comp = this.node.getComponent(RigidBody);

          if (comp) {
            comp.linearDamping = (_crd && ClientConst === void 0 ? (_reportPossibleCrUseOfClientConst({
              error: Error()
            }), ClientConst) : ClientConst).LinearDamping;
            comp.angularDamping = (_crd && ClientConst === void 0 ? (_reportPossibleCrUseOfClientConst({
              error: Error()
            }), ClientConst) : ClientConst).AngularDamping;
          }

          if (this.collider) {
            // 追加子节点的collider
            this.node.children.forEach(a => {
              const collider = a.getComponent(Collider);

              if (collider) {
                this.collider.push(collider);
              }
            });
          }
        }
        /**
         * 🎯 组件初始化逻辑（可重复调用）
         */


        initializeComponent() {
          if (!this.ent || !this.ent.ItemModel) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError(`❌ [${this.node.name}] 无法初始化：ent或ItemModel仍未设置`);
            return;
          }

          this.ItemModel = this.ent.ItemModel;
          const meshRenderer = this.node.getComponent(MeshRenderer);

          if (meshRenderer) {
            this.ItemModel.meshRenderer = meshRenderer;
          }

          this.addRigidBody();
        } // 🌟 发光效果：创建材质实例（基于testSceneComp策略）


        applyGlowEffect() {
          if (!this.ItemModel.meshRenderer || this.isGlowing) return;
          let interactionManager = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).sceneMgr.getCurrentGameEntity().interactionManager;
          let fromMaterial = interactionManager.sharedMaterial;

          if (!fromMaterial) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError(`❌ [${this.node.name}] 无法初始化：fromMaterial 材质未设置`);
            return;
          } //目前只有到一个材质。


          let rimLightXrayPbrMaterial = interactionManager.rimLightXrayPbrMaterial;
          this.copyMaterialProperties(fromMaterial, rimLightXrayPbrMaterial);
          this.ItemModel.meshRenderer.setSharedMaterial(rimLightXrayPbrMaterial, 0);
          this.ItemModel.meshRenderer.setInstancedAttribute('a_instanced_rimStrength', [2.0]);
          this.isGlowing = true;
        } // 🎨 安全的材质属性复制方法 - 确保不破坏GPU Instancing合批


        copyMaterialProperties(fromMaterial, toMaterial) {
          try {
            // ✅ 复制纹理属性（只读取引用，不创建新实例）
            const normalMap = fromMaterial.getProperty('normalMap');
            const pbrMap = fromMaterial.getProperty('pbrMap');
            const albedoMap = fromMaterial.getProperty('albedoMap');
            if (normalMap) toMaterial.setProperty('normalMap', normalMap);
            if (pbrMap) toMaterial.setProperty('pbrMap', pbrMap);
            if (albedoMap) toMaterial.setProperty('albedoMap', albedoMap); // 🎨 复制基础颜色和强度属性 - 保持物品原始外观

            const albedoColor = fromMaterial.getProperty('albedo');
            const albedoScale = fromMaterial.getProperty('albedoScale');
            const emissive = fromMaterial.getProperty('emissive');
            const emissiveScale = fromMaterial.getProperty('emissiveScale');
            const normalStrength = fromMaterial.getProperty('normalStrength'); // 🎯 复制PBR相关属性

            const metallicFactor = fromMaterial.getProperty('metallicFactor');
            const roughnessFactor = fromMaterial.getProperty('roughnessFactor');
            const occlusionStrength = fromMaterial.getProperty('occlusionStrength'); // 🌟 设置基础材质属性 - 保持物品原色

            if (albedoColor !== undefined) toMaterial.setProperty('albedo', albedoColor);
            if (albedoScale !== undefined) toMaterial.setProperty('albedoScale', albedoScale);
            if (emissive !== undefined) toMaterial.setProperty('emissive', emissive);
            if (emissiveScale !== undefined) toMaterial.setProperty('emissiveScale', emissiveScale);
            if (normalStrength !== undefined) toMaterial.setProperty('normalStrength', normalStrength);
            if (metallicFactor !== undefined) toMaterial.setProperty('metallicFactor', metallicFactor);
            if (roughnessFactor !== undefined) toMaterial.setProperty('roughnessFactor', roughnessFactor);
            if (occlusionStrength !== undefined) toMaterial.setProperty('occlusionStrength', occlusionStrength); // 🚀 不复制发光属性！保持用户在面板中的设置
            // glowColor, breatheSpeed, breatheIntensity, xrayAlpha 保持用户设置的值

            console.log(`✅ 材质属性复制完成: albedo=${albedoColor}, 发光属性保持用户设置`);
          } catch (error) {
            console.warn('⚠️ 材质属性复制失败:', error);
          }
        } // 🌟 移除发光效果：恢复到共享材质


        removeGlowEffect() {
          if (!this.isGlowing || !this.ItemModel.meshRenderer) return;
          let interactionManager = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).sceneMgr.getCurrentGameEntity().interactionManager;
          let sharedMaterial = interactionManager.sharedMaterial;
          this.ItemModel.meshRenderer.setSharedMaterial(sharedMaterial, 0);
          this.isGlowing = false;
        }

        setShadow(cast, receive) {
          if (this.ItemModel.meshRenderer) {
            this.ItemModel.meshRenderer.shadowCastingMode = cast ? 1 : 0;
            this.ItemModel.meshRenderer.receiveShadow = receive ? 1 : 0;
          }
        }

        doSkelAnimation() {
          var _this$node$getCompone;

          (_this$node$getCompone = this.node.getComponent(SkeletalAnimation)) == null || _this$node$getCompone.play();
        }

        getItemId() {
          return this.ItemModel.itemId;
        } //获取包围盒 只获取第一个mesh的


        getBoundingBox() {
          const meshRenderer = this.ItemModel.meshRenderer;

          if (meshRenderer) {
            var _meshRenderer$model;

            return (_meshRenderer$model = meshRenderer.model) == null ? void 0 : _meshRenderer$model.worldBounds;
          }
        }

        doOnMove(pickBoxComp) {
          this.ItemModel.pickBox = pickBoxComp; // 🎯 让PickBox也记录当前物品

          pickBoxComp.pickItem(this.node);

          if (this.ItemModel.rigidBody) {
            this.ItemModel.rigidBody.group = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
              error: Error()
            }), PHY_GROUP) : PHY_GROUP).ITEM_BOX;
          }

          this.onCancelTouch();
        }

        playEffectOnChoose() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).audio.playEffect(this.pickEffect || (_crd && MusicConf === void 0 ? (_reportPossibleCrUseOfMusicConf({
            error: Error()
          }), MusicConf) : MusicConf).commonPickEffect);
        }

        set enableRigidBody(b) {
          if (this.ItemModel.rigidBody) {
            this.ItemModel.rigidBody.enabled = b;
            this.ItemModel.rigidBody.useGravity = b;
          }

          this.collider.map(a => a.enabled = b);
        }

        changeRigidBodyType(type = physics.RigidBody.Type.STATIC) {
          if (this.ItemModel.rigidBody) {
            this.ItemModel.rigidBody.type = type;
          }
        }

        addRigidBody() {
          // 🎯 安全检查：确保meshRenderer存在
          if (!this.ItemModel.meshRenderer) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError(`⚠️ [${this.node.name}] addRigidBody时meshRenderer为空`);
            return;
          }

          let rigid = this.node.getComponent(RigidBody);

          if (!rigid) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError(`⚠️ [${this.node.name}] addRigidBody时rigid为空`);
            return;
          }

          rigid.group = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM;
          rigid.allowSleep = false;
          this.ItemModel.rigidBody = rigid; // 根据模型的aabb大小设置质量

          const aabb = this.getBoundingBox();

          if (aabb) {
            const halfExtents = aabb.halfExtents;
            const volume = 8 * halfExtents.x * halfExtents.y * halfExtents.z; // 体积 = 2 * hw * 2 * hh * 2 * hl

            rigid.mass = volume; // 假设密度为1
          } else {
            rigid.mass = 1; // 默认质量
          }

          return;
        }

        onTouch() {
          // 🎯 安全检查：确保ItemModel已正确初始化
          if (!this.ItemModel) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError(`❌ [${this.node.name}] onTouch时ItemModel未初始化`);
            return;
          }

          this.ItemModel.touching = true;
          this.doSkelAnimation(); // 🌟 简化发光逻辑：直接应用预加载材质

          this.applyGlowEffect();
          this.collider.forEach(a => a.on('onCollisionStay', this.onTriggerEnter, this));
        }

        onCancelTouch() {
          var _this$node$getCompone2;

          // 🎯 安全检查：确保ItemModel已正确初始化
          if (!this.ItemModel) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn(`⚠️ [${this.node.name}] onCancelTouch时ItemModel未初始化`);
            return;
          }

          this.ItemModel.touching = false; // 🌟 简化材质恢复

          this.removeGlowEffect();
          (_this$node$getCompone2 = this.node.getComponent(SkeletalAnimation)) == null || _this$node$getCompone2.stop();
          this.otherColliderNode = [];
          this.collider.forEach(a => a.off('onCollisionStay', this.onTriggerEnter, this));
        }

        onTriggerEnter(event) {
          let otherNode = event.otherCollider.node;
          const rigidBody = event.otherCollider.node.getComponent(RigidBody);

          if (this.node !== otherNode && (rigidBody == null ? void 0 : rigidBody.group) == (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM && this.otherColliderNode.indexOf(otherNode) == -1) {
            this.otherColliderNode.push(otherNode); // 计算从当前节点到其他节点的方向向量

            let thisPos = this.node.getWorldPosition();
            const direction = otherNode.getWorldPosition().subtract(thisPos).normalize(); // 计算目标位置，远离当前节点

            const distance = 0.1; // 移动的距离，可以根据需要调整

            const targetPos = otherNode.getWorldPosition().add(direction.multiplyScalar(distance)); // 使用 tween 动画移动其他节点

            tween(otherNode).to(0.2, {
              position: targetPos
            }) // 0.5 秒内移动到目标位置
            .start();
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "pickEffect", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "idleEffect", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5da0cbc389e38514f780e1e089222015c694fcac.js.map