{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/item/view/ItemSceneViewComp.ts"], "names": ["_decorator", "AudioClip", "Collider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "physics", "RigidBody", "SkeletalAnimation", "tween", "ClientConst", "MusicConf", "PHY_GROUP", "ecs", "CCComp", "smc", "oops", "ccclass", "property", "ItemSceneViewComp", "register", "type", "ent", "ItemModel", "collider", "otherColliderNode", "isGlowing", "reset", "node", "parent", "destroy", "start", "initializeComponent", "getComponents", "comp", "getComponent", "linearDamping", "LinearDamping", "angularDamping", "AngularDamping", "children", "for<PERSON>ach", "a", "push", "log", "logError", "name", "<PERSON><PERSON><PERSON><PERSON>", "addRigidBody", "applyGlowEffect", "interactionManager", "sceneMgr", "getCurrentGameEntity", "fromMaterial", "sharedMaterial", "rimLightXrayPbrMaterial", "copyMaterialProperties", "setSharedMaterial", "setInstancedAttribute", "toMaterial", "normalMap", "getProperty", "pbrMap", "albedoMap", "setProperty", "albedoColor", "albedoScale", "emissive", "emissiveScale", "normalStrength", "metallicFactor", "roughnessFactor", "occlusionStrength", "undefined", "error", "console", "warn", "removeGlowEffect", "setShadow", "cast", "receive", "shadowCastingMode", "receiveShadow", "doSkelAnimation", "play", "getItemId", "itemId", "getBoundingBox", "model", "worldBounds", "doOnMove", "pickBoxComp", "pickBox", "pickItem", "rigidBody", "group", "ITEM_BOX", "onCancelTouch", "playEffectOnChoose", "audio", "playEffect", "pickEffect", "commonPickEffect", "enableRigidBody", "b", "enabled", "useGravity", "map", "changeRigidBodyType", "Type", "STATIC", "rigid", "ITEM", "allowSleep", "aabb", "halfExtents", "volume", "x", "y", "z", "mass", "onTouch", "touching", "on", "onTriggerEnter", "log<PERSON>arn", "stop", "off", "event", "otherNode", "otherCollider", "indexOf", "thisPos", "getWorldPosition", "direction", "subtract", "normalize", "distance", "targetPos", "add", "multiplyScalar", "to", "position"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACIA,MAAAA,U,OAAAA,U;AACAC,MAAAA,S,OAAAA,S;AACAC,MAAAA,Q,OAAAA,Q;AAIAC,MAAAA,Y,OAAAA,Y;AAEAC,MAAAA,O,OAAAA,O;AACAC,MAAAA,S,OAAAA,S;AACAC,MAAAA,iB,OAAAA,iB;AACAC,MAAAA,K,OAAAA,K;;AAGKC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,S,iBAAAA,S;AAAWC,MAAAA,S,iBAAAA,S;;AAExBC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,G,iBAAAA,G;;AAEAC,MAAAA,I,iBAAAA,I;;;;;;;;;OAIH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;mCAIjBiB,iB,WAFZF,OAAO,CAAC,eAAD,C,UACP;AAAA;AAAA,sBAAIG,QAAJ,CAAa,eAAb,C,UAWIF,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAElB;AAAR,OAAD,C,UAGRe,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAElB;AAAR,OAAD,C,0CAfb,MAEagB,iBAFb;AAAA;AAAA,4BAE8C;AAAA;AAAA;AAAA,eAC1CG,GAD0C;AAAA,eAE1CC,SAF0C;AAAA,eAI1CC,QAJ0C,GAInB,EAJmB;AAIf;AAJe,eAK1CC,iBAL0C,GAKd,EALc;AAO1C;AAP0C,eAQlCC,SARkC,GAQb,KARa;;AAQN;AARM;;AAAA;AAAA;;AAiB1CC,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACA,eAAKD,IAAL,CAAUE,OAAV;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKC,mBAAL;AACA,eAAKR,QAAL,GAAgB,KAAKI,IAAL,CAAUK,aAAV,CAAwB7B,QAAxB,CAAhB;AACA,gBAAM8B,IAAI,GAAG,KAAKN,IAAL,CAAUO,YAAV,CAAuB5B,SAAvB,CAAb;;AACA,cAAI2B,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACE,aAAL,GAAqB;AAAA;AAAA,4CAAYC,aAAjC;AACAH,YAAAA,IAAI,CAACI,cAAL,GAAsB;AAAA;AAAA,4CAAYC,cAAlC;AACH;;AACD,cAAI,KAAKf,QAAT,EAAmB;AACf;AACA,iBAAKI,IAAL,CAAUY,QAAV,CAAmBC,OAAnB,CAA2BC,CAAC,IAAI;AAC5B,oBAAMlB,QAAQ,GAAGkB,CAAC,CAACP,YAAF,CAAe/B,QAAf,CAAjB;;AACA,kBAAIoB,QAAJ,EAAc;AACV,qBAAKA,QAAL,CAAcmB,IAAd,CAAmBnB,QAAnB;AACH;AACJ,aALD;AAMH;AACJ;AAED;AACJ;AACA;;;AACIQ,QAAAA,mBAAmB,GAAS;AACxB,cAAI,CAAC,KAAKV,GAAN,IAAa,CAAC,KAAKA,GAAL,CAASC,SAA3B,EAAsC;AAClC;AAAA;AAAA,8BAAKqB,GAAL,CAASC,QAAT,CAAmB,MAAK,KAAKjB,IAAL,CAAUkB,IAAK,2BAAvC;AACA;AACH;;AAED,eAAKvB,SAAL,GAAiB,KAAKD,GAAL,CAASC,SAA1B;AACA,gBAAMwB,YAAY,GAAG,KAAKnB,IAAL,CAAUO,YAAV,CAAuB9B,YAAvB,CAArB;;AACA,cAAI0C,YAAJ,EAAkB;AACd,iBAAKxB,SAAL,CAAewB,YAAf,GAA8BA,YAA9B;AACH;;AACD,eAAKC,YAAL;AACH,SAxDyC,CA0D1C;;;AACQC,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAK1B,SAAL,CAAewB,YAAhB,IAAgC,KAAKrB,SAAzC,EAAoD;AACpD,cAAIwB,kBAAkB,GAAG;AAAA;AAAA,0BAAIC,QAAJ,CAAaC,oBAAb,GAAoCF,kBAA7D;AACA,cAAIG,YAAY,GAAGH,kBAAkB,CAACI,cAAtC;;AACA,cAAI,CAACD,YAAL,EAAmB;AACf;AAAA;AAAA,8BAAKT,GAAL,CAASC,QAAT,CAAmB,MAAK,KAAKjB,IAAL,CAAUkB,IAAK,4BAAvC;AACA;AACH,WAP2B,CAQ5B;;;AACA,cAAIS,uBAAuB,GAAGL,kBAAkB,CAACK,uBAAjD;AACA,eAAKC,sBAAL,CAA4BH,YAA5B,EAA0CE,uBAA1C;AACA,eAAKhC,SAAL,CAAewB,YAAf,CAA4BU,iBAA5B,CAA8CF,uBAA9C,EAAuE,CAAvE;AACA,eAAKhC,SAAL,CAAewB,YAAf,CAA4BW,qBAA5B,CAAkD,yBAAlD,EAA6E,CAAC,GAAD,CAA7E;AACA,eAAKhC,SAAL,GAAiB,IAAjB;AACH,SAzEyC,CA2E1C;;;AACQ8B,QAAAA,sBAAsB,CAACH,YAAD,EAAyBM,UAAzB,EAAqD;AAC/E,cAAI;AACA;AACA,kBAAMC,SAAS,GAAGP,YAAY,CAACQ,WAAb,CAAyB,WAAzB,CAAlB;AACA,kBAAMC,MAAM,GAAGT,YAAY,CAACQ,WAAb,CAAyB,QAAzB,CAAf;AACA,kBAAME,SAAS,GAAGV,YAAY,CAACQ,WAAb,CAAyB,WAAzB,CAAlB;AAEA,gBAAID,SAAJ,EAAeD,UAAU,CAACK,WAAX,CAAuB,WAAvB,EAAoCJ,SAApC;AACf,gBAAIE,MAAJ,EAAYH,UAAU,CAACK,WAAX,CAAuB,QAAvB,EAAiCF,MAAjC;AACZ,gBAAIC,SAAJ,EAAeJ,UAAU,CAACK,WAAX,CAAuB,WAAvB,EAAoCD,SAApC,EARf,CAUA;;AACA,kBAAME,WAAW,GAAGZ,YAAY,CAACQ,WAAb,CAAyB,QAAzB,CAApB;AACA,kBAAMK,WAAW,GAAGb,YAAY,CAACQ,WAAb,CAAyB,aAAzB,CAApB;AACA,kBAAMM,QAAQ,GAAGd,YAAY,CAACQ,WAAb,CAAyB,UAAzB,CAAjB;AACA,kBAAMO,aAAa,GAAGf,YAAY,CAACQ,WAAb,CAAyB,eAAzB,CAAtB;AACA,kBAAMQ,cAAc,GAAGhB,YAAY,CAACQ,WAAb,CAAyB,gBAAzB,CAAvB,CAfA,CAiBA;;AACA,kBAAMS,cAAc,GAAGjB,YAAY,CAACQ,WAAb,CAAyB,gBAAzB,CAAvB;AACA,kBAAMU,eAAe,GAAGlB,YAAY,CAACQ,WAAb,CAAyB,iBAAzB,CAAxB;AACA,kBAAMW,iBAAiB,GAAGnB,YAAY,CAACQ,WAAb,CAAyB,mBAAzB,CAA1B,CApBA,CAsBA;;AACA,gBAAII,WAAW,KAAKQ,SAApB,EAA+Bd,UAAU,CAACK,WAAX,CAAuB,QAAvB,EAAiCC,WAAjC;AAC/B,gBAAIC,WAAW,KAAKO,SAApB,EAA+Bd,UAAU,CAACK,WAAX,CAAuB,aAAvB,EAAsCE,WAAtC;AAC/B,gBAAIC,QAAQ,KAAKM,SAAjB,EAA4Bd,UAAU,CAACK,WAAX,CAAuB,UAAvB,EAAmCG,QAAnC;AAC5B,gBAAIC,aAAa,KAAKK,SAAtB,EAAiCd,UAAU,CAACK,WAAX,CAAuB,eAAvB,EAAwCI,aAAxC;AACjC,gBAAIC,cAAc,KAAKI,SAAvB,EACId,UAAU,CAACK,WAAX,CAAuB,gBAAvB,EAAyCK,cAAzC;AACJ,gBAAIC,cAAc,KAAKG,SAAvB,EACId,UAAU,CAACK,WAAX,CAAuB,gBAAvB,EAAyCM,cAAzC;AACJ,gBAAIC,eAAe,KAAKE,SAAxB,EACId,UAAU,CAACK,WAAX,CAAuB,iBAAvB,EAA0CO,eAA1C;AACJ,gBAAIC,iBAAiB,KAAKC,SAA1B,EACId,UAAU,CAACK,WAAX,CAAuB,mBAAvB,EAA4CQ,iBAA5C;AACP,WAnCD,CAmCE,OAAOE,KAAP,EAAc;AACZC,YAAAA,OAAO,CAACC,IAAR,CAAa,cAAb,EAA6BF,KAA7B;AACH;AACJ,SAnHyC,CAqH1C;;;AACQG,QAAAA,gBAAgB,GAAS;AAC7B,cAAI,CAAC,KAAKnD,SAAN,IAAmB,CAAC,KAAKH,SAAL,CAAewB,YAAvC,EAAqD;AACrD,cAAIG,kBAAkB,GAAG;AAAA;AAAA,0BAAIC,QAAJ,CAAaC,oBAAb,GAAoCF,kBAA7D;AACA,cAAII,cAAc,GAAGJ,kBAAkB,CAACI,cAAxC;AACA,eAAK/B,SAAL,CAAewB,YAAf,CAA4BU,iBAA5B,CAA8CH,cAA9C,EAA8D,CAA9D;AACA,eAAK5B,SAAL,GAAiB,KAAjB;AACH;;AAEDoD,QAAAA,SAAS,CAACC,IAAD,EAAgBC,OAAhB,EAAkC;AACvC,cAAI,KAAKzD,SAAL,CAAewB,YAAnB,EAAiC;AAC7B,iBAAKxB,SAAL,CAAewB,YAAf,CAA4BkC,iBAA5B,GAAgDF,IAAI,GAAG,CAAH,GAAO,CAA3D;AACA,iBAAKxD,SAAL,CAAewB,YAAf,CAA4BmC,aAA5B,GAA4CF,OAAO,GAAG,CAAH,GAAO,CAA1D;AACH;AACJ;;AAEDG,QAAAA,eAAe,GAAG;AAAA;;AACd,wCAAKvD,IAAL,CAAUO,YAAV,CAAuB3B,iBAAvB,oCAA2C4E,IAA3C;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAK9D,SAAL,CAAe+D,MAAtB;AACH,SA3IyC,CA6I1C;;;AACAC,QAAAA,cAAc,GAA8B;AACxC,gBAAMxC,YAAY,GAAG,KAAKxB,SAAL,CAAewB,YAApC;;AACA,cAAIA,YAAJ,EAAkB;AAAA;;AACd,0CAAOA,YAAY,CAACyC,KAApB,qBAAO,oBAAoBC,WAA3B;AACH;AACJ;;AAEDC,QAAAA,QAAQ,CAACC,WAAD,EAAuB;AAC3B,eAAKpE,SAAL,CAAeqE,OAAf,GAAyBD,WAAzB,CAD2B,CAE3B;;AACAA,UAAAA,WAAW,CAACE,QAAZ,CAAqB,KAAKjE,IAA1B;;AACA,cAAI,KAAKL,SAAL,CAAeuE,SAAnB,EAA8B;AAC1B,iBAAKvE,SAAL,CAAeuE,SAAf,CAAyBC,KAAzB,GAAiC;AAAA;AAAA,wCAAUC,QAA3C;AACH;;AACD,eAAKC,aAAL;AACH;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB;AAAA;AAAA,4BAAKC,KAAL,CAAWC,UAAX,CAAsB,KAAKC,UAAL,IAAmB;AAAA;AAAA,sCAAUC,gBAAnD;AACH;;AAEkB,YAAfC,eAAe,CAACC,CAAD,EAAa;AAC5B,cAAI,KAAKjF,SAAL,CAAeuE,SAAnB,EAA8B;AAC1B,iBAAKvE,SAAL,CAAeuE,SAAf,CAAyBW,OAAzB,GAAmCD,CAAnC;AACA,iBAAKjF,SAAL,CAAeuE,SAAf,CAAyBY,UAAzB,GAAsCF,CAAtC;AACH;;AACD,eAAKhF,QAAL,CAAcmF,GAAd,CAAkBjE,CAAC,IAAKA,CAAC,CAAC+D,OAAF,GAAYD,CAApC;AACH;;AAEDI,QAAAA,mBAAmB,CAACvF,IAA4B,GAAGf,OAAO,CAACC,SAAR,CAAkBsG,IAAlB,CAAuBC,MAAvD,EAA+D;AAC9E,cAAI,KAAKvF,SAAL,CAAeuE,SAAnB,EAA8B;AAC1B,iBAAKvE,SAAL,CAAeuE,SAAf,CAAyBzE,IAAzB,GAAgCA,IAAhC;AACH;AACJ;;AAEO2B,QAAAA,YAAY,GAAG;AACnB;AACA,cAAI,CAAC,KAAKzB,SAAL,CAAewB,YAApB,EAAkC;AAC9B;AAAA;AAAA,8BAAKH,GAAL,CAASC,QAAT,CAAmB,OAAM,KAAKjB,IAAL,CAAUkB,IAAK,+BAAxC;AAEA;AACH;;AAED,cAAIiE,KAAK,GAAG,KAAKnF,IAAL,CAAUO,YAAV,CAAuB5B,SAAvB,CAAZ;;AACA,cAAI,CAACwG,KAAL,EAAY;AACR;AAAA;AAAA,8BAAKnE,GAAL,CAASC,QAAT,CAAmB,OAAM,KAAKjB,IAAL,CAAUkB,IAAK,wBAAxC;AACA;AACH;;AACDiE,UAAAA,KAAK,CAAChB,KAAN,GAAc;AAAA;AAAA,sCAAUiB,IAAxB;AACAD,UAAAA,KAAK,CAACE,UAAN,GAAmB,KAAnB;AACA,eAAK1F,SAAL,CAAeuE,SAAf,GAA2BiB,KAA3B,CAfmB,CAiBnB;;AACA,gBAAMG,IAAI,GAAG,KAAK3B,cAAL,EAAb;;AACA,cAAI2B,IAAJ,EAAU;AACN,kBAAMC,WAAW,GAAGD,IAAI,CAACC,WAAzB;AACA,kBAAMC,MAAM,GAAG,IAAID,WAAW,CAACE,CAAhB,GAAoBF,WAAW,CAACG,CAAhC,GAAoCH,WAAW,CAACI,CAA/D,CAFM,CAE4D;;AAClER,YAAAA,KAAK,CAACS,IAAN,GAAaJ,MAAb,CAHM,CAGe;AACxB,WAJD,MAIO;AACHL,YAAAA,KAAK,CAACS,IAAN,GAAa,CAAb,CADG,CACa;AACnB;;AAED;AACH;;AAEDC,QAAAA,OAAO,GAAG;AACN;AACA,cAAI,CAAC,KAAKlG,SAAV,EAAqB;AACjB;AAAA;AAAA,8BAAKqB,GAAL,CAASC,QAAT,CAAmB,MAAK,KAAKjB,IAAL,CAAUkB,IAAK,yBAAvC;AACA;AACH;;AAED,eAAKvB,SAAL,CAAemG,QAAf,GAA0B,IAA1B;AACA,eAAKvC,eAAL,GARM,CAUN;;AACA,eAAKlC,eAAL;AAEA,eAAKzB,QAAL,CAAciB,OAAd,CAAsBC,CAAC,IAAIA,CAAC,CAACiF,EAAF,CAAK,iBAAL,EAAwB,KAAKC,cAA7B,EAA6C,IAA7C,CAA3B;AACH;;AAED3B,QAAAA,aAAa,GAAG;AAAA;;AACZ;AACA,cAAI,CAAC,KAAK1E,SAAV,EAAqB;AACjB;AAAA;AAAA,8BAAKqB,GAAL,CAASiF,OAAT,CAAkB,OAAM,KAAKjG,IAAL,CAAUkB,IAAK,+BAAvC;AACA;AACH;;AAED,eAAKvB,SAAL,CAAemG,QAAf,GAA0B,KAA1B,CAPY,CASZ;;AACA,eAAK7C,gBAAL;AAEA,yCAAKjD,IAAL,CAAUO,YAAV,CAAuB3B,iBAAvB,qCAA2CsH,IAA3C;AACA,eAAKrG,iBAAL,GAAyB,EAAzB;AACA,eAAKD,QAAL,CAAciB,OAAd,CAAsBC,CAAC,IAAIA,CAAC,CAACqF,GAAF,CAAM,iBAAN,EAAyB,KAAKH,cAA9B,EAA8C,IAA9C,CAA3B;AACH;;AAEDA,QAAAA,cAAc,CAACI,KAAD,EAAuB;AACjC,cAAIC,SAAS,GAAGD,KAAK,CAACE,aAAN,CAAoBtG,IAApC;AACA,gBAAMkE,SAAS,GAAGkC,KAAK,CAACE,aAAN,CAAoBtG,IAApB,CAAyBO,YAAzB,CAAsC5B,SAAtC,CAAlB;;AACA,cACI,KAAKqB,IAAL,KAAcqG,SAAd,IACA,CAAAnC,SAAS,QAAT,YAAAA,SAAS,CAAEC,KAAX,KAAoB;AAAA;AAAA,sCAAUiB,IAD9B,IAEA,KAAKvF,iBAAL,CAAuB0G,OAAvB,CAA+BF,SAA/B,KAA6C,CAAC,CAHlD,EAIE;AACE,iBAAKxG,iBAAL,CAAuBkB,IAAvB,CAA4BsF,SAA5B,EADF,CAGE;;AACA,gBAAIG,OAAO,GAAG,KAAKxG,IAAL,CAAUyG,gBAAV,EAAd;AACA,kBAAMC,SAAS,GAAGL,SAAS,CAACI,gBAAV,GAA6BE,QAA7B,CAAsCH,OAAtC,EAA+CI,SAA/C,EAAlB,CALF,CAOE;;AACA,kBAAMC,QAAQ,GAAG,GAAjB,CARF,CAQwB;;AACtB,kBAAMC,SAAS,GAAGT,SAAS,CAACI,gBAAV,GAA6BM,GAA7B,CAAiCL,SAAS,CAACM,cAAV,CAAyBH,QAAzB,CAAjC,CAAlB,CATF,CAWE;;AACAhI,YAAAA,KAAK,CAACwH,SAAD,CAAL,CACKY,EADL,CACQ,GADR,EACa;AAAEC,cAAAA,QAAQ,EAAEJ;AAAZ,aADb,EACsC;AADtC,aAEK3G,KAFL;AAGH;AACJ;;AAvQyC,O;;;;;iBAYX,I;;;;;;;iBAGA,I", "sourcesContent": ["import {\n    _decorator,\n    AudioClip,\n    Collider,\n    geometry,\n    ITriggerEvent,\n    Material,\n    MeshRenderer,\n    Node,\n    physics,\n    RigidBody,\n    SkeletalAnimation,\n    tween,\n} from 'cc';\n\nimport { ClientConst, MusicConf, PHY_GROUP } from '../../common/ClientConst';\n\nimport { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { CCComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp';\nimport { smc } from '../../common/SingletonModuleComp';\n\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PickBox } from '../../prefab/PickBox';\nimport { ItemEntity } from '../ItemEntity';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('ItemSceneView')\**************('ItemSceneView')\nexport class ItemSceneViewComp extends CCComp {\n    ent!: ItemEntity;\n    ItemModel!: typeof this.ent.ItemModel;\n\n    collider: Collider[] = []; // 所有的碰撞体，包括子节点的碰撞体\n    otherColliderNode: Node[] = [];\n\n    // 🌟 简化材质系统 - 直接使用共享材质\n    private isGlowing: boolean = false; // 当前是否在发光\n\n    @property({ type: AudioClip })\n    // 选择音效\n    pickEffect: AudioClip | null = null;\n    @property({ type: AudioClip })\n    // 闲置音效\n    idleEffect: AudioClip | null = null;\n\n    reset() {\n        this.node.parent = null;\n        this.node.destroy();\n    }\n\n    start() {\n        this.initializeComponent();\n        this.collider = this.node.getComponents(Collider);\n        const comp = this.node.getComponent(RigidBody);\n        if (comp) {\n            comp.linearDamping = ClientConst.LinearDamping;\n            comp.angularDamping = ClientConst.AngularDamping;\n        }\n        if (this.collider) {\n            // 追加子节点的collider\n            this.node.children.forEach(a => {\n                const collider = a.getComponent(Collider);\n                if (collider) {\n                    this.collider.push(collider);\n                }\n            });\n        }\n    }\n\n    /**\n     * 🎯 组件初始化逻辑（可重复调用）\n     */\n    initializeComponent(): void {\n        if (!this.ent || !this.ent.ItemModel) {\n            oops.log.logError(`❌ [${this.node.name}] 无法初始化：ent或ItemModel仍未设置`);\n            return;\n        }\n\n        this.ItemModel = this.ent.ItemModel;\n        const meshRenderer = this.node.getComponent(MeshRenderer);\n        if (meshRenderer) {\n            this.ItemModel.meshRenderer = meshRenderer;\n        }\n        this.addRigidBody();\n    }\n\n    // 🌟 发光效果：创建材质实例（基于testSceneComp策略）\n    private applyGlowEffect(): void {\n        if (!this.ItemModel.meshRenderer || this.isGlowing) return;\n        let interactionManager = smc.sceneMgr.getCurrentGameEntity().interactionManager;\n        let fromMaterial = interactionManager.sharedMaterial;\n        if (!fromMaterial) {\n            oops.log.logError(`❌ [${this.node.name}] 无法初始化：fromMaterial 材质未设置`);\n            return;\n        }\n        //目前只有到一个材质。\n        let rimLightXrayPbrMaterial = interactionManager.rimLightXrayPbrMaterial;\n        this.copyMaterialProperties(fromMaterial, rimLightXrayPbrMaterial);\n        this.ItemModel.meshRenderer.setSharedMaterial(rimLightXrayPbrMaterial, 0);\n        this.ItemModel.meshRenderer.setInstancedAttribute('a_instanced_rimStrength', [2.0]);\n        this.isGlowing = true;\n    }\n\n    // 🎨 安全的材质属性复制方法 - 确保不破坏GPU Instancing合批\n    private copyMaterialProperties(fromMaterial: Material, toMaterial: Material): void {\n        try {\n            // ✅ 复制纹理属性（只读取引用，不创建新实例）\n            const normalMap = fromMaterial.getProperty('normalMap') as any;\n            const pbrMap = fromMaterial.getProperty('pbrMap') as any;\n            const albedoMap = fromMaterial.getProperty('albedoMap') as any;\n\n            if (normalMap) toMaterial.setProperty('normalMap', normalMap);\n            if (pbrMap) toMaterial.setProperty('pbrMap', pbrMap);\n            if (albedoMap) toMaterial.setProperty('albedoMap', albedoMap);\n\n            // 🎨 复制基础颜色和强度属性 - 保持物品原始外观\n            const albedoColor = fromMaterial.getProperty('albedo') as any;\n            const albedoScale = fromMaterial.getProperty('albedoScale') as any;\n            const emissive = fromMaterial.getProperty('emissive') as any;\n            const emissiveScale = fromMaterial.getProperty('emissiveScale') as any;\n            const normalStrength = fromMaterial.getProperty('normalStrength') as any;\n\n            // 🎯 复制PBR相关属性\n            const metallicFactor = fromMaterial.getProperty('metallicFactor') as any;\n            const roughnessFactor = fromMaterial.getProperty('roughnessFactor') as any;\n            const occlusionStrength = fromMaterial.getProperty('occlusionStrength') as any;\n\n            // 🌟 设置基础材质属性 - 保持物品原色\n            if (albedoColor !== undefined) toMaterial.setProperty('albedo', albedoColor);\n            if (albedoScale !== undefined) toMaterial.setProperty('albedoScale', albedoScale);\n            if (emissive !== undefined) toMaterial.setProperty('emissive', emissive);\n            if (emissiveScale !== undefined) toMaterial.setProperty('emissiveScale', emissiveScale);\n            if (normalStrength !== undefined)\n                toMaterial.setProperty('normalStrength', normalStrength);\n            if (metallicFactor !== undefined)\n                toMaterial.setProperty('metallicFactor', metallicFactor);\n            if (roughnessFactor !== undefined)\n                toMaterial.setProperty('roughnessFactor', roughnessFactor);\n            if (occlusionStrength !== undefined)\n                toMaterial.setProperty('occlusionStrength', occlusionStrength);\n        } catch (error) {\n            console.warn('⚠️ 材质属性复制失败:', error);\n        }\n    }\n\n    // 🌟 移除发光效果：恢复到共享材质\n    private removeGlowEffect(): void {\n        if (!this.isGlowing || !this.ItemModel.meshRenderer) return;\n        let interactionManager = smc.sceneMgr.getCurrentGameEntity().interactionManager;\n        let sharedMaterial = interactionManager.sharedMaterial;\n        this.ItemModel.meshRenderer.setSharedMaterial(sharedMaterial, 0);\n        this.isGlowing = false;\n    }\n\n    setShadow(cast: boolean, receive: boolean) {\n        if (this.ItemModel.meshRenderer) {\n            this.ItemModel.meshRenderer.shadowCastingMode = cast ? 1 : 0;\n            this.ItemModel.meshRenderer.receiveShadow = receive ? 1 : 0;\n        }\n    }\n\n    doSkelAnimation() {\n        this.node.getComponent(SkeletalAnimation)?.play();\n    }\n\n    getItemId() {\n        return this.ItemModel.itemId;\n    }\n\n    //获取包围盒 只获取第一个mesh的\n    getBoundingBox(): geometry.AABB | undefined {\n        const meshRenderer = this.ItemModel.meshRenderer;\n        if (meshRenderer) {\n            return meshRenderer.model?.worldBounds!;\n        }\n    }\n\n    doOnMove(pickBoxComp: PickBox) {\n        this.ItemModel.pickBox = pickBoxComp;\n        // 🎯 让PickBox也记录当前物品\n        pickBoxComp.pickItem(this.node);\n        if (this.ItemModel.rigidBody) {\n            this.ItemModel.rigidBody.group = PHY_GROUP.ITEM_BOX;\n        }\n        this.onCancelTouch();\n    }\n\n    playEffectOnChoose() {\n        oops.audio.playEffect(this.pickEffect || MusicConf.commonPickEffect);\n    }\n\n    set enableRigidBody(b: boolean) {\n        if (this.ItemModel.rigidBody) {\n            this.ItemModel.rigidBody.enabled = b;\n            this.ItemModel.rigidBody.useGravity = b;\n        }\n        this.collider.map(a => (a.enabled = b));\n    }\n\n    changeRigidBodyType(type: physics.RigidBody.Type = physics.RigidBody.Type.STATIC) {\n        if (this.ItemModel.rigidBody) {\n            this.ItemModel.rigidBody.type = type;\n        }\n    }\n\n    private addRigidBody() {\n        // 🎯 安全检查：确保meshRenderer存在\n        if (!this.ItemModel.meshRenderer) {\n            oops.log.logError(`⚠️ [${this.node.name}] addRigidBody时meshRenderer为空`);\n\n            return;\n        }\n\n        let rigid = this.node.getComponent(RigidBody);\n        if (!rigid) {\n            oops.log.logError(`⚠️ [${this.node.name}] addRigidBody时rigid为空`);\n            return;\n        }\n        rigid.group = PHY_GROUP.ITEM;\n        rigid.allowSleep = false;\n        this.ItemModel.rigidBody = rigid;\n\n        // 根据模型的aabb大小设置质量\n        const aabb = this.getBoundingBox();\n        if (aabb) {\n            const halfExtents = aabb.halfExtents;\n            const volume = 8 * halfExtents.x * halfExtents.y * halfExtents.z; // 体积 = 2 * hw * 2 * hh * 2 * hl\n            rigid.mass = volume; // 假设密度为1\n        } else {\n            rigid.mass = 1; // 默认质量\n        }\n\n        return;\n    }\n\n    onTouch() {\n        // 🎯 安全检查：确保ItemModel已正确初始化\n        if (!this.ItemModel) {\n            oops.log.logError(`❌ [${this.node.name}] onTouch时ItemModel未初始化`);\n            return;\n        }\n\n        this.ItemModel.touching = true;\n        this.doSkelAnimation();\n\n        // 🌟 简化发光逻辑：直接应用预加载材质\n        this.applyGlowEffect();\n\n        this.collider.forEach(a => a.on('onCollisionStay', this.onTriggerEnter, this));\n    }\n\n    onCancelTouch() {\n        // 🎯 安全检查：确保ItemModel已正确初始化\n        if (!this.ItemModel) {\n            oops.log.logWarn(`⚠️ [${this.node.name}] onCancelTouch时ItemModel未初始化`);\n            return;\n        }\n\n        this.ItemModel.touching = false;\n\n        // 🌟 简化材质恢复\n        this.removeGlowEffect();\n\n        this.node.getComponent(SkeletalAnimation)?.stop();\n        this.otherColliderNode = [];\n        this.collider.forEach(a => a.off('onCollisionStay', this.onTriggerEnter, this));\n    }\n\n    onTriggerEnter(event: ITriggerEvent) {\n        let otherNode = event.otherCollider.node;\n        const rigidBody = event.otherCollider.node.getComponent(RigidBody);\n        if (\n            this.node !== otherNode &&\n            rigidBody?.group == PHY_GROUP.ITEM &&\n            this.otherColliderNode.indexOf(otherNode) == -1\n        ) {\n            this.otherColliderNode.push(otherNode);\n\n            // 计算从当前节点到其他节点的方向向量\n            let thisPos = this.node.getWorldPosition();\n            const direction = otherNode.getWorldPosition().subtract(thisPos).normalize();\n\n            // 计算目标位置，远离当前节点\n            const distance = 0.1; // 移动的距离，可以根据需要调整\n            const targetPos = otherNode.getWorldPosition().add(direction.multiplyScalar(distance));\n\n            // 使用 tween 动画移动其他节点\n            tween(otherNode)\n                .to(0.2, { position: targetPos }) // 0.5 秒内移动到目标位置\n                .start();\n        }\n    }\n}\n"]}