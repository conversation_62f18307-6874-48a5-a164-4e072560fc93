System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, PhysicsSystem, PHY_GROUP, smc, ItemSceneViewComp, _dec, _class, _crd, ccclass, SimpleItemDetector;

  function _reportPossibleCrUseOfPHY_GROUP(extras) {
    _reporterNs.report("PHY_GROUP", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemSceneViewComp(extras) {
    _reporterNs.report("ItemSceneViewComp", "../../item/view/ItemSceneViewComp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      PhysicsSystem = _cc.PhysicsSystem;
    }, function (_unresolved_2) {
      PHY_GROUP = _unresolved_2.PHY_GROUP;
    }, function (_unresolved_3) {
      smc = _unresolved_3.smc;
    }, function (_unresolved_4) {
      ItemSceneViewComp = _unresolved_4.ItemSceneViewComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "13f86vFsXNJmoibPPSgCd+G", "SimpleItemDetector", undefined);

      __checkObsolete__(['_decorator', 'EventTouch', 'PhysicsSystem']);

      ({
        ccclass
      } = _decorator);
      /**
       * 🎯 简化物品检测器 - 专注精确检测可见物品
       *
       * 核心原则：
       * 1. 看到什么就能摸到什么
       * 2. 总是检测最前面的可见物品
       * 3. 不需要复杂的层级切换
       */

      _export("SimpleItemDetector", SimpleItemDetector = (_dec = ccclass('SimpleItemDetector'), _dec(_class = class SimpleItemDetector {
        /**
         * 🎯 精确检测触摸位置的物品
         */
        static detectItemAtPosition(event) {
          const camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;

          if (!camera) {
            return null;
          }

          const currentX = event.getLocationX();
          const currentY = event.getLocationY(); // 🎯 执行单次精确射线检测

          return this.performSingleRaycast(currentX, currentY);
        }
        /**
         * 🎯 执行单次射线检测 - 只检测最前面的物品
         */


        static performSingleRaycast(x, y) {
          const camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;
          const ray = camera.screenPointToRay(x, y);
          const raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA; // 🎯 只执行一次射线检测，获取最前面的物品

          if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
            const result = PhysicsSystem.instance.raycastClosestResult;
            const hitNode = result.collider.node;
            const itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
              error: Error()
            }), ItemSceneViewComp) : ItemSceneViewComp);

            if (itemComp && itemComp.ItemModel) {
              var _itemComp$node;

              console.log(`🎯 检测到物品: ${((_itemComp$node = itemComp.node) == null ? void 0 : _itemComp$node.name) || '未知'}`);
              return itemComp;
            }
          }

          return null;
        }
        /**
         * 🎯 扩展检测 - 使用多个检测点提高精确度
         * 适用于模型边缘或小模型的情况
         */


        static detectItemWithExpansion(event, radius = 5) {
          const camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;

          if (!camera) {
            return null;
          }

          const centerX = event.getLocationX();
          const centerY = event.getLocationY(); // 🎯 检测点：中心 + 四个方向

          const points = [{
            x: centerX,
            y: centerY
          }, // 中心点
          {
            x: centerX - radius,
            y: centerY
          }, // 左
          {
            x: centerX + radius,
            y: centerY
          }, // 右
          {
            x: centerX,
            y: centerY - radius
          }, // 上
          {
            x: centerX,
            y: centerY + radius
          } // 下
          ]; // 🎯 按优先级检测：中心点优先

          for (const point of points) {
            const item = this.performSingleRaycast(point.x, point.y);

            if (item) {
              return item;
            }
          }

          return null;
        }
        /**
         * 🎯 获取触摸位置的所有堆叠物品（用于调试）
         */


        static getAllItemsAtPosition(event) {
          const camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;

          if (!camera) {
            return [];
          }

          const currentX = event.getLocationX();
          const currentY = event.getLocationY();
          const ray = camera.screenPointToRay(currentX, currentY);
          const raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA;
          const allItems = [];
          const maxLayers = 3; // 最多检测3层

          const disabledColliders = [];

          try {
            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (let layer = 0; layer < maxLayers; layer++) {
              if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                const result = PhysicsSystem.instance.raycastClosestResult;
                const hitNode = result.collider.node;
                const itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                  error: Error()
                }), ItemSceneViewComp) : ItemSceneViewComp);

                if (itemComp && itemComp.ItemModel) {
                  // 避免重复添加同一个物品
                  if (!allItems.find(item => item.node === hitNode)) {
                    allItems.push(itemComp);
                  } // 临时禁用这个碰撞体，以便检测下一层


                  const collider = result.collider;

                  if (collider.enabled) {
                    collider.enabled = false;
                    disabledColliders.push(collider);
                  }
                } else {
                  break; // 没有找到有效物品，停止检测
                }
              } else {
                break; // 没有更多碰撞，停止检测
              }
            }
          } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
              if (collider && collider.isValid) {
                collider.enabled = true;
              }
            });
          }

          if (allItems.length > 1) {
            console.log(`🔍 检测到${allItems.length}个堆叠物品:`, allItems.map(item => {
              var _item$node;

              return (_item$node = item.node) == null ? void 0 : _item$node.name;
            }));
          }

          return allItems;
        }
        /**
         * 🎯 检测方法选择器 - 根据需求选择合适的检测方法
         */


        static detectItem(event, method = 'precise') {
          switch (method) {
            case 'precise':
              return this.detectItemAtPosition(event);

            case 'expanded':
              return this.detectItemWithExpansion(event, 8);

            case 'debug':
              const allItems = this.getAllItemsAtPosition(event);
              return allItems[0] || null;

            default:
              return this.detectItemAtPosition(event);
          }
        }

      }) || _class)); // 暴露到全局，便于调试


      window.SimpleItemDetector = SimpleItemDetector;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6b3677a6b69aef42667f91577f72260c4ef99c00.js.map