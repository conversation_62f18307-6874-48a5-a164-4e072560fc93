{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/SimpleItemDetector.ts"], "names": ["_decorator", "PhysicsSystem", "PHY_GROUP", "smc", "ItemSceneViewComp", "ccclass", "SimpleItemDetector", "detectItemAtPosition", "event", "camera", "CameraModel", "currentX", "getLocationX", "currentY", "getLocationY", "performSingleRaycast", "x", "y", "ray", "screenPointToRay", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "instance", "raycastClosest", "result", "raycastClosestResult", "hitNode", "collider", "node", "itemComp", "getComponent", "ItemModel", "console", "log", "name", "detectItemWithExpansion", "radius", "centerX", "centerY", "points", "point", "item", "getAllItemsAtPosition", "allItems", "maxLayers", "disabledColliders", "layer", "find", "push", "enabled", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "length", "map", "detectItem", "method", "window"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAwBC,MAAAA,a,OAAAA,a;;AACxBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcL,U;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;oCAEaM,kB,WADZD,OAAO,CAAC,oBAAD,C,gBAAR,MACaC,kBADb,CACgC;AAC5B;AACJ;AACA;AAC+B,eAApBC,oBAAoB,CAACC,KAAD,EAA8C;AACrE,gBAAMC,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,gBAAME,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAPqE,CASrE;;AACA,iBAAO,KAAKC,oBAAL,CAA0BJ,QAA1B,EAAoCE,QAApC,CAAP;AACH;AAED;AACJ;AACA;;;AACuC,eAApBE,oBAAoB,CAACC,CAAD,EAAYC,CAAZ,EAAiD;AAChF,gBAAMR,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,gBAAMS,GAAG,GAAGT,MAAM,CAACU,gBAAP,CAAwBH,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAMG,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE,CAHgF,CAKhF;;AACA,cAAItB,aAAa,CAACuB,QAAd,CAAuBC,cAAvB,CAAsCP,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,kBAAMM,MAAM,GAAGzB,aAAa,CAACuB,QAAd,CAAuBG,oBAAtC;AACA,kBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgBC,IAAhC;AACA,kBAAMC,QAAQ,GAAGH,OAAO,CAACI,YAAR;AAAA;AAAA,uDAAjB;;AAEA,gBAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAzB,EAAoC;AAAA;;AAChCC,cAAAA,OAAO,CAACC,GAAR,CAAa,aAAY,mBAAAJ,QAAQ,CAACD,IAAT,oCAAeM,IAAf,KAAuB,IAAK,EAArD;AACA,qBAAOL,QAAP;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACkC,eAAvBM,uBAAuB,CAC1B7B,KAD0B,EAE1B8B,MAAc,GAAG,CAFS,EAGF;AACxB,gBAAM7B,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,gBAAM8B,OAAO,GAAG/B,KAAK,CAACI,YAAN,EAAhB;AACA,gBAAM4B,OAAO,GAAGhC,KAAK,CAACM,YAAN,EAAhB,CAPwB,CASxB;;AACA,gBAAM2B,MAAM,GAAG,CACX;AAAEzB,YAAAA,CAAC,EAAEuB,OAAL;AAActB,YAAAA,CAAC,EAAEuB;AAAjB,WADW,EACiB;AAC5B;AAAExB,YAAAA,CAAC,EAAEuB,OAAO,GAAGD,MAAf;AAAuBrB,YAAAA,CAAC,EAAEuB;AAA1B,WAFW,EAE0B;AACrC;AAAExB,YAAAA,CAAC,EAAEuB,OAAO,GAAGD,MAAf;AAAuBrB,YAAAA,CAAC,EAAEuB;AAA1B,WAHW,EAG0B;AACrC;AAAExB,YAAAA,CAAC,EAAEuB,OAAL;AAActB,YAAAA,CAAC,EAAEuB,OAAO,GAAGF;AAA3B,WAJW,EAI0B;AACrC;AAAEtB,YAAAA,CAAC,EAAEuB,OAAL;AAActB,YAAAA,CAAC,EAAEuB,OAAO,GAAGF;AAA3B,WALW,CAK0B;AAL1B,WAAf,CAVwB,CAkBxB;;AACA,eAAK,MAAMI,KAAX,IAAoBD,MAApB,EAA4B;AACxB,kBAAME,IAAI,GAAG,KAAK5B,oBAAL,CAA0B2B,KAAK,CAAC1B,CAAhC,EAAmC0B,KAAK,CAACzB,CAAzC,CAAb;;AACA,gBAAI0B,IAAJ,EAAU;AACN,qBAAOA,IAAP;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACgC,eAArBC,qBAAqB,CAACpC,KAAD,EAAyC;AACjE,gBAAMC,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,EAAP;AACH;;AAED,gBAAME,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB;AACA,gBAAMI,GAAG,GAAGT,MAAM,CAACU,gBAAP,CAAwBR,QAAxB,EAAkCE,QAAlC,CAAZ;AACA,gBAAMO,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,gBAAMsB,QAA6B,GAAG,EAAtC;AACA,gBAAMC,SAAS,GAAG,CAAlB,CAZiE,CAY5C;;AACrB,gBAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AACA;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,SAA5B,EAAuCE,KAAK,EAA5C,EAAgD;AAC5C,kBAAI/C,aAAa,CAACuB,QAAd,CAAuBC,cAAvB,CAAsCP,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,sBAAMM,MAAM,GAAGzB,aAAa,CAACuB,QAAd,CAAuBG,oBAAtC;AACA,sBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgBC,IAAhC;AACA,sBAAMC,QAAQ,GAAGH,OAAO,CAACI,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACY,QAAQ,CAACI,IAAT,CAAcN,IAAI,IAAIA,IAAI,CAACb,IAAL,KAAcF,OAApC,CAAL,EAAmD;AAC/CiB,oBAAAA,QAAQ,CAACK,IAAT,CAAcnB,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,wBAAMF,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACsB,OAAb,EAAsB;AAClBtB,oBAAAA,QAAQ,CAACsB,OAAT,GAAmB,KAAnB;AACAJ,oBAAAA,iBAAiB,CAACG,IAAlB,CAAuBrB,QAAvB;AACH;AACJ,iBAZD,MAYO;AACH,wBADG,CACI;AACV;AACJ,eApBD,MAoBO;AACH,sBADG,CACI;AACV;AACJ;AACJ,WA3BD,SA2BU;AACN;AACAkB,YAAAA,iBAAiB,CAACK,OAAlB,CAA0BvB,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACwB,OAAzB,EAAkC;AAC9BxB,gBAAAA,QAAQ,CAACsB,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH;;AAED,cAAIN,QAAQ,CAACS,MAAT,GAAkB,CAAtB,EAAyB;AACrBpB,YAAAA,OAAO,CAACC,GAAR,CACK,SAAQU,QAAQ,CAACS,MAAO,QAD7B,EAEIT,QAAQ,CAACU,GAAT,CAAaZ,IAAI;AAAA;;AAAA,mCAAIA,IAAI,CAACb,IAAT,qBAAI,WAAWM,IAAf;AAAA,aAAjB,CAFJ;AAIH;;AAED,iBAAOS,QAAP;AACH;AAED;AACJ;AACA;;;AACqB,eAAVW,UAAU,CACbhD,KADa,EAEbiD,MAAwC,GAAG,SAF9B,EAGW;AACxB,kBAAQA,MAAR;AACI,iBAAK,SAAL;AACI,qBAAO,KAAKlD,oBAAL,CAA0BC,KAA1B,CAAP;;AACJ,iBAAK,UAAL;AACI,qBAAO,KAAK6B,uBAAL,CAA6B7B,KAA7B,EAAoC,CAApC,CAAP;;AACJ,iBAAK,OAAL;AACI,oBAAMqC,QAAQ,GAAG,KAAKD,qBAAL,CAA2BpC,KAA3B,CAAjB;AACA,qBAAOqC,QAAQ,CAAC,CAAD,CAAR,IAAe,IAAtB;;AACJ;AACI,qBAAO,KAAKtC,oBAAL,CAA0BC,KAA1B,CAAP;AATR;AAWH;;AA9J2B,O,eAiKhC;;;AACCkD,MAAAA,MAAD,CAAgBpD,kBAAhB,GAAqCA,kBAArC", "sourcesContent": ["import { _decorator, EventTouch, PhysicsSystem } from 'cc';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\n\nconst { ccclass } = _decorator;\n\n/**\n * 🎯 简化物品检测器 - 专注精确检测可见物品\n *\n * 核心原则：\n * 1. 看到什么就能摸到什么\n * 2. 总是检测最前面的可见物品\n * 3. 不需要复杂的层级切换\n */\n@ccclass('SimpleItemDetector')\nexport class SimpleItemDetector {\n    /**\n     * 🎯 精确检测触摸位置的物品\n     */\n    static detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 执行单次精确射线检测\n        return this.performSingleRaycast(currentX, currentY);\n    }\n\n    /**\n     * 🎯 执行单次射线检测 - 只检测最前面的物品\n     */\n    private static performSingleRaycast(x: number, y: number): ItemSceneViewComp | null {\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        // 🎯 只执行一次射线检测，获取最前面的物品\n        if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n            const result = PhysicsSystem.instance.raycastClosestResult;\n            const hitNode = result.collider.node;\n            const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n            if (itemComp && itemComp.ItemModel) {\n                console.log(`🎯 检测到物品: ${itemComp.node?.name || '未知'}`);\n                return itemComp;\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * 🎯 扩展检测 - 使用多个检测点提高精确度\n     * 适用于模型边缘或小模型的情况\n     */\n    static detectItemWithExpansion(\n        event: EventTouch,\n        radius: number = 5\n    ): ItemSceneViewComp | null {\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const centerX = event.getLocationX();\n        const centerY = event.getLocationY();\n\n        // 🎯 检测点：中心 + 四个方向\n        const points = [\n            { x: centerX, y: centerY }, // 中心点\n            { x: centerX - radius, y: centerY }, // 左\n            { x: centerX + radius, y: centerY }, // 右\n            { x: centerX, y: centerY - radius }, // 上\n            { x: centerX, y: centerY + radius }, // 下\n        ];\n\n        // 🎯 按优先级检测：中心点优先\n        for (const point of points) {\n            const item = this.performSingleRaycast(point.x, point.y);\n            if (item) {\n                return item;\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * 🎯 获取触摸位置的所有堆叠物品（用于调试）\n     */\n    static getAllItemsAtPosition(event: EventTouch): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return [];\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n        const ray = camera.screenPointToRay(currentX, currentY);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        if (allItems.length > 1) {\n            console.log(\n                `🔍 检测到${allItems.length}个堆叠物品:`,\n                allItems.map(item => item.node?.name)\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 检测方法选择器 - 根据需求选择合适的检测方法\n     */\n    static detectItem(\n        event: EventTouch,\n        method: 'precise' | 'expanded' | 'debug' = 'precise'\n    ): ItemSceneViewComp | null {\n        switch (method) {\n            case 'precise':\n                return this.detectItemAtPosition(event);\n            case 'expanded':\n                return this.detectItemWithExpansion(event, 8);\n            case 'debug':\n                const allItems = this.getAllItemsAtPosition(event);\n                return allItems[0] || null;\n            default:\n                return this.detectItemAtPosition(event);\n        }\n    }\n}\n\n// 暴露到全局，便于调试\n(window as any).SimpleItemDetector = SimpleItemDetector;\n"]}