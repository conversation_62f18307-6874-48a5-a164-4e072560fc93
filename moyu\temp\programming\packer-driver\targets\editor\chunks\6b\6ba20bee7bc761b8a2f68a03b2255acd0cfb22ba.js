System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, oops, _dec, _class, _class2, _crd, ccclass, property, RaycastPerformanceMonitor;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "09a19oes+dMmr3NkcFZaCNg", "RaycastPerformanceMonitor", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 🎯 射线检测性能监控器
       * 用于监控和优化射线检测的性能表现
       */

      _export("RaycastPerformanceMonitor", RaycastPerformanceMonitor = (_dec = ccclass('RaycastPerformanceMonitor'), _dec(_class = (_class2 = class RaycastPerformanceMonitor extends Component {
        constructor(...args) {
          super(...args);
          // 性能统计数据
          this.raycastCount = 0;
          this.raycastTotalTime = 0;
          this.raycastMaxTime = 0;
          this.raycastMinTime = Number.MAX_VALUE;
          this.lastResetTime = 0;
          // 缓存统计
          this.cacheHits = 0;
          this.cacheMisses = 0;
          // 监控配置
          this.monitoringEnabled = true;
          this.reportInterval = 5000;
          // 5秒报告一次
          this.performanceThreshold = 2;
        }

        // 2ms阈值
        onLoad() {
          RaycastPerformanceMonitor.instance = this;
          this.lastResetTime = Date.now(); // 定期报告性能数据

          this.schedule(this.reportPerformance.bind(this), this.reportInterval / 1000);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 射线检测性能监控器已启动');
        }

        onDestroy() {
          RaycastPerformanceMonitor.instance = null;
          this.unscheduleAllCallbacks();
        }
        /**
         * 获取单例实例
         */


        static getInstance() {
          return RaycastPerformanceMonitor.instance;
        }
        /**
         * 🎯 记录射线检测开始
         */


        static startRaycast() {
          const instance = RaycastPerformanceMonitor.getInstance();

          if (!instance || !instance.monitoringEnabled) {
            return 0;
          }

          return performance.now();
        }
        /**
         * 🎯 记录射线检测结束
         */


        static endRaycast(startTime, hitResult = false) {
          const instance = RaycastPerformanceMonitor.getInstance();

          if (!instance || !instance.monitoringEnabled || startTime === 0) {
            return;
          }

          const endTime = performance.now();
          const duration = endTime - startTime;
          instance.raycastCount++;
          instance.raycastTotalTime += duration;
          instance.raycastMaxTime = Math.max(instance.raycastMaxTime, duration);
          instance.raycastMinTime = Math.min(instance.raycastMinTime, duration); // 如果射线检测时间超过阈值，记录警告

          if (duration > instance.performanceThreshold) {
            console.warn(`⚠️ 射线检测耗时过长: ${duration.toFixed(2)}ms (阈值: ${instance.performanceThreshold}ms)`);
          }
        }
        /**
         * 🎯 记录缓存命中
         */


        static recordCacheHit() {
          const instance = RaycastPerformanceMonitor.getInstance();

          if (instance) {
            instance.cacheHits++;
          }
        }
        /**
         * 🎯 记录缓存未命中
         */


        static recordCacheMiss() {
          const instance = RaycastPerformanceMonitor.getInstance();

          if (instance) {
            instance.cacheMisses++;
          }
        }
        /**
         * 🎯 生成性能报告
         */


        reportPerformance() {
          if (this.raycastCount === 0) {
            return;
          }

          const currentTime = Date.now();
          const timeElapsed = (currentTime - this.lastResetTime) / 1000;
          const avgTime = this.raycastTotalTime / this.raycastCount;
          const raycastsPerSecond = this.raycastCount / timeElapsed;
          const cacheHitRate = this.cacheHits / (this.cacheHits + this.cacheMisses) * 100;
          const report = {
            '📊 射线检测统计': {
              '总次数': this.raycastCount,
              '平均耗时': `${avgTime.toFixed(2)}ms`,
              '最大耗时': `${this.raycastMaxTime.toFixed(2)}ms`,
              '最小耗时': `${this.raycastMinTime.toFixed(2)}ms`,
              '每秒检测次数': raycastsPerSecond.toFixed(1),
              '缓存命中率': `${cacheHitRate.toFixed(1)}%`,
              '缓存命中次数': this.cacheHits,
              '缓存未命中次数': this.cacheMisses
            }
          };
          console.log('🎯 射线检测性能报告:', report); // 性能建议

          if (avgTime > this.performanceThreshold) {
            console.warn(`⚠️ 射线检测平均耗时过高: ${avgTime.toFixed(2)}ms，建议优化`);
          }

          if (raycastsPerSecond > 30) {
            console.warn(`⚠️ 射线检测频率过高: ${raycastsPerSecond.toFixed(1)}/s，建议增加节流`);
          }

          if (cacheHitRate < 50) {
            console.warn(`⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`);
          } // 重置统计数据


          this.resetStats();
        }
        /**
         * 🎯 重置统计数据
         */


        resetStats() {
          this.raycastCount = 0;
          this.raycastTotalTime = 0;
          this.raycastMaxTime = 0;
          this.raycastMinTime = Number.MAX_VALUE;
          this.cacheHits = 0;
          this.cacheMisses = 0;
          this.lastResetTime = Date.now();
        }
        /**
         * 🎯 启用/禁用监控
         */


        static setMonitoringEnabled(enabled) {
          const instance = RaycastPerformanceMonitor.getInstance();

          if (instance) {
            instance.monitoringEnabled = enabled;
            console.log(`🎯 射线检测监控已${enabled ? '启用' : '禁用'}`);
          }
        }
        /**
         * 🎯 设置性能阈值
         */


        static setPerformanceThreshold(threshold) {
          const instance = RaycastPerformanceMonitor.getInstance();

          if (instance) {
            instance.performanceThreshold = threshold;
            console.log(`🎯 射线检测性能阈值已设置为: ${threshold}ms`);
          }
        }
        /**
         * 🎯 手动获取当前性能数据
         */


        static getCurrentStats() {
          const instance = RaycastPerformanceMonitor.getInstance();

          if (!instance) {
            return null;
          }

          const avgTime = instance.raycastCount > 0 ? instance.raycastTotalTime / instance.raycastCount : 0;
          const cacheHitRate = instance.cacheHits + instance.cacheMisses > 0 ? instance.cacheHits / (instance.cacheHits + instance.cacheMisses) * 100 : 0;
          return {
            raycastCount: instance.raycastCount,
            avgTime: avgTime.toFixed(2),
            maxTime: instance.raycastMaxTime.toFixed(2),
            minTime: instance.raycastMinTime === Number.MAX_VALUE ? 0 : instance.raycastMinTime.toFixed(2),
            cacheHitRate: cacheHitRate.toFixed(1),
            cacheHits: instance.cacheHits,
            cacheMisses: instance.cacheMisses
          };
        }

      }, _class2.instance = null, _class2)) || _class)); // 暴露到全局，便于调试


      window.RaycastPerformanceMonitor = RaycastPerformanceMonitor;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6ba20bee7bc761b8a2f68a03b2255acd0cfb22ba.js.map