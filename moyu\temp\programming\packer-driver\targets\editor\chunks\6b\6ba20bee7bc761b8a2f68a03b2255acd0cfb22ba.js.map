{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/optimization/RaycastPerformanceMonitor.ts"], "names": ["_decorator", "Component", "oops", "ccclass", "property", "RaycastPerformanceMonitor", "raycastCount", "raycastTotalTime", "raycastMaxTime", "raycastMinTime", "Number", "MAX_VALUE", "lastResetTime", "cacheHits", "cacheMisses", "monitoringEnabled", "reportInterval", "performanceThreshold", "onLoad", "instance", "Date", "now", "schedule", "reportPerformance", "bind", "log", "logBusiness", "onDestroy", "unscheduleAllCallbacks", "getInstance", "startRaycast", "performance", "endRaycast", "startTime", "hitResult", "endTime", "duration", "Math", "max", "min", "console", "warn", "toFixed", "recordCacheHit", "recordCache<PERSON><PERSON>", "currentTime", "timeElapsed", "avgTime", "raycastsPerSecond", "cacheHitRate", "report", "resetStats", "setMonitoringEnabled", "enabled", "setPerformanceThreshold", "threshold", "getCurrentStats", "maxTime", "minTime", "window"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,I,iBAAAA,I;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;AAE9B;AACA;AACA;AACA;;2CAEaK,yB,WADZF,OAAO,CAAC,2BAAD,C,2BAAR,MACaE,yBADb,SAC+CJ,SAD/C,CACyD;AAAA;AAAA;AAGrD;AAHqD,eAI7CK,YAJ6C,GAItB,CAJsB;AAAA,eAK7CC,gBAL6C,GAKlB,CALkB;AAAA,eAM7CC,cAN6C,GAMpB,CANoB;AAAA,eAO7CC,cAP6C,GAOpBC,MAAM,CAACC,SAPa;AAAA,eAQ7CC,aAR6C,GAQrB,CARqB;AAUrD;AAVqD,eAW7CC,SAX6C,GAWzB,CAXyB;AAAA,eAY7CC,WAZ6C,GAYvB,CAZuB;AAcrD;AAdqD,eAe7CC,iBAf6C,GAehB,IAfgB;AAAA,eAgB7CC,cAhB6C,GAgBpB,IAhBoB;AAgBd;AAhBc,eAiB7CC,oBAjB6C,GAiBd,CAjBc;AAAA;;AAiBX;AAE1CC,QAAAA,MAAM,GAAG;AACLb,UAAAA,yBAAyB,CAACc,QAA1B,GAAqC,IAArC;AACA,eAAKP,aAAL,GAAqBQ,IAAI,CAACC,GAAL,EAArB,CAFK,CAIL;;AACA,eAAKC,QAAL,CAAc,KAAKC,iBAAL,CAAuBC,IAAvB,CAA4B,IAA5B,CAAd,EAAiD,KAAKR,cAAL,GAAsB,IAAvE;AAEA;AAAA;AAAA,4BAAKS,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACRtB,UAAAA,yBAAyB,CAACc,QAA1B,GAAqC,IAArC;AACA,eAAKS,sBAAL;AACH;AAED;AACJ;AACA;;;AACsB,eAAXC,WAAW,GAAqC;AACnD,iBAAOxB,yBAAyB,CAACc,QAAjC;AACH;AAED;AACJ;AACA;;;AACuB,eAAZW,YAAY,GAAW;AAC1B,gBAAMX,QAAQ,GAAGd,yBAAyB,CAACwB,WAA1B,EAAjB;;AACA,cAAI,CAACV,QAAD,IAAa,CAACA,QAAQ,CAACJ,iBAA3B,EAA8C;AAC1C,mBAAO,CAAP;AACH;;AAED,iBAAOgB,WAAW,CAACV,GAAZ,EAAP;AACH;AAED;AACJ;AACA;;;AACqB,eAAVW,UAAU,CAACC,SAAD,EAAoBC,SAAkB,GAAG,KAAzC,EAAsD;AACnE,gBAAMf,QAAQ,GAAGd,yBAAyB,CAACwB,WAA1B,EAAjB;;AACA,cAAI,CAACV,QAAD,IAAa,CAACA,QAAQ,CAACJ,iBAAvB,IAA4CkB,SAAS,KAAK,CAA9D,EAAiE;AAC7D;AACH;;AAED,gBAAME,OAAO,GAAGJ,WAAW,CAACV,GAAZ,EAAhB;AACA,gBAAMe,QAAQ,GAAGD,OAAO,GAAGF,SAA3B;AAEAd,UAAAA,QAAQ,CAACb,YAAT;AACAa,UAAAA,QAAQ,CAACZ,gBAAT,IAA6B6B,QAA7B;AACAjB,UAAAA,QAAQ,CAACX,cAAT,GAA0B6B,IAAI,CAACC,GAAL,CAASnB,QAAQ,CAACX,cAAlB,EAAkC4B,QAAlC,CAA1B;AACAjB,UAAAA,QAAQ,CAACV,cAAT,GAA0B4B,IAAI,CAACE,GAAL,CAASpB,QAAQ,CAACV,cAAlB,EAAkC2B,QAAlC,CAA1B,CAZmE,CAcnE;;AACA,cAAIA,QAAQ,GAAGjB,QAAQ,CAACF,oBAAxB,EAA8C;AAC1CuB,YAAAA,OAAO,CAACC,IAAR,CAAc,gBAAeL,QAAQ,CAACM,OAAT,CAAiB,CAAjB,CAAoB,WAAUvB,QAAQ,CAACF,oBAAqB,KAAzF;AACH;AACJ;AAED;AACJ;AACA;;;AACyB,eAAd0B,cAAc,GAAS;AAC1B,gBAAMxB,QAAQ,GAAGd,yBAAyB,CAACwB,WAA1B,EAAjB;;AACA,cAAIV,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACN,SAAT;AACH;AACJ;AAED;AACJ;AACA;;;AAC0B,eAAf+B,eAAe,GAAS;AAC3B,gBAAMzB,QAAQ,GAAGd,yBAAyB,CAACwB,WAA1B,EAAjB;;AACA,cAAIV,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACL,WAAT;AACH;AACJ;AAED;AACJ;AACA;;;AACYS,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,KAAKjB,YAAL,KAAsB,CAA1B,EAA6B;AACzB;AACH;;AAED,gBAAMuC,WAAW,GAAGzB,IAAI,CAACC,GAAL,EAApB;AACA,gBAAMyB,WAAW,GAAG,CAACD,WAAW,GAAG,KAAKjC,aAApB,IAAqC,IAAzD;AACA,gBAAMmC,OAAO,GAAG,KAAKxC,gBAAL,GAAwB,KAAKD,YAA7C;AACA,gBAAM0C,iBAAiB,GAAG,KAAK1C,YAAL,GAAoBwC,WAA9C;AACA,gBAAMG,YAAY,GAAG,KAAKpC,SAAL,IAAkB,KAAKA,SAAL,GAAiB,KAAKC,WAAxC,IAAuD,GAA5E;AAEA,gBAAMoC,MAAM,GAAG;AACX,yBAAa;AACT,qBAAO,KAAK5C,YADH;AAET,sBAAS,GAAEyC,OAAO,CAACL,OAAR,CAAgB,CAAhB,CAAmB,IAFrB;AAGT,sBAAS,GAAE,KAAKlC,cAAL,CAAoBkC,OAApB,CAA4B,CAA5B,CAA+B,IAHjC;AAIT,sBAAS,GAAE,KAAKjC,cAAL,CAAoBiC,OAApB,CAA4B,CAA5B,CAA+B,IAJjC;AAKT,wBAAUM,iBAAiB,CAACN,OAAlB,CAA0B,CAA1B,CALD;AAMT,uBAAU,GAAEO,YAAY,CAACP,OAAb,CAAqB,CAArB,CAAwB,GAN3B;AAOT,wBAAU,KAAK7B,SAPN;AAQT,yBAAW,KAAKC;AARP;AADF,WAAf;AAaA0B,UAAAA,OAAO,CAACf,GAAR,CAAY,cAAZ,EAA4ByB,MAA5B,EAxB8B,CA0B9B;;AACA,cAAIH,OAAO,GAAG,KAAK9B,oBAAnB,EAAyC;AACrCuB,YAAAA,OAAO,CAACC,IAAR,CAAc,kBAAiBM,OAAO,CAACL,OAAR,CAAgB,CAAhB,CAAmB,SAAlD;AACH;;AAED,cAAIM,iBAAiB,GAAG,EAAxB,EAA4B;AACxBR,YAAAA,OAAO,CAACC,IAAR,CAAc,gBAAeO,iBAAiB,CAACN,OAAlB,CAA0B,CAA1B,CAA6B,WAA1D;AACH;;AAED,cAAIO,YAAY,GAAG,EAAnB,EAAuB;AACnBT,YAAAA,OAAO,CAACC,IAAR,CAAc,eAAcQ,YAAY,CAACP,OAAb,CAAqB,CAArB,CAAwB,YAApD;AACH,WArC6B,CAuC9B;;;AACA,eAAKS,UAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,UAAU,GAAS;AACvB,eAAK7C,YAAL,GAAoB,CAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,cAAL,GAAsBC,MAAM,CAACC,SAA7B;AACA,eAAKE,SAAL,GAAiB,CAAjB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKF,aAAL,GAAqBQ,IAAI,CAACC,GAAL,EAArB;AACH;AAED;AACJ;AACA;;;AAC+B,eAApB+B,oBAAoB,CAACC,OAAD,EAAyB;AAChD,gBAAMlC,QAAQ,GAAGd,yBAAyB,CAACwB,WAA1B,EAAjB;;AACA,cAAIV,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACJ,iBAAT,GAA6BsC,OAA7B;AACAb,YAAAA,OAAO,CAACf,GAAR,CAAa,aAAY4B,OAAO,GAAG,IAAH,GAAU,IAAK,EAA/C;AACH;AACJ;AAED;AACJ;AACA;;;AACkC,eAAvBC,uBAAuB,CAACC,SAAD,EAA0B;AACpD,gBAAMpC,QAAQ,GAAGd,yBAAyB,CAACwB,WAA1B,EAAjB;;AACA,cAAIV,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACF,oBAAT,GAAgCsC,SAAhC;AACAf,YAAAA,OAAO,CAACf,GAAR,CAAa,oBAAmB8B,SAAU,IAA1C;AACH;AACJ;AAED;AACJ;AACA;;;AAC0B,eAAfC,eAAe,GAAQ;AAC1B,gBAAMrC,QAAQ,GAAGd,yBAAyB,CAACwB,WAA1B,EAAjB;;AACA,cAAI,CAACV,QAAL,EAAe;AACX,mBAAO,IAAP;AACH;;AAED,gBAAM4B,OAAO,GAAG5B,QAAQ,CAACb,YAAT,GAAwB,CAAxB,GAA4Ba,QAAQ,CAACZ,gBAAT,GAA4BY,QAAQ,CAACb,YAAjE,GAAgF,CAAhG;AACA,gBAAM2C,YAAY,GAAI9B,QAAQ,CAACN,SAAT,GAAqBM,QAAQ,CAACL,WAA/B,GAA8C,CAA9C,GACfK,QAAQ,CAACN,SAAT,IAAsBM,QAAQ,CAACN,SAAT,GAAqBM,QAAQ,CAACL,WAApD,IAAmE,GADpD,GAEf,CAFN;AAIA,iBAAO;AACHR,YAAAA,YAAY,EAAEa,QAAQ,CAACb,YADpB;AAEHyC,YAAAA,OAAO,EAAEA,OAAO,CAACL,OAAR,CAAgB,CAAhB,CAFN;AAGHe,YAAAA,OAAO,EAAEtC,QAAQ,CAACX,cAAT,CAAwBkC,OAAxB,CAAgC,CAAhC,CAHN;AAIHgB,YAAAA,OAAO,EAAEvC,QAAQ,CAACV,cAAT,KAA4BC,MAAM,CAACC,SAAnC,GAA+C,CAA/C,GAAmDQ,QAAQ,CAACV,cAAT,CAAwBiC,OAAxB,CAAgC,CAAhC,CAJzD;AAKHO,YAAAA,YAAY,EAAEA,YAAY,CAACP,OAAb,CAAqB,CAArB,CALX;AAMH7B,YAAAA,SAAS,EAAEM,QAAQ,CAACN,SANjB;AAOHC,YAAAA,WAAW,EAAEK,QAAQ,CAACL;AAPnB,WAAP;AASH;;AAxMoD,O,UACtCK,Q,GAA6C,I,yBA0MhE;;;AACCwC,MAAAA,MAAD,CAAgBtD,yBAAhB,GAA4CA,yBAA5C", "sourcesContent": ["import { _decorator, Component } from 'cc';\nimport { oops } from '../../../extensions/oops-plugin-framework/assets/core/Oops';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 🎯 射线检测性能监控器\n * 用于监控和优化射线检测的性能表现\n */\n@ccclass('RaycastPerformanceMonitor')\nexport class RaycastPerformanceMonitor extends Component {\n    private static instance: RaycastPerformanceMonitor | null = null;\n    \n    // 性能统计数据\n    private raycastCount: number = 0;\n    private raycastTotalTime: number = 0;\n    private raycastMaxTime: number = 0;\n    private raycastMinTime: number = Number.MAX_VALUE;\n    private lastResetTime: number = 0;\n    \n    // 缓存统计\n    private cacheHits: number = 0;\n    private cacheMisses: number = 0;\n    \n    // 监控配置\n    private monitoringEnabled: boolean = true;\n    private reportInterval: number = 5000; // 5秒报告一次\n    private performanceThreshold: number = 2; // 2ms阈值\n    \n    onLoad() {\n        RaycastPerformanceMonitor.instance = this;\n        this.lastResetTime = Date.now();\n        \n        // 定期报告性能数据\n        this.schedule(this.reportPerformance.bind(this), this.reportInterval / 1000);\n        \n        oops.log.logBusiness('🎯 射线检测性能监控器已启动');\n    }\n    \n    onDestroy() {\n        RaycastPerformanceMonitor.instance = null;\n        this.unscheduleAllCallbacks();\n    }\n    \n    /**\n     * 获取单例实例\n     */\n    static getInstance(): RaycastPerformanceMonitor | null {\n        return RaycastPerformanceMonitor.instance;\n    }\n    \n    /**\n     * 🎯 记录射线检测开始\n     */\n    static startRaycast(): number {\n        const instance = RaycastPerformanceMonitor.getInstance();\n        if (!instance || !instance.monitoringEnabled) {\n            return 0;\n        }\n        \n        return performance.now();\n    }\n    \n    /**\n     * 🎯 记录射线检测结束\n     */\n    static endRaycast(startTime: number, hitResult: boolean = false): void {\n        const instance = RaycastPerformanceMonitor.getInstance();\n        if (!instance || !instance.monitoringEnabled || startTime === 0) {\n            return;\n        }\n        \n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        \n        instance.raycastCount++;\n        instance.raycastTotalTime += duration;\n        instance.raycastMaxTime = Math.max(instance.raycastMaxTime, duration);\n        instance.raycastMinTime = Math.min(instance.raycastMinTime, duration);\n        \n        // 如果射线检测时间超过阈值，记录警告\n        if (duration > instance.performanceThreshold) {\n            console.warn(`⚠️ 射线检测耗时过长: ${duration.toFixed(2)}ms (阈值: ${instance.performanceThreshold}ms)`);\n        }\n    }\n    \n    /**\n     * 🎯 记录缓存命中\n     */\n    static recordCacheHit(): void {\n        const instance = RaycastPerformanceMonitor.getInstance();\n        if (instance) {\n            instance.cacheHits++;\n        }\n    }\n    \n    /**\n     * 🎯 记录缓存未命中\n     */\n    static recordCacheMiss(): void {\n        const instance = RaycastPerformanceMonitor.getInstance();\n        if (instance) {\n            instance.cacheMisses++;\n        }\n    }\n    \n    /**\n     * 🎯 生成性能报告\n     */\n    private reportPerformance(): void {\n        if (this.raycastCount === 0) {\n            return;\n        }\n        \n        const currentTime = Date.now();\n        const timeElapsed = (currentTime - this.lastResetTime) / 1000;\n        const avgTime = this.raycastTotalTime / this.raycastCount;\n        const raycastsPerSecond = this.raycastCount / timeElapsed;\n        const cacheHitRate = this.cacheHits / (this.cacheHits + this.cacheMisses) * 100;\n        \n        const report = {\n            '📊 射线检测统计': {\n                '总次数': this.raycastCount,\n                '平均耗时': `${avgTime.toFixed(2)}ms`,\n                '最大耗时': `${this.raycastMaxTime.toFixed(2)}ms`,\n                '最小耗时': `${this.raycastMinTime.toFixed(2)}ms`,\n                '每秒检测次数': raycastsPerSecond.toFixed(1),\n                '缓存命中率': `${cacheHitRate.toFixed(1)}%`,\n                '缓存命中次数': this.cacheHits,\n                '缓存未命中次数': this.cacheMisses\n            }\n        };\n        \n        console.log('🎯 射线检测性能报告:', report);\n        \n        // 性能建议\n        if (avgTime > this.performanceThreshold) {\n            console.warn(`⚠️ 射线检测平均耗时过高: ${avgTime.toFixed(2)}ms，建议优化`);\n        }\n        \n        if (raycastsPerSecond > 30) {\n            console.warn(`⚠️ 射线检测频率过高: ${raycastsPerSecond.toFixed(1)}/s，建议增加节流`);\n        }\n        \n        if (cacheHitRate < 50) {\n            console.warn(`⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`);\n        }\n        \n        // 重置统计数据\n        this.resetStats();\n    }\n    \n    /**\n     * 🎯 重置统计数据\n     */\n    private resetStats(): void {\n        this.raycastCount = 0;\n        this.raycastTotalTime = 0;\n        this.raycastMaxTime = 0;\n        this.raycastMinTime = Number.MAX_VALUE;\n        this.cacheHits = 0;\n        this.cacheMisses = 0;\n        this.lastResetTime = Date.now();\n    }\n    \n    /**\n     * 🎯 启用/禁用监控\n     */\n    static setMonitoringEnabled(enabled: boolean): void {\n        const instance = RaycastPerformanceMonitor.getInstance();\n        if (instance) {\n            instance.monitoringEnabled = enabled;\n            console.log(`🎯 射线检测监控已${enabled ? '启用' : '禁用'}`);\n        }\n    }\n    \n    /**\n     * 🎯 设置性能阈值\n     */\n    static setPerformanceThreshold(threshold: number): void {\n        const instance = RaycastPerformanceMonitor.getInstance();\n        if (instance) {\n            instance.performanceThreshold = threshold;\n            console.log(`🎯 射线检测性能阈值已设置为: ${threshold}ms`);\n        }\n    }\n    \n    /**\n     * 🎯 手动获取当前性能数据\n     */\n    static getCurrentStats(): any {\n        const instance = RaycastPerformanceMonitor.getInstance();\n        if (!instance) {\n            return null;\n        }\n        \n        const avgTime = instance.raycastCount > 0 ? instance.raycastTotalTime / instance.raycastCount : 0;\n        const cacheHitRate = (instance.cacheHits + instance.cacheMisses) > 0 \n            ? instance.cacheHits / (instance.cacheHits + instance.cacheMisses) * 100 \n            : 0;\n        \n        return {\n            raycastCount: instance.raycastCount,\n            avgTime: avgTime.toFixed(2),\n            maxTime: instance.raycastMaxTime.toFixed(2),\n            minTime: instance.raycastMinTime === Number.MAX_VALUE ? 0 : instance.raycastMinTime.toFixed(2),\n            cacheHitRate: cacheHitRate.toFixed(1),\n            cacheHits: instance.cacheHits,\n            cacheMisses: instance.cacheMisses\n        };\n    }\n}\n\n// 暴露到全局，便于调试\n(window as any).RaycastPerformanceMonitor = RaycastPerformanceMonitor;\n"]}