{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA6J,uCAA7J,EAA8T,uCAA9T,EAAme,uCAAne,EAAqoB,uCAAroB,EAAiyB,uCAAjyB,EAAs7B,uCAAt7B,EAAy/B,uCAAz/B,EAA+lC,uCAA/lC,EAAusC,uCAAvsC,EAAozC,wCAApzC,EAA24C,wCAA34C,EAAq+C,wCAAr+C,EAA+iD,wCAA/iD,EAAkoD,wCAAloD,EAAouD,wCAApuD,EAA2zD,wCAA3zD,EAAi5D,wCAAj5D,EAAq+D,wCAAr+D,EAAwjE,wCAAxjE,EAA8oE,wCAA9oE,EAA6tE,wCAA7tE,EAA2yE,wCAA3yE,EAAy4E,wCAAz4E,EAA+9E,wCAA/9E,EAAikF,wCAAjkF,EAAoqF,wCAApqF,EAAkwF,wCAAlwF,EAA+1F,wCAA/1F,EAAi8F,wCAAj8F,EAA0hG,wCAA1hG,EAAgoG,wCAAhoG,EAAitG,wCAAjtG,EAAqyG,wCAAryG,EAAo4G,wCAAp4G,EAAu+G,wCAAv+G,EAA6kH,wCAA7kH,EAAqrH,wCAArrH,EAAsxH,wCAAtxH,EAAo3H,wCAAp3H,EAAk9H,wCAAl9H,EAAijI,wCAAjjI,EAAmpI,wCAAnpI,EAAivI,wCAAjvI,EAA40I,wCAA50I,EAA85I,wCAA95I,EAAo/I,wCAAp/I,EAA+kJ,wCAA/kJ,EAA2qJ,wCAA3qJ,EAA6wJ,wCAA7wJ,EAA41J,wCAA51J,EAA07J,wCAA17J,EAA0hK,wCAA1hK,EAA4nK,wCAA5nK,EAAwtK,wCAAxtK,EAAozK,wCAApzK,EAAg5K,wCAAh5K,EAA8+K,wCAA9+K,EAAwkL,wCAAxkL,EAAiqL,wCAAjqL,EAAyvL,wCAAzvL,EAA81L,wCAA91L,EAAg8L,wCAAh8L,EAAmiM,wCAAniM,EAAooM,wCAApoM,EAA6uM,wCAA7uM,EAAo0M,wCAAp0M,EAAu5M,wCAAv5M,EAAm/M,wCAAn/M,EAAklN,wCAAllN,EAA2qN,wCAA3qN,EAA6wN,wCAA7wN,EAAu2N,wCAAv2N,EAAy8N,wCAAz8N,EAAuiO,wCAAviO,EAA0oO,wCAA1oO,EAA2uO,wCAA3uO,EAAm0O,wCAAn0O,EAAw6O,wCAAx6O,EAAugP,wCAAvgP,EAAqlP,wCAArlP,EAAwqP,wCAAxqP,EAAmwP,wCAAnwP,EAAu1P,wCAAv1P,EAA46P,wCAA56P,EAA0gQ,wCAA1gQ,EAA+lQ,wCAA/lQ,EAAirQ,wCAAjrQ,EAAmwQ,wCAAnwQ,EAAs1Q,wCAAt1Q,EAAu6Q,wCAAv6Q,EAAy/Q,wCAAz/Q,EAA6kR,wCAA7kR,EAAgqR,wCAAhqR,EAA4vR,wCAA5vR,EAA+0R,wCAA/0R,EAAi6R,wCAAj6R,EAA8+R,wCAA9+R,EAA0kS,wCAA1kS,EAA6qS,wCAA7qS,EAAuwS,yCAAvwS,EAAi2S,yCAAj2S,EAA27S,yCAA37S,EAAiiT,yCAAjiT,EAAmoT,yCAAnoT,EAAiuT,yCAAjuT,EAAo0T,yCAAp0T,EAAu6T,yCAAv6T,EAA0gU,yCAA1gU,EAAomU,yCAApmU,EAAssU,yCAAtsU,EAAwyU,yCAAxyU,EAA24U,yCAA34U,EAAi/U,yCAAj/U,EAAolV,yCAAplV,EAAgrV,yCAAhrV,EAA6wV,yCAA7wV,EAAs2V,yCAAt2V,EAA07V,yCAA17V,EAA4gW,yCAA5gW,EAAimW,yCAAjmW,EAAqrW,yCAArrW,EAA4wW,yCAA5wW,EAA22W,yCAA32W,EAA08W,yCAA18W,EAA6hX,yCAA7hX,EAAunX,yCAAvnX,EAAiuX,yCAAjuX,EAAm0X,yCAAn0X,EAA86X,yCAA96X,EAAmhY,yCAAnhY,EAAonY,yCAApnY,EAAmtY,yCAAntY,EAAozY,yCAApzY,EAAw5Y,yCAAx5Y,EAA2/Y,yCAA3/Y,EAA+lZ,yCAA/lZ,EAA8rZ,yCAA9rZ,EAAwyZ,yCAAxyZ,EAA44Z,yCAA54Z,EAAw+Z,yCAAx+Z,EAAuka,yCAAvka,EAAkra,yCAAlra,EAAswa,yCAAtwa,EAA61a,yCAA71a,EAA46a,yCAA56a,EAAs/a,yCAAt/a,EAAwlb,yCAAxlb,EAA0rb,yCAA1rb,EAAgzb,yCAAhzb,EAA06b,yCAA16b,EAAiic,yCAAjic,EAAspc,yCAAtpc,EAAgxc,yCAAhxc,EAAu4c,yCAAv4c,EAAggd,yCAAhgd,EAAqnd,yCAArnd,EAAoud,yCAApud,EAA61d,yCAA71d,EAAm9d,yCAAn9d,EAA8ke,yCAA9ke,EAAgte,yCAAhte,EAAk1e,yCAAl1e,EAAk8e,yCAAl8e,EAAyjf,yCAAzjf,EAAuqf,yCAAvqf,EAAgxf,yCAAhxf,EAA+3f,yCAA/3f,EAAw/f,yCAAx/f,EAA2mgB,yCAA3mgB,EAA+tgB,yCAA/tgB,EAAk1gB,yCAAl1gB,EAAo8gB,yCAAp8gB,EAAmjhB,yCAAnjhB,EAAwqhB,yCAAxqhB,EAAiyhB,yCAAjyhB,EAAg5hB,yCAAh5hB,EAA6/hB,yCAA7/hB,EAA2miB,yCAA3miB,EAAytiB,yCAAztiB,EAAw0iB,yCAAx0iB,EAAq7iB,yCAAr7iB,EAAiijB,yCAAjijB,EAA8ojB,yCAA9ojB,EAA0vjB,yCAA1vjB,EAAw2jB,yCAAx2jB,EAAu9jB,yCAAv9jB,EAAukkB,yCAAvkkB,EAAorkB,yCAAprkB,EAAkykB,yCAAlykB,EAAg5kB,yCAAh5kB,EAA6/kB,yCAA7/kB,EAAymlB,yCAAzmlB,EAAqtlB,yCAArtlB,EAA+0lB,yCAA/0lB,EAA08lB,yCAA18lB,EAAskmB,yCAAtkmB,EAA2smB,yCAA3smB,EAAy0mB,yCAAz0mB,EAAy8mB,yCAAz8mB,EAAkknB,yCAAlknB,EAAqsnB,yCAArsnB,EAAm0nB,yCAAn0nB,EAAs7nB,yCAAt7nB,EAAyioB,yCAAzioB,EAAspoB,yCAAtpoB,EAA2voB,yCAA3voB,EAAo2oB,yCAAp2oB,EAA+8oB,yCAA/8oB,EAAyjpB,yCAAzjpB,EAAkqpB,yCAAlqpB,EAA8wpB,yCAA9wpB,EAAw3pB,yCAAx3pB,EAAm+pB,yCAAn+pB,EAAmlqB,yCAAnlqB,EAAksqB,yCAAlsqB,EAAqzqB,yCAArzqB,EAAw6qB,yCAAx6qB,EAAuhrB,yCAAvhrB,EAA4orB,yCAA5orB,EAAiwrB,yCAAjwrB,EAAy3rB,yCAAz3rB,EAA0+rB,yCAA1+rB,EAA6lsB,yCAA7lsB,EAAgtsB,yCAAhtsB,EAAi0sB,yCAAj0sB,EAAo7sB,yCAAp7sB,EAA2itB,yCAA3itB,EAAmqtB,yCAAnqtB,EAA0xtB,yCAA1xtB,EAAk5tB,yCAAl5tB,EAA2guB,yCAA3guB,EAA0nuB,yCAA1nuB,EAA+uuB,yCAA/uuB,EAA81uB,yCAA91uB,EAAk9uB,yCAAl9uB,EAAmkvB,yCAAnkvB,EAAirvB,yCAAjrvB,EAAiyvB,yCAAjyvB,EAAi5vB,yCAAj5vB,EAAkgwB,yCAAlgwB,EAAmnwB,yCAAnnwB,EAAsuwB,yCAAtuwB,EAAs1wB,yCAAt1wB,EAAw8wB,yCAAx8wB,EAAkkxB,yCAAlkxB,EAA2rxB,yCAA3rxB,EAAozxB,yCAApzxB,EAA66xB,yCAA76xB,EAAmiyB,yCAAniyB,EAA0pyB,yCAA1pyB,EAAuwyB,yCAAvwyB,EAA43yB,yCAA53yB,EAAg/yB,yCAAh/yB,EAAomzB,yCAApmzB,EAAqtzB,yCAArtzB,EAA80zB,yCAA90zB,EAA27zB,yCAA37zB,EAA4i0B,yCAA5i0B,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///D:/codeSoftware/coscos/editor/Creator/3.8.5/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///D:/codeSoftware/coscos/editor/Creator/3.8.5/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///D:/codeSoftware/coscos/editor/Creator/3.8.5/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///D:/codeSoftware/coscos/editor/Creator/3.8.5/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///D:/codeSoftware/coscos/editor/Creator/3.8.5/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///D:/codeSoftware/coscos/editor/Creator/3.8.5/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/Main.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/facebook-instant-games/FBInstantManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/facebook-instant-games/FacebookGameEvents.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/facebook-instant-games/FacebookSecurityManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/Camera/CameraEntity.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/Camera/CameraModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/Global.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/account/Account.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/account/model/AccountModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ClientConfig.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ClientConst.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/CommonNet.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/Creating.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/DataManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/Enum.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/Pop.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/SingletonModuleComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/SystemError.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/config/GameServerConfig.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/config/GameStorageConfig.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/config/GameUIConfig.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/config/LocalConfig.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/debug/ViewModelDebugger.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ecs/GameSystem.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/loader/SimpleLoadingManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/mgr/Ad.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/mgr/WxMgr.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/prompt/PromptManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/scene/SimpleSceneManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/DynamicMapInitializer.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/FormattedTableLevelJson.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/LevelConfigTypes.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/TableErroCode.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/TableLanguage.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/TableLevelJson.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/TablePromptWindow.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/table/TablePropConf.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/tips/TipsManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ui/List.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ui/ListItem.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ui/mvvm/VMCustom.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ui/mvvm/VMExample.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/common/ui/mvvm/VMLabelLanguage.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/Guide.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/model/GuideModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/view/GuideFollow3DComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/view/GuideView3DItemComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/view/GuideViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/view/GuideViewItem.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/view/GuideViewMask.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/view/GuideViewPrompt.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/guide/view/PolygonMask.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/initialize/Initialize.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/initialize/bll/Login.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/initialize/model/AccountModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/initialize/model/GateModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/initialize/view/InitialViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/initialize/view/LoginViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/initialize/view/SimpleLoadingViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/item/BaseItemEntity.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/item/ItemEntity.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/item/model/ItemModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/item/view/ItemSceneViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/AudioManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/CollectionSlotManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/ConfigManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/DailyChallengeManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/GameResultManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/ItemInteractionManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/LevelProgressManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/PropManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/ScrollableRankingManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/managers/UnifiedGameManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/Box.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/DuckNode.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/Effect2DFollow3D.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/Example2d.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/GameResult.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/GroundSceneViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/PersonInfo.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/PickBox.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/Rank.ts.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/RankCell.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/Revive.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/Setting.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/SheepMove.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/WallCell.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/WallSceneViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/closeBtn.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/prefab/example.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/role/Role.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/role/model/RoleModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/sceneMgr/vIew/GameSceneViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/sceneMgr/vIew/PropCell.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/BaseSceneEntity.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/GameEntity.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/SimpleItemDetector.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/bll/SceneUtils.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/model/GameModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/view/CameraViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/view/GameUIViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Hall/HallEntity.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Hall/bll/HallGroundCell.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Hall/bll/HallVmRankCell.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Hall/model/HallModelComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Hall/view/HallSceneViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/game/scenes/Hall/view/HallUIViewComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/optimization/RequestOptimizer.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/testScript/ItemInteractionTest.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/testScript/TouchSystemTest.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/testScript/fbTestComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/testScript/testComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/models/GameConst.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/models/Security.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/models/ShareConfig.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/ServiceProtoGame.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/ServiceProtoGate.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/base.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/commonTools.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/PtlGameGenTestRankData.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/PtlGameGetRank.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/PtlGameUpdateSimpleData.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/PtlUpdateProgress.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/PtlUpdateProp.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/PtlUserInfo.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/admin/PtlAuth.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/game/admin/PtlLogined.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/MsgAuthLoginRes.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/PtlFacebookLogin.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/PtlGameArea.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/PtlGameGenTestRankData.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/PtlJustAuthLogin.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/PtlLogin.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/PtlRegister.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/protocols/gate/admin/PtlGameServerJoin.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/tsrpc/types/GameState.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/utils/DynamicMeshCreator.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/utils/ObjectUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/assets/script/utils/Utils.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/Oops.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/Root.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/audio/AudioEffect.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/audio/AudioEffectPool.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/audio/AudioManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/audio/AudioMusic.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/event/EventDispatcher.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/event/EventMessage.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/event/MessageManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/loader/ResLoader.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/log/Logger.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/random/RandomManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/random/SeedRandom.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/storage/StorageManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/storage/StorageSecurityCrypto.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/storage/StorageSecuritySimple.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/timer/Timer.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/common/timer/TimerManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/game/GameManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/GuiEnum.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/layer/Defines.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/layer/DelegateComponent.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/layer/LayerDialog.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/layer/LayerManager.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/layer/LayerNotify.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/layer/LayerPopup.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/layer/LayerUI.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/prompt/CommonPrompt.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/prompt/LoadingIndicator.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/gui/prompt/Notify.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/ArrayUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/CameraUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/DeviceUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/EncryptUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/ImageUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/JsonUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/LayerUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/MathUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/ObjectUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/PhysicsUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/PlatformUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/RegexUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/RotateUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/StringUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/TimeUtils.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/Vec3Util.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/core/utils/ViewUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/2d/Ambilight.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/2d/FlashSpine.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/2d/FlashSprite.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/2d/SpineFinishedRelease.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/Effect2DFollow3D.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/EffectDelayRelease.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/EffectEvent.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/EffectFinishedRelease.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/animator-effect/EffectSingleCase.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/collection/AsyncQueue.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/collection/Collection.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/collection/List.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECS.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECSComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECSEntity.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECSGroup.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECSMask.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECSMatcher.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECSModel.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/ecs/ECSSystem.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/extension/ArrayExt.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/extension/DateExt.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/extension/DirectorExt.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/extension/NodeDragExt.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/extension/NodeExt.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/button/ButtonEffect.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/button/ButtonSimple.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/button/ButtonTouchLong.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/button/UIButton.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/label/LabelChange.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/label/LabelNumber.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/label/LabelTime.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/language/Language.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/language/LanguageLabel.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/language/LanguagePack.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/language/LanguageSpine.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/gui/language/LanguageSprite.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/JsonOb.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/StringFormat.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMBase.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMCompsEdit.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMCustom.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMEnv.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMEvent.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMLabel.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMModify.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMParent.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMProgress.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/VMState.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/ViewModel.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/ui/BhvButtonGroup.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/ui/BhvFrameIndex.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/ui/BhvRollNumber.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/model-view/ui/BhvSwitchPage.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/render-texture/RtToModel.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/libs/render-texture/RtToSprite.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/common/CCComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/common/CCVMParentComp.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/common/GameCollision.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/common/GameComponent.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/common/ModuleUtil.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/config/BuildTimeConstants.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/config/Config.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/config/GameConfig.ts\"), () => import(\"file:///F:/moyuproject/moyu/extensions/oops-plugin-framework/assets/module/config/GameQueryConfig.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}