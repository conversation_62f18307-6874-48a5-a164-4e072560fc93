System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, LayerSwitchDebugger;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f1c83DKj+VEG6WD3lm9AG5x", "LayerSwitchDebugger", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 🎯 层级切换调试器
       * 用于调试和优化多层射线检测的层级切换功能
       */

      _export("LayerSwitchDebugger", LayerSwitchDebugger = (_dec = ccclass('LayerSwitchDebugger'), _dec2 = property(Label), _dec3 = property(Label), _dec(_class = (_class2 = class LayerSwitchDebugger extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "statusLabel", _descriptor, this);

          _initializerDefineProperty(this, "configLabel", _descriptor2, this);

          // 调试配置
          this.debugConfig = {
            layerSwitchDelay: 800,
            slowMoveThreshold: 5,
            maxSlowMoveTime: 3000,
            pauseAfterSelection: 1000,
            maxLayers: 3
          };
        }

        start() {
          this.updateConfigDisplay();
          this.scheduleStatusUpdate(); // 暴露调试方法到全局

          window.LayerDebugger = {
            setDelay: delay => this.setLayerSwitchDelay(delay),
            setThreshold: threshold => this.setSlowMoveThreshold(threshold),
            setMaxTime: time => this.setMaxSlowMoveTime(time),
            setPause: pause => this.setPauseAfterSelection(pause),
            setMaxLayers: layers => this.setMaxLayers(layers),
            getConfig: () => this.debugConfig,
            resetToDefault: () => this.resetToDefault(),
            showHelp: () => this.showHelp()
          };
          console.log('🎯 层级切换调试器已启动，使用 LayerDebugger 进行调试');
          this.showHelp();
        }
        /**
         * 🎯 定期更新状态显示
         */


        scheduleStatusUpdate() {
          this.schedule(() => {
            this.updateStatusDisplay();
          }, 1); // 每秒更新一次
        }
        /**
         * 🎯 更新状态显示
         */


        updateStatusDisplay() {
          if (!this.statusLabel) return;
          const status = `层级切换状态:
当前延迟: ${this.debugConfig.layerSwitchDelay}ms
慢速阈值: ${this.debugConfig.slowMoveThreshold}px
最大时间: ${this.debugConfig.maxSlowMoveTime}ms
选择暂停: ${this.debugConfig.pauseAfterSelection}ms
最大层数: ${this.debugConfig.maxLayers}层

使用说明:
- 慢慢移动手指 (<${this.debugConfig.slowMoveThreshold}px) 触发层级切换
- 每 ${this.debugConfig.layerSwitchDelay}ms 切换一次
- 选择物品后暂停 ${this.debugConfig.pauseAfterSelection}ms
- 快速移动或超时自动退出`;
          this.statusLabel.string = status;
        }
        /**
         * 🎯 更新配置显示
         */


        updateConfigDisplay() {
          if (!this.configLabel) return;
          this.configLabel.string = `调试命令:
LayerDebugger.setDelay(800)     // 设置切换延迟
LayerDebugger.setThreshold(5)   // 设置慢速阈值
LayerDebugger.setMaxTime(3000)  // 设置最大时间
LayerDebugger.setPause(1000)    // 设置选择暂停
LayerDebugger.setMaxLayers(3)   // 设置最大层数
LayerDebugger.resetToDefault()  // 重置为默认值`;
        }
        /**
         * 🎯 设置层级切换延迟
         */


        setLayerSwitchDelay(delay) {
          if (delay < 100 || delay > 5000) {
            console.warn('⚠️ 延迟应在100-5000ms之间');
            return;
          }

          this.debugConfig.layerSwitchDelay = delay;
          console.log(`✅ 层级切换延迟已设置为: ${delay}ms`);
          this.updateStatusDisplay(); // 通知ItemInteractionManager更新配置

          this.notifyConfigChange();
        }
        /**
         * 🎯 设置慢速移动阈值
         */


        setSlowMoveThreshold(threshold) {
          if (threshold < 1 || threshold > 20) {
            console.warn('⚠️ 阈值应在1-20px之间');
            return;
          }

          this.debugConfig.slowMoveThreshold = threshold;
          console.log(`✅ 慢速移动阈值已设置为: ${threshold}px`);
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 设置最大慢速移动时间
         */


        setMaxSlowMoveTime(time) {
          if (time < 1000 || time > 10000) {
            console.warn('⚠️ 时间应在1000-10000ms之间');
            return;
          }

          this.debugConfig.maxSlowMoveTime = time;
          console.log(`✅ 最大慢速移动时间已设置为: ${time}ms`);
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 设置选择后暂停时间
         */


        setPauseAfterSelection(pause) {
          if (pause < 0 || pause > 5000) {
            console.warn('⚠️ 暂停时间应在0-5000ms之间');
            return;
          }

          this.debugConfig.pauseAfterSelection = pause;
          console.log(`✅ 选择后暂停时间已设置为: ${pause}ms`);
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 设置最大检测层数
         */


        setMaxLayers(layers) {
          if (layers < 1 || layers > 10) {
            console.warn('⚠️ 层数应在1-10之间');
            return;
          }

          this.debugConfig.maxLayers = layers;
          console.log(`✅ 最大检测层数已设置为: ${layers}层`);
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 重置为默认配置
         */


        resetToDefault() {
          this.debugConfig = {
            layerSwitchDelay: 800,
            slowMoveThreshold: 5,
            maxSlowMoveTime: 3000,
            pauseAfterSelection: 1000,
            maxLayers: 3
          };
          console.log('✅ 已重置为默认配置');
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 通知配置变更
         */


        notifyConfigChange() {
          // 这里可以通知ItemInteractionManager更新配置
          // 暂时通过事件或全局变量的方式
          window.LayerSwitchConfig = this.debugConfig;
        }
        /**
         * 🎯 显示帮助信息
         */


        showHelp() {
          const help = `
🎯 层级切换调试器使用指南:

问题诊断:
1. 切换太快 → LayerDebugger.setDelay(1200) // 增加延迟
2. 切换太慢 → LayerDebugger.setDelay(500)  // 减少延迟
3. 难以触发 → LayerDebugger.setThreshold(3) // 降低阈值
4. 容易触发 → LayerDebugger.setThreshold(8) // 提高阈值
5. 切换太久 → LayerDebugger.setMaxTime(2000) // 减少最大时间

推荐配置:
- 精确操作: delay=1000, threshold=3, maxTime=2000
- 快速操作: delay=600, threshold=7, maxTime=1500
- 平衡模式: delay=800, threshold=5, maxTime=3000 (默认)

实时调试:
- 打开控制台查看切换日志
- 观察 "🔄 切换到第X层物品" 消息
- 注意 "🏁 退出慢速移动模式" 时机
`;
          console.log(help);
        }
        /**
         * 🎯 获取当前性能统计
         */


        getPerformanceStats() {
          return {
            config: this.debugConfig,
            recommendations: this.getRecommendations(),
            troubleshooting: this.getTroubleshootingTips()
          };
        }
        /**
         * 🎯 获取推荐配置
         */


        getRecommendations() {
          return {
            precise: {
              delay: 1000,
              threshold: 3,
              maxTime: 2000
            },
            fast: {
              delay: 600,
              threshold: 7,
              maxTime: 1500
            },
            balanced: {
              delay: 800,
              threshold: 5,
              maxTime: 3000
            }
          };
        }
        /**
         * 🎯 获取故障排除提示
         */


        getTroubleshootingTips() {
          return ['如果切换太频繁，增加 layerSwitchDelay', '如果难以触发切换，降低 slowMoveThreshold', '如果切换时间太长，减少 maxSlowMoveTime', '如果选择后仍在切换，增加 pauseAfterSelection', '如果检测不到下层，增加 maxLayers'];
        }

        onDestroy() {
          this.unscheduleAllCallbacks();
          delete window.LayerDebugger;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "statusLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "configLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class)); // 暴露到全局，便于调试


      window.LayerSwitchDebugger = LayerSwitchDebugger;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9b449da6ebe0ca74cd074eb5d0399a57a0b47947.js.map