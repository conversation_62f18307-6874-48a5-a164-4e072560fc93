{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/optimization/LayerSwitchDebugger.ts"], "names": ["_decorator", "Component", "Label", "ccclass", "property", "LayerSwitchDebugger", "debugConfig", "layerSwitchDelay", "slowMoveThreshold", "maxSlowMoveTime", "pauseAfterSelection", "maxLayers", "start", "updateConfigDisplay", "scheduleStatusUpdate", "window", "LayerDebugger", "<PERSON><PERSON><PERSON><PERSON>", "delay", "setLayerSwitchDelay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "setSlowMoveThreshold", "setMaxTime", "time", "setMaxSlowMoveTime", "setPause", "pause", "setPauseAfterSelection", "setMaxLayers", "layers", "getConfig", "resetToDefault", "showHelp", "console", "log", "schedule", "updateStatusDisplay", "statusLabel", "status", "string", "config<PERSON><PERSON><PERSON>", "warn", "notifyConfigChange", "LayerSwitchConfig", "help", "getPerformanceStats", "config", "recommendations", "getRecommendations", "troubleshooting", "getTroubleshootingTips", "precise", "maxTime", "fast", "balanced", "onDestroy", "unscheduleAllCallbacks"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;;;;;;;;OAE1B;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;AAE9B;AACA;AACA;AACA;;qCAEaK,mB,WADZF,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAACF,KAAD,C,UAGRE,QAAQ,CAACF,KAAD,C,2BALb,MACaG,mBADb,SACyCJ,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;;AAO/C;AAP+C,eAQvCK,WARuC,GAQzB;AAClBC,YAAAA,gBAAgB,EAAE,GADA;AAElBC,YAAAA,iBAAiB,EAAE,CAFD;AAGlBC,YAAAA,eAAe,EAAE,IAHC;AAIlBC,YAAAA,mBAAmB,EAAE,IAJH;AAKlBC,YAAAA,SAAS,EAAE;AALO,WARyB;AAAA;;AAgB/CC,QAAAA,KAAK,GAAG;AACJ,eAAKC,mBAAL;AACA,eAAKC,oBAAL,GAFI,CAIJ;;AACCC,UAAAA,MAAD,CAAgBC,aAAhB,GAAgC;AAC5BC,YAAAA,QAAQ,EAAGC,KAAD,IAAmB,KAAKC,mBAAL,CAAyBD,KAAzB,CADD;AAE5BE,YAAAA,YAAY,EAAGC,SAAD,IAAuB,KAAKC,oBAAL,CAA0BD,SAA1B,CAFT;AAG5BE,YAAAA,UAAU,EAAGC,IAAD,IAAkB,KAAKC,kBAAL,CAAwBD,IAAxB,CAHF;AAI5BE,YAAAA,QAAQ,EAAGC,KAAD,IAAmB,KAAKC,sBAAL,CAA4BD,KAA5B,CAJD;AAK5BE,YAAAA,YAAY,EAAGC,MAAD,IAAoB,KAAKD,YAAL,CAAkBC,MAAlB,CALN;AAM5BC,YAAAA,SAAS,EAAE,MAAM,KAAKzB,WANM;AAO5B0B,YAAAA,cAAc,EAAE,MAAM,KAAKA,cAAL,EAPM;AAQ5BC,YAAAA,QAAQ,EAAE,MAAM,KAAKA,QAAL;AARY,WAAhC;AAWAC,UAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACA,eAAKF,QAAL;AACH;AAED;AACJ;AACA;;;AACYnB,QAAAA,oBAAoB,GAAS;AACjC,eAAKsB,QAAL,CAAc,MAAM;AAChB,iBAAKC,mBAAL;AACH,WAFD,EAEG,CAFH,EADiC,CAG1B;AACV;AAED;AACJ;AACA;;;AACYA,QAAAA,mBAAmB,GAAS;AAChC,cAAI,CAAC,KAAKC,WAAV,EAAuB;AAEvB,gBAAMC,MAAM,GAAI;AACxB,QAAQ,KAAKjC,WAAL,CAAiBC,gBAAiB;AAC1C,QAAQ,KAAKD,WAAL,CAAiBE,iBAAkB;AAC3C,QAAQ,KAAKF,WAAL,CAAiBG,eAAgB;AACzC,QAAQ,KAAKH,WAAL,CAAiBI,mBAAoB;AAC7C,QAAQ,KAAKJ,WAAL,CAAiBK,SAAU;AACnC;AACA;AACA,aAAa,KAAKL,WAAL,CAAiBE,iBAAkB;AAChD,MAAM,KAAKF,WAAL,CAAiBC,gBAAiB;AACxC,YAAY,KAAKD,WAAL,CAAiBI,mBAAoB;AACjD,cAXQ;AAaA,eAAK4B,WAAL,CAAiBE,MAAjB,GAA0BD,MAA1B;AACH;AAED;AACJ;AACA;;;AACY1B,QAAAA,mBAAmB,GAAS;AAChC,cAAI,CAAC,KAAK4B,WAAV,EAAuB;AAEvB,eAAKA,WAAL,CAAiBD,MAAjB,GAA2B;AACnC;AACA;AACA;AACA;AACA;AACA,0CANQ;AAOH;AAED;AACJ;AACA;;;AACIrB,QAAAA,mBAAmB,CAACD,KAAD,EAAsB;AACrC,cAAIA,KAAK,GAAG,GAAR,IAAeA,KAAK,GAAG,IAA3B,EAAiC;AAC7BgB,YAAAA,OAAO,CAACQ,IAAR,CAAa,qBAAb;AACA;AACH;;AAED,eAAKpC,WAAL,CAAiBC,gBAAjB,GAAoCW,KAApC;AACAgB,UAAAA,OAAO,CAACC,GAAR,CAAa,iBAAgBjB,KAAM,IAAnC;AACA,eAAKmB,mBAAL,GARqC,CAUrC;;AACA,eAAKM,kBAAL;AACH;AAED;AACJ;AACA;;;AACIrB,QAAAA,oBAAoB,CAACD,SAAD,EAA0B;AAC1C,cAAIA,SAAS,GAAG,CAAZ,IAAiBA,SAAS,GAAG,EAAjC,EAAqC;AACjCa,YAAAA,OAAO,CAACQ,IAAR,CAAa,iBAAb;AACA;AACH;;AAED,eAAKpC,WAAL,CAAiBE,iBAAjB,GAAqCa,SAArC;AACAa,UAAAA,OAAO,CAACC,GAAR,CAAa,iBAAgBd,SAAU,IAAvC;AACA,eAAKgB,mBAAL;AACA,eAAKM,kBAAL;AACH;AAED;AACJ;AACA;;;AACIlB,QAAAA,kBAAkB,CAACD,IAAD,EAAqB;AACnC,cAAIA,IAAI,GAAG,IAAP,IAAeA,IAAI,GAAG,KAA1B,EAAiC;AAC7BU,YAAAA,OAAO,CAACQ,IAAR,CAAa,uBAAb;AACA;AACH;;AAED,eAAKpC,WAAL,CAAiBG,eAAjB,GAAmCe,IAAnC;AACAU,UAAAA,OAAO,CAACC,GAAR,CAAa,mBAAkBX,IAAK,IAApC;AACA,eAAKa,mBAAL;AACA,eAAKM,kBAAL;AACH;AAED;AACJ;AACA;;;AACIf,QAAAA,sBAAsB,CAACD,KAAD,EAAsB;AACxC,cAAIA,KAAK,GAAG,CAAR,IAAaA,KAAK,GAAG,IAAzB,EAA+B;AAC3BO,YAAAA,OAAO,CAACQ,IAAR,CAAa,qBAAb;AACA;AACH;;AAED,eAAKpC,WAAL,CAAiBI,mBAAjB,GAAuCiB,KAAvC;AACAO,UAAAA,OAAO,CAACC,GAAR,CAAa,kBAAiBR,KAAM,IAApC;AACA,eAAKU,mBAAL;AACA,eAAKM,kBAAL;AACH;AAED;AACJ;AACA;;;AACId,QAAAA,YAAY,CAACC,MAAD,EAAuB;AAC/B,cAAIA,MAAM,GAAG,CAAT,IAAcA,MAAM,GAAG,EAA3B,EAA+B;AAC3BI,YAAAA,OAAO,CAACQ,IAAR,CAAa,eAAb;AACA;AACH;;AAED,eAAKpC,WAAL,CAAiBK,SAAjB,GAA6BmB,MAA7B;AACAI,UAAAA,OAAO,CAACC,GAAR,CAAa,iBAAgBL,MAAO,GAApC;AACA,eAAKO,mBAAL;AACA,eAAKM,kBAAL;AACH;AAED;AACJ;AACA;;;AACIX,QAAAA,cAAc,GAAS;AACnB,eAAK1B,WAAL,GAAmB;AACfC,YAAAA,gBAAgB,EAAE,GADH;AAEfC,YAAAA,iBAAiB,EAAE,CAFJ;AAGfC,YAAAA,eAAe,EAAE,IAHF;AAIfC,YAAAA,mBAAmB,EAAE,IAJN;AAKfC,YAAAA,SAAS,EAAE;AALI,WAAnB;AAQAuB,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACA,eAAKE,mBAAL;AACA,eAAKM,kBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,kBAAkB,GAAS;AAC/B;AACA;AACC5B,UAAAA,MAAD,CAAgB6B,iBAAhB,GAAoC,KAAKtC,WAAzC;AACH;AAED;AACJ;AACA;;;AACI2B,QAAAA,QAAQ,GAAS;AACb,gBAAMY,IAAI,GAAI;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAnBQ;AAqBAX,UAAAA,OAAO,CAACC,GAAR,CAAYU,IAAZ;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,mBAAmB,GAAQ;AACvB,iBAAO;AACHC,YAAAA,MAAM,EAAE,KAAKzC,WADV;AAEH0C,YAAAA,eAAe,EAAE,KAAKC,kBAAL,EAFd;AAGHC,YAAAA,eAAe,EAAE,KAAKC,sBAAL;AAHd,WAAP;AAKH;AAED;AACJ;AACA;;;AACYF,QAAAA,kBAAkB,GAAQ;AAC9B,iBAAO;AACHG,YAAAA,OAAO,EAAE;AAAElC,cAAAA,KAAK,EAAE,IAAT;AAAeG,cAAAA,SAAS,EAAE,CAA1B;AAA6BgC,cAAAA,OAAO,EAAE;AAAtC,aADN;AAEHC,YAAAA,IAAI,EAAE;AAAEpC,cAAAA,KAAK,EAAE,GAAT;AAAcG,cAAAA,SAAS,EAAE,CAAzB;AAA4BgC,cAAAA,OAAO,EAAE;AAArC,aAFH;AAGHE,YAAAA,QAAQ,EAAE;AAAErC,cAAAA,KAAK,EAAE,GAAT;AAAcG,cAAAA,SAAS,EAAE,CAAzB;AAA4BgC,cAAAA,OAAO,EAAE;AAArC;AAHP,WAAP;AAKH;AAED;AACJ;AACA;;;AACYF,QAAAA,sBAAsB,GAAa;AACvC,iBAAO,CACH,6BADG,EAEH,+BAFG,EAGH,6BAHG,EAIH,kCAJG,EAKH,uBALG,CAAP;AAOH;;AAEDK,QAAAA,SAAS,GAAG;AACR,eAAKC,sBAAL;AACA,iBAAQ1C,MAAD,CAAgBC,aAAvB;AACH;;AA3P8C,O;;;;;iBAEnB,I;;;;;;;iBAGA,I;;kCAyPhC;;;AACCD,MAAAA,MAAD,CAAgBV,mBAAhB,GAAsCA,mBAAtC", "sourcesContent": ["import { _decorator, Component, Label, Node } from 'cc';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 🎯 层级切换调试器\n * 用于调试和优化多层射线检测的层级切换功能\n */\n@ccclass('LayerSwitchDebugger')\nexport class LayerSwitchDebugger extends Component {\n    @property(Label)\n    statusLabel: Label | null = null;\n    \n    @property(Label)\n    configLabel: Label | null = null;\n    \n    // 调试配置\n    private debugConfig = {\n        layerSwitchDelay: 800,\n        slowMoveThreshold: 5,\n        maxSlowMoveTime: 3000,\n        pauseAfterSelection: 1000,\n        maxLayers: 3\n    };\n    \n    start() {\n        this.updateConfigDisplay();\n        this.scheduleStatusUpdate();\n        \n        // 暴露调试方法到全局\n        (window as any).LayerDebugger = {\n            setDelay: (delay: number) => this.setLayerSwitchDelay(delay),\n            setThreshold: (threshold: number) => this.setSlowMoveThreshold(threshold),\n            setMaxTime: (time: number) => this.setMaxSlowMoveTime(time),\n            setPause: (pause: number) => this.setPauseAfterSelection(pause),\n            setMaxLayers: (layers: number) => this.setMaxLayers(layers),\n            getConfig: () => this.debugConfig,\n            resetToDefault: () => this.resetToDefault(),\n            showHelp: () => this.showHelp()\n        };\n        \n        console.log('🎯 层级切换调试器已启动，使用 LayerDebugger 进行调试');\n        this.showHelp();\n    }\n    \n    /**\n     * 🎯 定期更新状态显示\n     */\n    private scheduleStatusUpdate(): void {\n        this.schedule(() => {\n            this.updateStatusDisplay();\n        }, 1); // 每秒更新一次\n    }\n    \n    /**\n     * 🎯 更新状态显示\n     */\n    private updateStatusDisplay(): void {\n        if (!this.statusLabel) return;\n        \n        const status = `层级切换状态:\n当前延迟: ${this.debugConfig.layerSwitchDelay}ms\n慢速阈值: ${this.debugConfig.slowMoveThreshold}px\n最大时间: ${this.debugConfig.maxSlowMoveTime}ms\n选择暂停: ${this.debugConfig.pauseAfterSelection}ms\n最大层数: ${this.debugConfig.maxLayers}层\n\n使用说明:\n- 慢慢移动手指 (<${this.debugConfig.slowMoveThreshold}px) 触发层级切换\n- 每 ${this.debugConfig.layerSwitchDelay}ms 切换一次\n- 选择物品后暂停 ${this.debugConfig.pauseAfterSelection}ms\n- 快速移动或超时自动退出`;\n        \n        this.statusLabel.string = status;\n    }\n    \n    /**\n     * 🎯 更新配置显示\n     */\n    private updateConfigDisplay(): void {\n        if (!this.configLabel) return;\n        \n        this.configLabel.string = `调试命令:\nLayerDebugger.setDelay(800)     // 设置切换延迟\nLayerDebugger.setThreshold(5)   // 设置慢速阈值\nLayerDebugger.setMaxTime(3000)  // 设置最大时间\nLayerDebugger.setPause(1000)    // 设置选择暂停\nLayerDebugger.setMaxLayers(3)   // 设置最大层数\nLayerDebugger.resetToDefault()  // 重置为默认值`;\n    }\n    \n    /**\n     * 🎯 设置层级切换延迟\n     */\n    setLayerSwitchDelay(delay: number): void {\n        if (delay < 100 || delay > 5000) {\n            console.warn('⚠️ 延迟应在100-5000ms之间');\n            return;\n        }\n        \n        this.debugConfig.layerSwitchDelay = delay;\n        console.log(`✅ 层级切换延迟已设置为: ${delay}ms`);\n        this.updateStatusDisplay();\n        \n        // 通知ItemInteractionManager更新配置\n        this.notifyConfigChange();\n    }\n    \n    /**\n     * 🎯 设置慢速移动阈值\n     */\n    setSlowMoveThreshold(threshold: number): void {\n        if (threshold < 1 || threshold > 20) {\n            console.warn('⚠️ 阈值应在1-20px之间');\n            return;\n        }\n        \n        this.debugConfig.slowMoveThreshold = threshold;\n        console.log(`✅ 慢速移动阈值已设置为: ${threshold}px`);\n        this.updateStatusDisplay();\n        this.notifyConfigChange();\n    }\n    \n    /**\n     * 🎯 设置最大慢速移动时间\n     */\n    setMaxSlowMoveTime(time: number): void {\n        if (time < 1000 || time > 10000) {\n            console.warn('⚠️ 时间应在1000-10000ms之间');\n            return;\n        }\n        \n        this.debugConfig.maxSlowMoveTime = time;\n        console.log(`✅ 最大慢速移动时间已设置为: ${time}ms`);\n        this.updateStatusDisplay();\n        this.notifyConfigChange();\n    }\n    \n    /**\n     * 🎯 设置选择后暂停时间\n     */\n    setPauseAfterSelection(pause: number): void {\n        if (pause < 0 || pause > 5000) {\n            console.warn('⚠️ 暂停时间应在0-5000ms之间');\n            return;\n        }\n        \n        this.debugConfig.pauseAfterSelection = pause;\n        console.log(`✅ 选择后暂停时间已设置为: ${pause}ms`);\n        this.updateStatusDisplay();\n        this.notifyConfigChange();\n    }\n    \n    /**\n     * 🎯 设置最大检测层数\n     */\n    setMaxLayers(layers: number): void {\n        if (layers < 1 || layers > 10) {\n            console.warn('⚠️ 层数应在1-10之间');\n            return;\n        }\n        \n        this.debugConfig.maxLayers = layers;\n        console.log(`✅ 最大检测层数已设置为: ${layers}层`);\n        this.updateStatusDisplay();\n        this.notifyConfigChange();\n    }\n    \n    /**\n     * 🎯 重置为默认配置\n     */\n    resetToDefault(): void {\n        this.debugConfig = {\n            layerSwitchDelay: 800,\n            slowMoveThreshold: 5,\n            maxSlowMoveTime: 3000,\n            pauseAfterSelection: 1000,\n            maxLayers: 3\n        };\n        \n        console.log('✅ 已重置为默认配置');\n        this.updateStatusDisplay();\n        this.notifyConfigChange();\n    }\n    \n    /**\n     * 🎯 通知配置变更\n     */\n    private notifyConfigChange(): void {\n        // 这里可以通知ItemInteractionManager更新配置\n        // 暂时通过事件或全局变量的方式\n        (window as any).LayerSwitchConfig = this.debugConfig;\n    }\n    \n    /**\n     * 🎯 显示帮助信息\n     */\n    showHelp(): void {\n        const help = `\n🎯 层级切换调试器使用指南:\n\n问题诊断:\n1. 切换太快 → LayerDebugger.setDelay(1200) // 增加延迟\n2. 切换太慢 → LayerDebugger.setDelay(500)  // 减少延迟\n3. 难以触发 → LayerDebugger.setThreshold(3) // 降低阈值\n4. 容易触发 → LayerDebugger.setThreshold(8) // 提高阈值\n5. 切换太久 → LayerDebugger.setMaxTime(2000) // 减少最大时间\n\n推荐配置:\n- 精确操作: delay=1000, threshold=3, maxTime=2000\n- 快速操作: delay=600, threshold=7, maxTime=1500\n- 平衡模式: delay=800, threshold=5, maxTime=3000 (默认)\n\n实时调试:\n- 打开控制台查看切换日志\n- 观察 \"🔄 切换到第X层物品\" 消息\n- 注意 \"🏁 退出慢速移动模式\" 时机\n`;\n        \n        console.log(help);\n    }\n    \n    /**\n     * 🎯 获取当前性能统计\n     */\n    getPerformanceStats(): any {\n        return {\n            config: this.debugConfig,\n            recommendations: this.getRecommendations(),\n            troubleshooting: this.getTroubleshootingTips()\n        };\n    }\n    \n    /**\n     * 🎯 获取推荐配置\n     */\n    private getRecommendations(): any {\n        return {\n            precise: { delay: 1000, threshold: 3, maxTime: 2000 },\n            fast: { delay: 600, threshold: 7, maxTime: 1500 },\n            balanced: { delay: 800, threshold: 5, maxTime: 3000 }\n        };\n    }\n    \n    /**\n     * 🎯 获取故障排除提示\n     */\n    private getTroubleshootingTips(): string[] {\n        return [\n            '如果切换太频繁，增加 layerSwitchDelay',\n            '如果难以触发切换，降低 slowMoveThreshold',\n            '如果切换时间太长，减少 maxSlowMoveTime',\n            '如果选择后仍在切换，增加 pauseAfterSelection',\n            '如果检测不到下层，增加 maxLayers'\n        ];\n    }\n    \n    onDestroy() {\n        this.unscheduleAllCallbacks();\n        delete (window as any).LayerDebugger;\n    }\n}\n\n// 暴露到全局，便于调试\n(window as any).LayerSwitchDebugger = LayerSwitchDebugger;\n"]}