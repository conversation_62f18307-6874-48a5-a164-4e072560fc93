{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/testScript/testComp.ts"], "names": ["_decorator", "assetManager", "Component", "instantiate", "Material", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Prefab", "Vec4", "ccclass", "property", "testComp", "sharedMaterial", "dissolveEnable", "rimLightXrayPbrMaterial", "time", "instantiatedNodes", "currentGlowNode", "currentRimStrength", "onLoad", "loadBundle", "err", "bundle", "console", "error", "load", "material", "log", "prefabs", "for<PERSON>ach", "name", "prefab", "i", "node", "setParent", "push", "<PERSON><PERSON><PERSON><PERSON>", "getComponent", "effectAsset", "includes", "start", "update", "deltaTime", "length", "cycleTime", "dissolveValue", "switchMaterial", "glowTime", "randomIndex", "Math", "floor", "random", "setProperty", "copyMaterialProperties", "fromMaterial", "toMaterial", "normalMap", "getProperty", "albedoMap", "pbrMap", "warn", "toRimLight", "setSharedMaterial", "setInstancedAttribute"], "mappings": ";;;;;;;;;;AACIA,MAAAA,U,OAAAA,U;AACAC,MAAAA,Y,OAAAA,Y;AACAC,MAAAA,S,OAAAA,S;AACAC,MAAAA,W,OAAAA,W;AACAC,MAAAA,Q,OAAAA,Q;AACAC,MAAAA,Y,OAAAA,Y;AAEAC,MAAAA,M,OAAAA,M;AACAC,MAAAA,I,OAAAA,I;;;;;;;;;OAEE;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;0BAEjBU,Q,WADZF,OAAO,CAAC,UAAD,C,gBAAR,MACaE,QADb,SAC8BR,SAD9B,CACwC;AAAA;AAAA;AACpC;AADoC,eAEpCS,cAFoC,GAEF,IAFE;AAGpC;AAHoC,eAIpCC,cAJoC,GAIV,KAJU;AAMpC;AANoC,eAOpCC,uBAPoC,GAOO,IAPP;AAQpC;AARoC,eAS5BC,IAT4B,GASb,CATa;AAUpC;AAVoC,eAW5BC,iBAX4B,GAWA,EAXA;AAYpC;AAZoC,eAa5BC,eAb4B,GAaG,IAbH;AAcpC;AAdoC,eAe5BC,kBAf4B,GAeC,CAfD;AAAA;;AAgB1BC,QAAAA,MAAM,GAAS;AACrBjB,UAAAA,YAAY,CAACkB,UAAb,CAAwB,WAAxB,EAAqC,CAACC,GAAD,EAAMC,MAAN,KAAiB;AAClD,gBAAID,GAAJ,EAAS;AACLE,cAAAA,OAAO,CAACC,KAAR,CAAc,yBAAd,EAAyCH,GAAzC;AACA;AACH;;AACDC,YAAAA,MAAM,CAACG,IAAP,CAAY,0CAAZ,EAAwDpB,QAAxD,EAAkE,CAACgB,GAAD,EAAMK,QAAN,KAAmB;AACjF,kBAAIL,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,aAAd,EAA6BH,GAA7B;AACA;AACH;;AACDE,cAAAA,OAAO,CAACI,GAAR,CAAY,aAAZ,EAA2BD,QAA3B;AACA,mBAAKZ,uBAAL,GAA+BY,QAA/B;AACH,aAPD;AASA,kBAAME,OAAO,GAAG,CACZ,mBADY,EAEZ,iBAFY,EAGZ,iBAHY,EAIZ,iBAJY,EAKZ,mBALY,EAMZ,iBANY,EAOZ,iBAPY,EAQZ,iBARY,EASZ,iBATY,EAUZ,mBAVY,EAWZ,iBAXY,EAYZ,iBAZY,EAaZ,mBAbY,EAcZ,mBAdY,EAeZ,kBAfY,EAgBZ,iBAhBY,EAiBZ,kBAjBY,EAkBZ,iBAlBY,EAmBZ,iBAnBY,EAoBZ,iBApBY,EAqBZ,iBArBY,EAsBZ,mBAtBY,CAAhB;AAwBAA,YAAAA,OAAO,CAACC,OAAR,CAAgBC,IAAI,IAAI;AACpBR,cAAAA,MAAM,CAACG,IAAP,CAAYK,IAAZ,EAAkBvB,MAAlB,EAA0B,CAACc,GAAD,EAAMU,MAAN,KAAiB;AACvC,oBAAIV,GAAJ,EAAS;AACLE,kBAAAA,OAAO,CAACC,KAAR,CAAc,YAAd,EAA4BH,GAA5B;AACA;AACH;;AACDE,gBAAAA,OAAO,CAACI,GAAR,CAAY,YAAZ,EAA0BG,IAA1B;;AACA,qBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,sBAAIC,IAAI,GAAG7B,WAAW,CAAC2B,MAAD,CAAtB;AACAE,kBAAAA,IAAI,CAACC,SAAL,CAAe,KAAKD,IAApB,EAFyB,CAIzB;;AACA,uBAAKjB,iBAAL,CAAuBmB,IAAvB,CAA4BF,IAA5B;AAEA,sBAAIG,YAAY,GAAGH,IAAI,CAACI,YAAL,CAAkB/B,YAAlB,CAAnB;;AACA,sBAAI,CAAC,KAAKM,cAAV,EAA0B;AACtB,yBAAKA,cAAL,GAAsBwB,YAAY,CAACxB,cAAnC;;AACA,wBACI,KAAKA,cAAL,IACA,KAAKA,cAAL,CAAoB0B,WAApB,CAAgCR,IAAhC,CAAqCS,QAArC,CAA8C,UAA9C,CAFJ,EAGE;AACE,2BAAK1B,cAAL,GAAsB,IAAtB;AACH;AACJ;AACJ;AACJ,eAxBD;AAyBH,aA1BD;AA2BH,WAjED;AAkEH;;AACD2B,QAAAA,KAAK,GAAG,CAAE;;AACVC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAK7B,cAAL,IAAuB,KAAKD,cAA5B,IAA8C,KAAKI,iBAAL,CAAuB2B,MAAvB,GAAgC,CAAlF,EAAqF;AACjF,iBAAK5B,IAAL,IAAa2B,SAAb,CADiF,CAGjF;;AACA,kBAAME,SAAS,GAAG,KAAK7B,IAAL,GAAY,CAA9B;AACA,gBAAI8B,aAAJ;;AAEA,gBAAID,SAAS,IAAI,CAAjB,EAAoB;AAChB;AACAC,cAAAA,aAAa,GAAGD,SAAS,GAAG,CAA5B,CAFgB,CAIhB;;AACA,kBAAI,KAAK3B,eAAT,EAA0B;AACtB,qBAAK6B,cAAL,CAAoB,KAAK7B,eAAzB,EAA0C,KAA1C;AACA,qBAAKA,eAAL,GAAuB,IAAvB;AACH;AACJ,aATD,MASO,IAAI2B,SAAS,IAAI,CAAjB,EAAoB;AACvB;AACAC,cAAAA,aAAa,GAAG,CAAhB,CAFuB,CAIvB;;AACA,oBAAME,QAAQ,GAAGH,SAAS,GAAG,CAA7B,CALuB,CAKS;AAEhC;;AACA,kBAAI,CAAC,KAAK3B,eAAV,EAA2B;AACvB,sBAAM+B,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,KAAKnC,iBAAL,CAAuB2B,MAAlD,CAApB,CADuB,CAEvB;;AACA,qBAAK1B,eAAL,GAAuB,KAAKD,iBAAL,CAAuBgC,WAAvB,CAAvB;AACAzB,gBAAAA,OAAO,CAACI,GAAR,CAAa,QAAOqB,WAAY,eAAhC;AACA,qBAAKF,cAAL,CAAoB,KAAK7B,eAAzB,EAA0C,IAA1C;AACH;AACJ,aAfM,MAeA;AACH;AACA4B,cAAAA,aAAa,GAAG,IAAI,CAACD,SAAS,GAAG,CAAb,IAAkB,CAAtC,CAFG,CAIH;;AACA,kBAAI,KAAK3B,eAAT,EAA0B;AACtB,qBAAK6B,cAAL,CAAoB,KAAK7B,eAAzB,EAA0C,KAA1C;AACA,qBAAKA,eAAL,GAAuB,IAAvB;AACH;AACJ,aAxCgF,CA0CjF;;;AACA,iBAAKL,cAAL,CAAoBwC,WAApB,CACI,gBADJ,EAEI,IAAI5C,IAAJ,CAASqC,aAAT,EAAwB,GAAxB,EAA6B,GAA7B,EAAkC,GAAlC,CAFJ;AAIH;AACJ,SAtImC,CAuIpC;;;AACQQ,QAAAA,sBAAsB,CAACC,YAAD,EAAyBC,UAAzB,EAAqD;AAC/E,cAAI;AACA;AACA,kBAAMC,SAAS,GAAGF,YAAY,CAACG,WAAb,CAAyB,WAAzB,CAAlB;AACA,kBAAMC,SAAS,GAAGJ,YAAY,CAACG,WAAb,CAAyB,WAAzB,CAAlB;AACA,kBAAME,MAAM,GAAGL,YAAY,CAACG,WAAb,CAAyB,QAAzB,CAAf,CAJA,CAMA;;AACA,gBAAID,SAAJ,EAAeD,UAAU,CAACH,WAAX,CAAuB,WAAvB,EAAoCI,SAApC;AACf,gBAAIE,SAAJ,EAAeH,UAAU,CAACH,WAAX,CAAuB,WAAvB,EAAoCM,SAApC;AACf,gBAAIC,MAAJ,EAAYJ,UAAU,CAACH,WAAX,CAAuB,QAAvB,EAAiCO,MAAjC;AACf,WAVD,CAUE,OAAOnC,KAAP,EAAc;AACZD,YAAAA,OAAO,CAACqC,IAAR,CAAa,cAAb,EAA6BpC,KAA7B;AACH;AACJ;;AACDsB,QAAAA,cAAc,CAACb,IAAD,EAAa4B,UAAb,EAAkC;AAC5C,cAAIzB,YAAY,GAAGH,IAAI,CAACI,YAAL,CAAkB/B,YAAlB,CAAnB;;AACA,cAAIuD,UAAJ,EAAgB;AACZ,gBAAI,KAAKjD,cAAL,IAAuB,KAAKE,uBAAhC,EAAyD;AACrD,mBAAKuC,sBAAL,CAA4B,KAAKzC,cAAjC,EAAiD,KAAKE,uBAAtD;AAEAsB,cAAAA,YAAY,CAAC0B,iBAAb,CAA+B,KAAKhD,uBAApC,EAA6D,CAA7D,EAHqD,CAKrD;;AACAsB,cAAAA,YAAY,CAAC2B,qBAAb,CAAmC,yBAAnC,EAA8D,CAAC,GAAD,CAA9D;AAEAxC,cAAAA,OAAO,CAACI,GAAR,CAAa,oBAAb;AACAJ,cAAAA,OAAO,CAACI,GAAR,CAAa,UAAb,EAAwBS,YAAY,CAACxB,cAAb,CAA4BkB,IAApD;AACAP,cAAAA,OAAO,CAACI,GAAR,CAAa,gBAAb,EAA8BS,YAAY,CAACxB,cAAb,CAA4B0B,WAA5B,CAAwCR,IAAtE;AACH;AACJ,WAbD,MAaO;AACH,gBAAI,KAAKlB,cAAT,EAAyB;AACrBwB,cAAAA,YAAY,CAAC0B,iBAAb,CAA+B,KAAKlD,cAApC,EAAoD,CAApD;AACAW,cAAAA,OAAO,CAACI,GAAR,CAAa,YAAb;AACH;AACJ;AACJ,SA5KmC,CA8KpC;AACA;AACA;AACA;;;AAjLoC,O", "sourcesContent": ["import {\n    _decorator,\n    assetManager,\n    Component,\n    instantiate,\n    Material,\n    MeshRenderer,\n    Node,\n    Prefab,\n    Vec4,\n} from 'cc';\nconst { ccclass, property } = _decorator;\n@ccclass('testComp')\nexport class testComp extends Component {\n    // 共享材质\n    sharedMaterial: Material | null = null;\n    // 呼吸消融\n    dissolveEnable: boolean = false;\n\n    // 发光材质\n    rimLightXrayPbrMaterial: Material | null = null;\n    // 时间累计\n    private time: number = 0;\n    // 实例化的节点数组\n    private instantiatedNodes: Node[] = [];\n    // 当前发光的节点\n    private currentGlowNode: Node | null = null;\n    // 当前发光强度\n    private currentRimStrength: number = 0;\n    protected onLoad(): void {\n        assetManager.loadBundle('bundleOne', (err, bundle) => {\n            if (err) {\n                console.error('❌ 加载BundleOne bundle失败:', err);\n                return;\n            }\n            bundle.load('common/materials/rimLightXrayPbrMaterial', Material, (err, material) => {\n                if (err) {\n                    console.error('❌ 加载发光材质失败:', err);\n                    return;\n                }\n                console.log('✅ 发光材质加载完成:', material);\n                this.rimLightXrayPbrMaterial = material;\n            });\n\n            const prefabs = [\n                'prefabs/game/半个茄子',\n                'prefabs/game/南瓜',\n                'prefabs/game/土豆',\n                'prefabs/game/大蒜',\n                'prefabs/game/洋葱半个',\n                'prefabs/game/玉米',\n                'prefabs/game/甜菜',\n                'prefabs/game/生姜',\n                'prefabs/game/番茄',\n                'prefabs/game/红卷心菜',\n                'prefabs/game/红椒',\n                'prefabs/game/红薯',\n                'prefabs/game/绿卷心菜',\n                'prefabs/game/胡桃南瓜',\n                'prefabs/game/胡萝卜',\n                'prefabs/game/芦笋',\n                'prefabs/game/花椰菜',\n                'prefabs/game/青椒',\n                'prefabs/game/韭葱',\n                'prefabs/game/黄椒',\n                'prefabs/game/黄瓜',\n                'prefabs/game/黄西葫芦',\n            ];\n            prefabs.forEach(name => {\n                bundle.load(name, Prefab, (err, prefab) => {\n                    if (err) {\n                        console.error('❌ 加载预制体失败:', err);\n                        return;\n                    }\n                    console.log('✅ 预制体加载完成:', name);\n                    for (let i = 1; i <= 6; i++) {\n                        let node = instantiate(prefab);\n                        node.setParent(this.node);\n\n                        // 保存实例化的节点\n                        this.instantiatedNodes.push(node);\n\n                        let meshRenderer = node.getComponent(MeshRenderer);\n                        if (!this.sharedMaterial) {\n                            this.sharedMaterial = meshRenderer.sharedMaterial;\n                            if (\n                                this.sharedMaterial &&\n                                this.sharedMaterial.effectAsset.name.includes('dissolve')\n                            ) {\n                                this.dissolveEnable = true;\n                            }\n                        }\n                    }\n                });\n            });\n        });\n    }\n    start() {}\n    update(deltaTime: number) {\n        if (this.dissolveEnable && this.sharedMaterial && this.instantiatedNodes.length > 0) {\n            this.time += deltaTime;\n\n            // 9秒循环：0~2秒生成，2~7秒发光，7~9秒消失\n            const cycleTime = this.time % 9;\n            let dissolveValue: number;\n\n            if (cycleTime <= 2) {\n                // 0~2秒：消融生成 (从不可见到可见，dissolveValue: 0→1)\n                dissolveValue = cycleTime / 2;\n\n                // 确保没有发光节点\n                if (this.currentGlowNode) {\n                    this.switchMaterial(this.currentGlowNode, false);\n                    this.currentGlowNode = null;\n                }\n            } else if (cycleTime <= 7) {\n                // 2~7秒：完全显示状态，随机一个模型发光（5秒发光时间）\n                dissolveValue = 1;\n\n                // 发光时间计算（从第2秒开始的时间）\n                const glowTime = cycleTime - 2; // 0~5秒\n\n                // 在2秒时刻随机选择一个节点发光\n                if (!this.currentGlowNode) {\n                    const randomIndex = Math.floor(Math.random() * this.instantiatedNodes.length);\n                    // const randomIndex = 1; // 选择第2个节点进行测试\n                    this.currentGlowNode = this.instantiatedNodes[randomIndex];\n                    console.log(`✨ 选择第${randomIndex}个模型发光，开始呼吸灯效果`);\n                    this.switchMaterial(this.currentGlowNode, true);\n                }\n            } else {\n                // 7~9秒：切换回原材质，消融消失 (从可见到不可见，dissolveValue: 1→0)\n                dissolveValue = 1 - (cycleTime - 7) / 2;\n\n                // 切换回原材质\n                if (this.currentGlowNode) {\n                    this.switchMaterial(this.currentGlowNode, false);\n                    this.currentGlowNode = null;\n                }\n            }\n\n            // 设置共享材质的消融参数（影响所有使用该材质的模型）\n            this.sharedMaterial.setProperty(\n                'dissolveParams',\n                new Vec4(dissolveValue, 0.1, 0.0, 0.0)\n            );\n        }\n    }\n    // 🎨 安全的材质属性复制方法 - 确保不破坏GPU Instancing合批\n    private copyMaterialProperties(fromMaterial: Material, toMaterial: Material): void {\n        try {\n            // ✅ 复制shader支持的纹理属性\n            const normalMap = fromMaterial.getProperty('normalMap') as any;\n            const albedoMap = fromMaterial.getProperty('albedoMap') as any;\n            const pbrMap = fromMaterial.getProperty('pbrMap') as any;\n\n            // 🎯 设置纹理属性\n            if (normalMap) toMaterial.setProperty('normalMap', normalMap);\n            if (albedoMap) toMaterial.setProperty('albedoMap', albedoMap);\n            if (pbrMap) toMaterial.setProperty('pbrMap', pbrMap);\n        } catch (error) {\n            console.warn('⚠️ 材质属性复制失败:', error);\n        }\n    }\n    switchMaterial(node: Node, toRimLight: boolean) {\n        let meshRenderer = node.getComponent(MeshRenderer);\n        if (toRimLight) {\n            if (this.sharedMaterial && this.rimLightXrayPbrMaterial) {\n                this.copyMaterialProperties(this.sharedMaterial, this.rimLightXrayPbrMaterial);\n\n                meshRenderer.setSharedMaterial(this.rimLightXrayPbrMaterial, 0);\n\n                // 🌟 设置基础rim light强度（会被呼吸效果调制）\n                meshRenderer.setInstancedAttribute('a_instanced_rimStrength', [2.0]);\n\n                console.log(`🌟 切换到发光材质，开启呼吸灯效果`);\n                console.log(`🌟 当前材质：`, meshRenderer.sharedMaterial.name);\n                console.log(`🌟 当前材质effect：`, meshRenderer.sharedMaterial.effectAsset.name);\n            }\n        } else {\n            if (this.sharedMaterial) {\n                meshRenderer.setSharedMaterial(this.sharedMaterial, 0);\n                console.log(`🔄 切换回原始材质`);\n            }\n        }\n    }\n\n    // 更新发光强度 - 已不需要，呼吸灯效果由shader自动处理\n    // private updateRimStrength(node: Node) {\n    //     // 功能已迁移到shader中的呼吸灯计算\n    // }\n}\n"]}