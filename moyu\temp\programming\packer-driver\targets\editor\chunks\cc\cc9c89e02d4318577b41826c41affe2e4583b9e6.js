System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, input, Input, PhysicsSystem, oops, PHY_GROUP, smc, ItemSceneViewComp, GameSceneViewComp, _dec, _class, _crd, ccclass, property, ItemInteractionManager;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPHY_GROUP(extras) {
    _reporterNs.report("PHY_GROUP", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemSceneViewComp(extras) {
    _reporterNs.report("ItemSceneViewComp", "../../item/view/ItemSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameSceneViewComp(extras) {
    _reporterNs.report("GameSceneViewComp", "../../sceneMgr/vIew/GameSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEntity(extras) {
    _reporterNs.report("GameEntity", "./GameEntity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      input = _cc.input;
      Input = _cc.Input;
      PhysicsSystem = _cc.PhysicsSystem;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      PHY_GROUP = _unresolved_3.PHY_GROUP;
    }, function (_unresolved_4) {
      smc = _unresolved_4.smc;
    }, function (_unresolved_5) {
      ItemSceneViewComp = _unresolved_5.ItemSceneViewComp;
    }, function (_unresolved_6) {
      GameSceneViewComp = _unresolved_6.GameSceneViewComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "15b11JywPdI6Z9ridQfpm7x", "ItemInteractionManager", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'input', 'Input', 'PhysicsSystem']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 物品交互管理器 - 智能触摸组件版本
       * 负责触摸事件的捕获和处理，与类版本协同工作
       */

      _export("ItemInteractionManager", ItemInteractionManager = (_dec = ccclass('ItemInteractionManager'), _dec(_class = class ItemInteractionManager extends Component {
        constructor(...args) {
          super(...args);
          this.gameEntity = null;
          this.currentTouchedItem = null;
          // 🎯 防抖和状态管理
          this.lastClickTime = 0;
          this.clickDelay = 100;
          // 100ms防抖延迟
          this.isProcessingClick = false;
          // 🚀 性能优化：触摸移动节流 - 增强版
          this.lastMoveTime = 0;
          this.moveThrottleDelay = 33;
          // 30FPS限制，约33ms (降低检测频率)
          this.lastMovePosition = {
            x: 0,
            y: 0
          };
          this.moveDistanceThreshold = 10;
          // 增加像素阈值到10px，减少不必要的检测
          // 🎯 射线检测缓存优化
          this.raycastCache = new Map();
          this.raycastCacheTimeout = 100;
          // 缓存100ms，减少重复计算
          this.lastRaycastPosition = {
            x: -1,
            y: -1
          };
          this.raycastPositionThreshold = 8;
          // 射线检测位置阈值
          // 🎯 智能区域检测系统
          this.currentDetectedItems = [];
          // 当前检测到的所有物品
          this.currentItemIndex = 0;
          // 当前选中的物品索引
          this.lastDetectionPosition = {
            x: -1,
            y: -1
          };
          this.detectionRadius = 25;
          // 检测半径25px
          this.itemSwitchDelay = 600;
          // 物品切换延迟600ms
          this.lastItemSwitchTime = 0;
          // 🎯 慢速移动检测
          this.slowMoveThreshold = 8;
          // 慢速移动阈值8px
          this.isInSlowMoveMode = false;
          this.slowMoveStartTime = 0;
          this.maxSlowMoveTime = 4000;
          // 最大慢速移动时间4秒
          // 🎯 区域检测优化
          this.useExpandedDetection = true;
          // 使用扩展检测
          this.expandedRadius = 15;
          // 扩展检测半径15px
          // 🎯 简单性能监控
          this.raycastCount = 0;
          this.cacheHitCount = 0;
          this.lastPerformanceReport = 0;
        }

        onLoad() {
          input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');
        }

        start() {
          // 在start中获取GameEntity，确保ent属性已经设置
          const gameSceneViewComp = this.node.getComponent(_crd && GameSceneViewComp === void 0 ? (_reportPossibleCrUseOfGameSceneViewComp({
            error: Error()
          }), GameSceneViewComp) : GameSceneViewComp);

          if (gameSceneViewComp && gameSceneViewComp.ent) {
            this.gameEntity = gameSceneViewComp.ent;
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`);
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');
          }
        }

        onDestroy() {
          input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this); // 🧹 清理射线检测缓存

          this.raycastCache.clear();
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');
        }
        /**
         * 🔍 检查是否可以处理点击
         */


        canProcessClick() {
          const currentTime = Date.now(); // 防抖检查

          if (currentTime - this.lastClickTime < this.clickDelay) {
            // console.log('🛡️ 点击过快，触发防抖保护');
            return false;
          } // 处理状态检查


          if (this.isProcessingClick) {
            // console.log('🛡️ 正在处理点击，跳过重复处理');
            return false;
          }

          return true;
        }
        /**
         * 🎯 智能物品选择逻辑
         */


        intelligentItemSelection(item) {
          if (!this.gameEntity || !this.gameEntity.interactionManager) {
            console.warn('⚠️ GameEntity或InteractionManager未准备好');
            return false;
          }

          const itemNode = item.node;
          const interactionManager = this.gameEntity.interactionManager; // 🔍 检查物品是否在收集槽管理器中

          if (interactionManager.slotManager) {
            const slot = interactionManager.slotManager.getSlotByItem(itemNode);

            if (slot) {
              // 物品在收集槽中
              if (slot.index >= 7) {
                // 在额外槽位(7-9)，可以选中移动到主槽位
                // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);
                return true;
              } else {
                // 在主槽位(0-6)，不能选中
                // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);
                return false;
              }
            }
          } // 🔍 检查物品是否在场景中可选择


          const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());

          if (!isSelectable) {
            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);
            return false;
          } // 🔍 检查收集槽是否已满


          if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {
            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);
            return false;
          }

          return true;
        }
        /**
         * 🚀 优化版射线检测 - 带缓存和智能跳过
         */


        detectItemAtPosition(event) {
          if (!this.gameEntity) {
            return null;
          }

          const camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;

          if (!camera) {
            return null;
          }

          const currentX = event.getLocationX();
          const currentY = event.getLocationY(); // 🎯 检查是否为慢速移动（可能想要切换到下层模型）

          const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);
          const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);
          const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
          const isSlowMove = distance < this.slowMoveThreshold; // 🎯 检查缓存

          const cacheKey = `${Math.floor(currentX / this.raycastPositionThreshold)}_${Math.floor(currentY / this.raycastPositionThreshold)}`;
          const cached = this.raycastCache.get(cacheKey);

          if (cached && Date.now() - cached.timestamp < this.raycastCacheTimeout) {
            this.cacheHitCount++;
            this.reportPerformanceIfNeeded(); // 🎯 如果是慢速移动且有多个物品，使用缓存的物品进行切换

            if (isSlowMove && cached.items.length > 1) {
              this.currentDetectedItems = cached.items;
              return this.handleItemSwitching();
            }

            return cached.items[0] || null;
          } // 🎯 执行扩展区域检测


          const allItems = this.performExpandedAreaDetection(currentX, currentY); // 🎯 更新缓存

          this.raycastCache.set(cacheKey, {
            items: allItems,
            timestamp: Date.now()
          }); // 🎯 清理过期缓存

          this.cleanupCache(); // 更新最后检测位置

          this.lastRaycastPosition.x = currentX;
          this.lastRaycastPosition.y = currentY; // 🎯 返回合适的物品

          if (isSlowMove && allItems.length > 1) {
            this.currentDetectedItems = allItems;
            return this.handleItemSwitching();
          } else {
            // 🎯 快速移动时退出慢速模式，回到顶层
            this.exitSlowMoveMode();
          }

          return allItems[0] || null;
        }
        /**
         * 🎯 扩展区域检测 - 使用多点射线检测更大区域
         */


        performExpandedAreaDetection(centerX, centerY) {
          const camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;
          const raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA;
          const allItems = [];
          const foundItems = new Set(); // 避免重复，使用节点名称
          // 🎯 检测点配置：中心点 + 周围8个点

          const detectionPoints = [{
            x: centerX,
            y: centerY
          }, // 中心点
          {
            x: centerX - this.expandedRadius,
            y: centerY
          }, // 左
          {
            x: centerX + this.expandedRadius,
            y: centerY
          }, // 右
          {
            x: centerX,
            y: centerY - this.expandedRadius
          }, // 上
          {
            x: centerX,
            y: centerY + this.expandedRadius
          }, // 下
          {
            x: centerX - this.expandedRadius,
            y: centerY - this.expandedRadius
          }, // 左上
          {
            x: centerX + this.expandedRadius,
            y: centerY - this.expandedRadius
          }, // 右上
          {
            x: centerX - this.expandedRadius,
            y: centerY + this.expandedRadius
          }, // 左下
          {
            x: centerX + this.expandedRadius,
            y: centerY + this.expandedRadius
          } // 右下
          ];
          const startTime = performance.now(); // 🎯 对每个检测点进行射线检测

          for (const point of detectionPoints) {
            const ray = camera.screenPointToRay(point.x, point.y);

            if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
              const result = PhysicsSystem.instance.raycastClosestResult;
              const hitNode = result.collider.node;
              const itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                error: Error()
              }), ItemSceneViewComp) : ItemSceneViewComp);

              if (itemComp && itemComp.ItemModel && !foundItems.has(hitNode.uuid)) {
                allItems.push(itemComp);
                foundItems.add(hitNode.uuid);
              }
            }
          } // 🎯 记录性能


          const endTime = performance.now();
          const duration = endTime - startTime;
          this.raycastCount++;

          if (duration > 4) {
            console.warn(`⚠️ 扩展区域检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`);
          }

          return allItems;
        }
        /**
         * 🎯 智能物品切换 - 在当前区域的物品间切换
         */


        handleItemSwitching() {
          if (this.currentDetectedItems.length <= 1) {
            return this.currentDetectedItems[0] || null;
          }

          const now = Date.now(); // 🎯 检查是否刚进入慢速移动模式

          if (!this.isInSlowMoveMode) {
            this.isInSlowMoveMode = true;
            this.slowMoveStartTime = now;
            this.currentItemIndex = 0;
            console.log(`🎯 进入慢速移动模式，当前区域有${this.currentDetectedItems.length}个物品`);
          } // 🎯 检查是否超时，自动退出


          if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {
            this.exitSlowMoveMode();
            return this.currentDetectedItems[0];
          } // 🎯 检查是否应该切换到下一个物品


          if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {
            var _currentItem$node;

            this.currentItemIndex = (this.currentItemIndex + 1) % this.currentDetectedItems.length;
            this.lastItemSwitchTime = now;
            const currentItem = this.currentDetectedItems[this.currentItemIndex];
            console.log(`🔄 切换到物品: ${((_currentItem$node = currentItem.node) == null ? void 0 : _currentItem$node.name) || '未知'} (${this.currentItemIndex + 1}/${this.currentDetectedItems.length})`);
          }

          return this.currentDetectedItems[this.currentItemIndex] || this.currentDetectedItems[0];
        }
        /**
         * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品
         */


        performMultiLayerRaycast(x, y) {
          const startTime = performance.now();
          const camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;
          const ray = camera.screenPointToRay(x, y);
          const raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA;
          const allItems = [];
          const maxLayers = 3; // 最多检测3层，平衡性能和功能

          const disabledColliders = [];

          try {
            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (let layer = 0; layer < maxLayers; layer++) {
              if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                const result = PhysicsSystem.instance.raycastClosestResult;
                const hitNode = result.collider.node;
                const itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                  error: Error()
                }), ItemSceneViewComp) : ItemSceneViewComp);

                if (itemComp && itemComp.ItemModel) {
                  // 避免重复添加同一个物品
                  if (!allItems.find(item => item.node === hitNode)) {
                    allItems.push(itemComp);
                  } // 临时禁用这个碰撞体，以便检测下一层


                  const collider = result.collider;

                  if (collider.enabled) {
                    collider.enabled = false;
                    disabledColliders.push(collider);
                  }
                } else {
                  break; // 没有找到有效物品，停止检测
                }
              } else {
                break; // 没有更多碰撞，停止检测
              }
            }
          } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
              if (collider && collider.isValid) {
                collider.enabled = true;
              }
            });
          } // 🎯 记录性能数据


          const endTime = performance.now();
          const duration = endTime - startTime;

          if (duration > 3) {
            // 多层检测阈值稍高
            console.warn(`⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`);
          }

          return allItems;
        }
        /**
         * 🎯 退出慢速移动模式
         */


        exitSlowMoveMode() {
          if (this.isInSlowMoveMode) {
            this.isInSlowMoveMode = false;
            this.currentItemIndex = 0;
            this.currentDetectedItems = [];
            console.log('🏁 退出慢速移动模式，回到顶层');
          }
        }
        /**
         * 🎯 暂停物品切换 - 选择物品后暂停一段时间
         */


        pauseLayerSwitching() {
          this.exitSlowMoveMode();
          this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒

          console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);
        }
        /**
         * 🎯 清理过期缓存
         */


        cleanupCache() {
          if (this.raycastCache.size > 50) {
            const now = Date.now();
            const keysToDelete = [];
            this.raycastCache.forEach((value, key) => {
              if (now - value.timestamp > this.raycastCacheTimeout * 2) {
                keysToDelete.push(key);
              }
            });
            keysToDelete.forEach(key => this.raycastCache.delete(key));
          }
        }
        /**
         * 🎯 性能报告（如果需要）
         */


        reportPerformanceIfNeeded() {
          const now = Date.now();

          if (now - this.lastPerformanceReport > 10000) {
            // 每10秒报告一次
            const cacheHitRate = this.raycastCount > 0 ? this.cacheHitCount / this.raycastCount * 100 : 0;

            if (this.raycastCount > 0) {
              console.log(`🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`);

              if (cacheHitRate < 30) {
                console.warn(`⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`);
              }
            }

            this.lastPerformanceReport = now;
          }
        }
        /**
         * 应用触摸效果到物品
         */


        applyTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = true;

            if (typeof itemComp.onTouch === 'function') {
              itemComp.onTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);

          }
        }
        /**
         * 取消物品的触摸效果
         */


        cancelTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = false;

            if (typeof itemComp.onCancelTouch === 'function') {
              itemComp.onCancelTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);

          }
        }
        /**
         * 处理触摸开始事件
         */


        onTouchStart(event) {
          // 🚀 初始化移动位置缓存
          this.lastMovePosition.x = event.getLocationX();
          this.lastMovePosition.y = event.getLocationY();
          this.lastMoveTime = Date.now(); // 🎯 触摸开始时重置慢速移动模式

          this.exitSlowMoveMode(); // 高频交互日志注释掉，减少日志噪音
          // oops.log.logTrace('🎯 触摸开始');

          const hitItem = this.detectItemAtPosition(event);

          if (hitItem) {
            this.currentTouchedItem = hitItem;
            this.applyTouchEffect(hitItem);
          } else {// oops.log.logTrace('🎯 触摸开始：未击中任何物品');
          }
        }
        /**
         * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测
         */


        onTouchMove(event) {
          const currentTime = Date.now();
          const currentX = event.getLocationX();
          const currentY = event.getLocationY(); // 🎯 时间节流：限制检测频率到30FPS

          if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {
            return;
          } // 🎯 距离阈值：只有移动足够距离才触发检测


          const deltaX = Math.abs(currentX - this.lastMovePosition.x);
          const deltaY = Math.abs(currentY - this.lastMovePosition.y);
          const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

          if (distance < this.moveDistanceThreshold) {
            return;
          } // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率


          if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {
            // 更新位置但不执行射线检测，减少性能消耗
            this.lastMoveTime = currentTime;
            this.lastMovePosition.x = currentX;
            this.lastMovePosition.y = currentY;
            return;
          } // 更新缓存


          this.lastMoveTime = currentTime;
          this.lastMovePosition.x = currentX;
          this.lastMovePosition.y = currentY; // 🎯 执行射线检测（现在频率大大降低）

          const hitItem = this.detectItemAtPosition(event); // 如果当前触摸的物品和检测到的物品不同，需要切换

          if (this.currentTouchedItem !== hitItem) {
            // 取消之前物品的触摸效果
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } // 应用新物品的触摸效果


            if (hitItem) {
              this.applyTouchEffect(hitItem); // 高频交互日志注释掉，减少日志噪音
              // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);
            }

            this.currentTouchedItem = hitItem;
          }
        }
        /**
         * 处理触摸结束事件 - 智能版本
         */


        onTouchEnd(event) {
          // 🛡️ 防抖和状态检查
          if (!this.canProcessClick()) {
            // 清除触摸状态但不处理选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            }

            this.currentTouchedItem = null;
            return;
          } // 只有在物品上松手才算选中


          const hitItem = this.detectItemAtPosition(event);

          if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {
            var _this$currentTouchedI;

            // 在同一个物品上松手，检查是否可以选中
            this.cancelTouchEffect(this.currentTouchedItem);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`✅ 检测到物品选择: ${((_this$currentTouchedI = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI.name) || '未知物品'}`); // 🎯 智能选择检查

            if (this.intelligentItemSelection(this.currentTouchedItem)) {
              // 设置处理状态
              this.isProcessingClick = true;
              this.lastClickTime = Date.now(); // 触发选择逻辑

              if (this.gameEntity && this.gameEntity.interactionManager) {
                try {
                  var _this$currentTouchedI2;

                  this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness(`🎯 成功触发物品选择: ${((_this$currentTouchedI2 = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI2.name) || '未知物品'}`); // 🎯 选择物品后暂停层级切换

                  this.pauseLayerSwitching();
                } catch (error) {
                  console.error('❌ 物品选择过程中发生错误:', error);
                }
              } // 延迟重置处理状态


              setTimeout(() => {
                this.isProcessingClick = false;
              }, this.clickDelay);
            } else {}
          } else {
            // 不在原物品上松手，取消选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } else {// oops.log.logTrace('❌ 在空白区域松手，无选择');
            }
          } // 清除当前触摸状态


          this.currentTouchedItem = null;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cc9c89e02d4318577b41826c41affe2e4583b9e6.js.map