System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, input, Input, oops, ItemSceneViewComp, GameSceneViewComp, SimpleItemDetector, _dec, _class, _crd, ccclass, property, ItemInteractionManager;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemSceneViewComp(extras) {
    _reporterNs.report("ItemSceneViewComp", "../../item/view/ItemSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameSceneViewComp(extras) {
    _reporterNs.report("GameSceneViewComp", "../../sceneMgr/vIew/GameSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEntity(extras) {
    _reporterNs.report("GameEntity", "./GameEntity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSimpleItemDetector(extras) {
    _reporterNs.report("SimpleItemDetector", "./SimpleItemDetector", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      input = _cc.input;
      Input = _cc.Input;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      ItemSceneViewComp = _unresolved_3.ItemSceneViewComp;
    }, function (_unresolved_4) {
      GameSceneViewComp = _unresolved_4.GameSceneViewComp;
    }, function (_unresolved_5) {
      SimpleItemDetector = _unresolved_5.SimpleItemDetector;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "15b11JywPdI6Z9ridQfpm7x", "ItemInteractionManager", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'input', 'Input']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 物品交互管理器 - 智能触摸组件版本
       * 负责触摸事件的捕获和处理，与类版本协同工作
       */

      _export("ItemInteractionManager", ItemInteractionManager = (_dec = ccclass('ItemInteractionManager'), _dec(_class = class ItemInteractionManager extends Component {
        constructor(...args) {
          super(...args);
          this.gameEntity = null;
          this.currentTouchedItem = null;
          // 🎯 防抖和状态管理
          this.lastClickTime = 0;
          this.clickDelay = 100;
          // 100ms防抖延迟
          this.isProcessingClick = false;
          // 🚀 性能优化：触摸移动节流 - 增强版
          this.lastMoveTime = 0;
          this.moveThrottleDelay = 33;
          // 30FPS限制，约33ms (降低检测频率)
          this.lastMovePosition = {
            x: 0,
            y: 0
          };
          this.moveDistanceThreshold = 10;
          // 增加像素阈值到10px，减少不必要的检测
          // 🎯 射线检测缓存优化
          this.raycastCache = new Map();
          this.raycastCacheTimeout = 100;
          // 缓存100ms，减少重复计算
          this.lastRaycastPosition = {
            x: -1,
            y: -1
          };
          this.raycastPositionThreshold = 8;
          // 射线检测位置阈值
          // 🎯 简化配置 - 只保留必要的参数
          this.moveDistanceThreshold = 10;
          // 移动距离阈值，用于性能优化
          // 🎯 简单性能监控
          this.raycastCount = 0;
          this.cacheHitCount = 0;
          this.lastPerformanceReport = 0;
        }

        onLoad() {
          input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');
        }

        start() {
          // 在start中获取GameEntity，确保ent属性已经设置
          const gameSceneViewComp = this.node.getComponent(_crd && GameSceneViewComp === void 0 ? (_reportPossibleCrUseOfGameSceneViewComp({
            error: Error()
          }), GameSceneViewComp) : GameSceneViewComp);

          if (gameSceneViewComp && gameSceneViewComp.ent) {
            this.gameEntity = gameSceneViewComp.ent;
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`);
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');
          }
        }

        onDestroy() {
          input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this); // 🧹 清理射线检测缓存

          this.raycastCache.clear();
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');
        }
        /**
         * 🔍 检查是否可以处理点击
         */


        canProcessClick() {
          const currentTime = Date.now(); // 防抖检查

          if (currentTime - this.lastClickTime < this.clickDelay) {
            // console.log('🛡️ 点击过快，触发防抖保护');
            return false;
          } // 处理状态检查


          if (this.isProcessingClick) {
            // console.log('🛡️ 正在处理点击，跳过重复处理');
            return false;
          }

          return true;
        }
        /**
         * 🎯 智能物品选择逻辑
         */


        intelligentItemSelection(item) {
          if (!this.gameEntity || !this.gameEntity.interactionManager) {
            console.warn('⚠️ GameEntity或InteractionManager未准备好');
            return false;
          }

          const itemNode = item.node;
          const interactionManager = this.gameEntity.interactionManager; // 🔍 检查物品是否在收集槽管理器中

          if (interactionManager.slotManager) {
            const slot = interactionManager.slotManager.getSlotByItem(itemNode);

            if (slot) {
              // 物品在收集槽中
              if (slot.index >= 7) {
                // 在额外槽位(7-9)，可以选中移动到主槽位
                // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);
                return true;
              } else {
                // 在主槽位(0-6)，不能选中
                // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);
                return false;
              }
            }
          } // 🔍 检查物品是否在场景中可选择


          const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());

          if (!isSelectable) {
            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);
            return false;
          } // 🔍 检查收集槽是否已满


          if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {
            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);
            return false;
          }

          return true;
        }
        /**
         * 🚀 精确射线检测 - 使用简化检测器
         */


        detectItemAtPosition(event) {
          if (!this.gameEntity) {
            return null;
          } // 🎯 使用简化检测器 - 总是检测最前面的可见物品


          return (_crd && SimpleItemDetector === void 0 ? (_reportPossibleCrUseOfSimpleItemDetector({
            error: Error()
          }), SimpleItemDetector) : SimpleItemDetector).detectItem(event, 'precise');
        }
        /**
         * 🎯 精确射线检测 - 只检测最前面的可见物品
         */


        performPreciseRaycast(x, y) {
          const camera = smc.camera.CameraModel.camera;
          const ray = camera.screenPointToRay(x, y);
          const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA; // 🎯 只执行一次射线检测，获取最前面的物品

          if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
            const result = PhysicsSystem.instance.raycastClosestResult;
            const hitNode = result.collider.node;
            const itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
              error: Error()
            }), ItemSceneViewComp) : ItemSceneViewComp);

            if (itemComp && itemComp.ItemModel) {
              return itemComp;
            }
          }

          return null;
        }
        /**
         * 🎯 简化多层射线检测 - 只使用单点射线，逐层检测
         */


        performSimpleMultiLayerRaycast(x, y) {
          const camera = smc.camera.CameraModel.camera;
          const ray = camera.screenPointToRay(x, y);
          const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;
          const allItems = [];
          const maxLayers = 3; // 最多检测3层

          const disabledColliders = [];

          try {
            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (let layer = 0; layer < maxLayers; layer++) {
              if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                const result = PhysicsSystem.instance.raycastClosestResult;
                const hitNode = result.collider.node;
                const itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                  error: Error()
                }), ItemSceneViewComp) : ItemSceneViewComp);

                if (itemComp && itemComp.ItemModel) {
                  // 避免重复添加同一个物品
                  if (!allItems.find(item => item.node === hitNode)) {
                    allItems.push(itemComp);
                  } // 临时禁用这个碰撞体，以便检测下一层


                  const collider = result.collider;

                  if (collider.enabled) {
                    collider.enabled = false;
                    disabledColliders.push(collider);
                  }
                } else {
                  break; // 没有找到有效物品，停止检测
                }
              } else {
                break; // 没有更多碰撞，停止检测
              }
            }
          } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
              if (collider && collider.isValid) {
                collider.enabled = true;
              }
            });
          }

          return allItems;
        }
        /**
         * 🎯 改进的层级切换 - 需要持续慢速移动才触发
         */


        handleSimpleLayerSwitching(items, moveDistance) {
          if (items.length <= 1) {
            return items[0] || null;
          }

          const now = Date.now();
          const isSlowMove = moveDistance < this.slowMoveThreshold; // 🎯 如果不是慢速移动，直接返回顶层物品

          if (!isSlowMove) {
            this.exitSlowMoveMode();
            return items[0];
          } // 🎯 开始记录慢速移动时间


          if (!this.isInSlowMoveMode) {
            this.isInSlowMoveMode = true;
            this.slowMoveStartTime = now;
            this.currentItemIndex = 0;
            this.candidateItems = items.map(item => ({
              item,
              score: 100
            })); // 不立即开始切换，先返回顶层物品

            return items[0];
          } // 🎯 检查是否持续慢速移动足够长时间


          const slowMoveDuration = now - this.slowMoveStartTime;

          if (slowMoveDuration < this.slowMoveRequiredTime) {
            // 还没有持续足够长时间，继续返回顶层物品
            return items[0];
          } // 🎯 现在开始层级切换


          if (slowMoveDuration >= this.slowMoveRequiredTime) {
            // 第一次进入切换模式
            if (this.lastItemSwitchTime === 0) {
              this.lastItemSwitchTime = now;
              console.log(`🎯 持续慢速移动${this.slowMoveRequiredTime}ms，开始层级切换 (共${items.length}层)`);
            } // 检查是否应该切换到下一层


            if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {
              var _items$this$currentIt;

              this.currentItemIndex = (this.currentItemIndex + 1) % items.length;
              this.lastItemSwitchTime = now;
              console.log(`🔄 切换到第${this.currentItemIndex + 1}层: ${((_items$this$currentIt = items[this.currentItemIndex].node) == null ? void 0 : _items$this$currentIt.name) || '未知'}`);
            }
          } // 🎯 检查是否超时


          if (slowMoveDuration > this.maxSlowMoveTime) {
            this.exitSlowMoveMode();
            return items[0]; // 返回顶层物品
          }

          return items[this.currentItemIndex] || items[0];
        }
        /**
         * 🎯 合并和评分物品
         */


        mergeAndScoreItems(raycastItems, screenItems, touchX, touchY) {
          const candidates = new Map();
          const camera = smc.camera.CameraModel.camera; // 处理射线检测结果（高优先级）

          for (const item of raycastItems) {
            const worldPos = item.node.worldPosition;
            const screenPos = camera.worldToScreen(worldPos);
            const distance = Math.sqrt(Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2));
            candidates.set(item.node.uuid, {
              item,
              score: 100 - distance * 0.5 // 射线检测基础分100，距离越近分数越高

            });
          } // 处理屏幕距离检测结果（低优先级）


          for (const item of screenItems) {
            if (!candidates.has(item.node.uuid)) {
              const worldPos = item.node.worldPosition;
              const screenPos = camera.worldToScreen(worldPos);
              const distance = Math.sqrt(Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2));
              candidates.set(item.node.uuid, {
                item,
                score: 50 - distance * 0.3 // 屏幕距离基础分50

              });
            }
          }

          return Array.from(candidates.values());
        }
        /**
         * 🎯 选择最佳物品
         */


        selectBestItem(candidates) {
          if (candidates.length === 0) {
            return null;
          } // 按分数排序，选择最高分的物品


          candidates.sort((a, b) => b.score - a.score);
          const bestCandidate = candidates[0]; // 如果有多个候选物品，可以在这里实现慢速移动时的切换逻辑

          if (candidates.length > 1) {
            return this.handleMultipleCandidates(candidates);
          }

          return bestCandidate.item;
        }
        /**
         * 🎯 处理多个候选物品
         */


        handleMultipleCandidates(candidates) {
          var _candidates$this$curr;

          const now = Date.now(); // 检查是否为慢速移动模式

          if (!this.isInSlowMoveMode) {
            this.isInSlowMoveMode = true;
            this.slowMoveStartTime = now;
            this.candidateItems = candidates;
            this.currentItemIndex = 0;
            console.log(`🎯 检测到${candidates.length}个候选物品，进入慢速选择模式`);
          } // 检查是否超时


          if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {
            this.exitSlowMoveMode();
            return candidates[0].item; // 返回最高分物品
          } // 检查是否应该切换


          if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {
            var _currentItem$item$nod;

            this.currentItemIndex = (this.currentItemIndex + 1) % candidates.length;
            this.lastItemSwitchTime = now;
            const currentItem = candidates[this.currentItemIndex];
            console.log(`🔄 切换到候选物品: ${((_currentItem$item$nod = currentItem.item.node) == null ? void 0 : _currentItem$item$nod.name) || '未知'} (${this.currentItemIndex + 1}/${candidates.length})`);
          }

          return ((_candidates$this$curr = candidates[this.currentItemIndex]) == null ? void 0 : _candidates$this$curr.item) || candidates[0].item;
        }
        /**
         * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品
         */


        performMultiLayerRaycast(x, y) {
          const startTime = performance.now();
          const camera = smc.camera.CameraModel.camera;
          const ray = camera.screenPointToRay(x, y);
          const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;
          const allItems = [];
          const maxLayers = 3; // 最多检测3层，平衡性能和功能

          const disabledColliders = [];

          try {
            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (let layer = 0; layer < maxLayers; layer++) {
              if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                const result = PhysicsSystem.instance.raycastClosestResult;
                const hitNode = result.collider.node;
                const itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                  error: Error()
                }), ItemSceneViewComp) : ItemSceneViewComp);

                if (itemComp && itemComp.ItemModel) {
                  // 避免重复添加同一个物品
                  if (!allItems.find(item => item.node === hitNode)) {
                    allItems.push(itemComp);
                  } // 临时禁用这个碰撞体，以便检测下一层


                  const collider = result.collider;

                  if (collider.enabled) {
                    collider.enabled = false;
                    disabledColliders.push(collider);
                  }
                } else {
                  break; // 没有找到有效物品，停止检测
                }
              } else {
                break; // 没有更多碰撞，停止检测
              }
            }
          } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
              if (collider && collider.isValid) {
                collider.enabled = true;
              }
            });
          } // 🎯 记录性能数据


          const endTime = performance.now();
          const duration = endTime - startTime;

          if (duration > 3) {
            // 多层检测阈值稍高
            console.warn(`⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`);
          }

          return allItems;
        }
        /**
         * 🎯 退出慢速移动模式
         */


        exitSlowMoveMode() {
          if (this.isInSlowMoveMode) {
            this.isInSlowMoveMode = false;
            this.currentItemIndex = 0;
            this.candidateItems = [];
            this.lastItemSwitchTime = 0; // 重置切换时间

            console.log('🏁 退出慢速移动模式，回到顶层');
          }
        }
        /**
         * 🎯 暂停物品切换 - 选择物品后暂停一段时间
         */


        pauseLayerSwitching() {
          this.exitSlowMoveMode();
          this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒

          console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);
        }
        /**
         * 🎯 清理过期缓存
         */


        cleanupCache() {
          if (this.raycastCache.size > 50) {
            const now = Date.now();
            const keysToDelete = [];
            this.raycastCache.forEach((value, key) => {
              if (now - value.timestamp > this.raycastCacheTimeout * 2) {
                keysToDelete.push(key);
              }
            });
            keysToDelete.forEach(key => this.raycastCache.delete(key));
          }
        }
        /**
         * 🎯 性能报告（如果需要）
         */


        reportPerformanceIfNeeded() {
          const now = Date.now();

          if (now - this.lastPerformanceReport > 10000) {
            // 每10秒报告一次
            const cacheHitRate = this.raycastCount > 0 ? this.cacheHitCount / this.raycastCount * 100 : 0;

            if (this.raycastCount > 0) {
              console.log(`🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`);

              if (cacheHitRate < 30) {
                console.warn(`⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`);
              }
            }

            this.lastPerformanceReport = now;
          }
        }
        /**
         * 应用触摸效果到物品
         */


        applyTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = true;

            if (typeof itemComp.onTouch === 'function') {
              itemComp.onTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);

          }
        }
        /**
         * 取消物品的触摸效果
         */


        cancelTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = false;

            if (typeof itemComp.onCancelTouch === 'function') {
              itemComp.onCancelTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);

          }
        }
        /**
         * 处理触摸开始事件
         */


        onTouchStart(event) {
          // 🚀 初始化移动位置缓存
          this.lastMovePosition.x = event.getLocationX();
          this.lastMovePosition.y = event.getLocationY();
          this.lastMoveTime = Date.now(); // 🎯 触摸开始时重置慢速移动模式

          this.exitSlowMoveMode(); // 高频交互日志注释掉，减少日志噪音
          // oops.log.logTrace('🎯 触摸开始');

          const hitItem = this.detectItemAtPosition(event);

          if (hitItem) {
            this.currentTouchedItem = hitItem;
            this.applyTouchEffect(hitItem);
          } else {// oops.log.logTrace('🎯 触摸开始：未击中任何物品');
          }
        }
        /**
         * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测
         */


        onTouchMove(event) {
          const currentTime = Date.now();
          const currentX = event.getLocationX();
          const currentY = event.getLocationY(); // 🎯 时间节流：限制检测频率到30FPS

          if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {
            return;
          } // 🎯 距离阈值：只有移动足够距离才触发检测


          const deltaX = Math.abs(currentX - this.lastMovePosition.x);
          const deltaY = Math.abs(currentY - this.lastMovePosition.y);
          const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

          if (distance < this.moveDistanceThreshold) {
            return;
          } // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率


          if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {
            // 更新位置但不执行射线检测，减少性能消耗
            this.lastMoveTime = currentTime;
            this.lastMovePosition.x = currentX;
            this.lastMovePosition.y = currentY;
            return;
          } // 更新缓存


          this.lastMoveTime = currentTime;
          this.lastMovePosition.x = currentX;
          this.lastMovePosition.y = currentY; // 🎯 执行射线检测（现在频率大大降低）

          const hitItem = this.detectItemAtPosition(event); // 如果当前触摸的物品和检测到的物品不同，需要切换

          if (this.currentTouchedItem !== hitItem) {
            // 取消之前物品的触摸效果
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } // 应用新物品的触摸效果


            if (hitItem) {
              this.applyTouchEffect(hitItem); // 高频交互日志注释掉，减少日志噪音
              // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);
            }

            this.currentTouchedItem = hitItem;
          }
        }
        /**
         * 处理触摸结束事件 - 智能版本
         */


        onTouchEnd(event) {
          // 🛡️ 防抖和状态检查
          if (!this.canProcessClick()) {
            // 清除触摸状态但不处理选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            }

            this.currentTouchedItem = null;
            return;
          } // 只有在物品上松手才算选中


          const hitItem = this.detectItemAtPosition(event);

          if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {
            var _this$currentTouchedI;

            // 在同一个物品上松手，检查是否可以选中
            this.cancelTouchEffect(this.currentTouchedItem);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`✅ 检测到物品选择: ${((_this$currentTouchedI = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI.name) || '未知物品'}`); // 🎯 智能选择检查

            if (this.intelligentItemSelection(this.currentTouchedItem)) {
              // 设置处理状态
              this.isProcessingClick = true;
              this.lastClickTime = Date.now(); // 触发选择逻辑

              if (this.gameEntity && this.gameEntity.interactionManager) {
                try {
                  var _this$currentTouchedI2;

                  this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness(`🎯 成功触发物品选择: ${((_this$currentTouchedI2 = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI2.name) || '未知物品'}`); // 🎯 选择物品后暂停层级切换

                  this.pauseLayerSwitching();
                } catch (error) {
                  console.error('❌ 物品选择过程中发生错误:', error);
                }
              } // 延迟重置处理状态


              setTimeout(() => {
                this.isProcessingClick = false;
              }, this.clickDelay);
            } else {}
          } else {
            // 不在原物品上松手，取消选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } else {// oops.log.logTrace('❌ 在空白区域松手，无选择');
            }
          } // 清除当前触摸状态


          this.currentTouchedItem = null;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cc9c89e02d4318577b41826c41affe2e4583b9e6.js.map