{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "PhysicsSystem", "oops", "PHY_GROUP", "smc", "ItemSceneViewComp", "GameSceneViewComp", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "lastRaycastPosition", "lastRaycastTime", "raycastThrottleDelay", "lastDetectedItem", "isLongPressing", "longPressStartTime", "long<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longPressRaycastDelay", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "raycastCache", "clear", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "currentX", "getLocationX", "currentY", "getLocationY", "deltaX", "Math", "abs", "deltaY", "distance", "sqrt", "raycastDelay", "detectedItem", "performOptimizedRaycast", "camera", "CameraModel", "ray", "screenPointToRay", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "instance", "raycastClosest", "result", "raycastClosestResult", "hitNode", "collider", "itemComp", "ItemModel", "performSimpleMultiLayerRaycast", "allItems", "maxLayers", "disabledColliders", "layer", "find", "push", "enabled", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "handleSimpleLayerSwitching", "items", "moveDistance", "length", "isSlowMove", "slowMoveThreshold", "exitSlowMoveMode", "isInSlowMoveMode", "slowMoveStartTime", "currentItemIndex", "candidate<PERSON><PERSON>s", "map", "score", "slowMoveDuration", "slowMoveRequiredTime", "lastItemSwitchTime", "itemSwitchDelay", "name", "maxSlowMoveTime", "mergeAndScoreItems", "raycastItems", "screenItems", "touchX", "touchY", "candidates", "Map", "worldPos", "worldPosition", "screenPos", "worldToScreen", "pow", "set", "uuid", "Array", "from", "values", "selectBestItem", "sort", "a", "b", "bestCandidate", "handleMultipleCandidates", "currentItem", "performMultiLayerRaycast", "startTime", "performance", "endTime", "duration", "toFixed", "pauseLayerSwitching", "cleanupCache", "size", "keysToDelete", "value", "key", "timestamp", "raycastCacheTimeout", "delete", "reportPerformanceIfNeeded", "lastPerformanceReport", "cacheHitRate", "raycastCount", "cacheHitCount", "applyTouchEffect", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "hitItem", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,a,OAAAA,a;;AACjDC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;wCAEaY,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CX,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CY,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,EAbU;AAaN;AAE5C;AAfkD,eAgB1CC,mBAhB0C,GAgBM;AAAEH,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAhBN;AAAA,eAiB1CG,eAjB0C,GAiBhB,CAjBgB;AAAA,eAkB1CC,oBAlB0C,GAkBX,EAlBW;AAkBP;AAlBO,eAmB1CH,qBAnB0C,GAmBV,CAnBU;AAmBP;AAnBO,eAoB1CI,gBApB0C,GAoBG,IApBH;AAsBlD;AAtBkD,eAuB1CC,cAvB0C,GAuBhB,KAvBgB;AAAA,eAwB1CC,kBAxB0C,GAwBb,CAxBa;AAAA,eAyB1CC,kBAzB0C,GAyBb,GAzBa;AAyBR;AAzBQ,eA0B1CC,qBA1B0C,GA0BV,EA1BU;AAAA;;AA0BN;AAE5CC,QAAAA,MAAM,GAAG;AACL9B,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACAlC,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACApC,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAKlC,UAAL,GAAkB+B,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,CACK,0CAAyC,KAAK7B,UAAL,GAAkB,IAAlB,GAAyB,IAAK,EAD5E;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAK4B,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR/C,UAAAA,KAAK,CAACgD,GAAN,CAAU/C,KAAK,CAAC+B,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACAlC,UAAAA,KAAK,CAACgD,GAAN,CAAU/C,KAAK,CAAC+B,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACApC,UAAAA,KAAK,CAACgD,GAAN,CAAU/C,KAAK,CAAC+B,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD,EAHQ,CAKR;;AACA,eAAKW,YAAL,CAAkBC,KAAlB;AACA;AAAA;AAAA,4BAAKX,GAAL,CAASC,WAAT,CAAqB,uCAArB;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,eAAe,GAAY;AAC/B,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAKvC,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYwC,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAK7C,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgB8C,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,gBAAMC,QAAQ,GAAGJ,IAAI,CAACb,IAAtB;AACA,gBAAMc,kBAAkB,GAAG,KAAK9C,UAAL,CAAgB8C,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,kBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,gBAAMC,YAAY,GAAG,KAAKtD,UAAL,CAAgBuD,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAK7D,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH;;AAED,gBAAM8D,QAAQ,GAAGD,KAAK,CAACE,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,gBAAMtB,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CAPsE,CAStE;;AACA,gBAAMuB,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKnD,mBAAL,CAAyBH,CAA7C,CAAf;AACA,gBAAM6D,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKrD,mBAAL,CAAyBF,CAA7C,CAAf;AACA,gBAAM6D,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB,CAZsE,CActE;;AACA,cACIC,QAAQ,GAAG,KAAK5D,qBAAhB,IACAiC,GAAG,GAAG,KAAK/B,eAAX,GAA6B,KAAKC,oBADlC,IAEA,KAAKC,gBAHT,EAIE;AACE,mBAAO,KAAKA,gBAAZ;AACH,WArBqE,CAuBtE;;;AACA,gBAAM0D,YAAY,GAAG,KAAKzD,cAAL,GACf,KAAKG,qBADU,GAEf,KAAKL,oBAFX;;AAGA,cAAI8B,GAAG,GAAG,KAAK/B,eAAX,GAA6B4D,YAAjC,EAA+C;AAC3C,mBAAO,KAAK1D,gBAAZ;AACH,WA7BqE,CA+BtE;;;AACA,gBAAM2D,YAAY,GAAG,KAAKC,uBAAL,CAA6BZ,QAA7B,EAAuCE,QAAvC,CAArB,CAhCsE,CAkCtE;;AACA,eAAKrD,mBAAL,CAAyBH,CAAzB,GAA6BsD,QAA7B;AACA,eAAKnD,mBAAL,CAAyBF,CAAzB,GAA6BuD,QAA7B;AACA,eAAKpD,eAAL,GAAuB+B,GAAvB;AACA,eAAK7B,gBAAL,GAAwB2D,YAAxB;AAEA,iBAAOA,YAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,uBAAuB,CAAClE,CAAD,EAAYC,CAAZ,EAAiD;AAC5E,gBAAMkE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,gBAAME,GAAG,GAAGF,MAAM,CAACG,gBAAP,CAAwBtE,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAMsE,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE,CAP4E,CAS5E;;AACA,cAAI3F,aAAa,CAAC4F,QAAd,CAAuBC,cAAvB,CAAsCP,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,kBAAMM,MAAM,GAAG9F,aAAa,CAAC4F,QAAd,CAAuBG,oBAAtC;AACA,kBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgBxD,IAAhC;AACA,kBAAMyD,QAAQ,GAAGF,OAAO,CAACtD,YAAR;AAAA;AAAA,uDAAjB;;AAEA,gBAAIwD,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC,qBAAOD,QAAP;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYE,QAAAA,8BAA8B,CAACnF,CAAD,EAAYC,CAAZ,EAA4C;AAC9E,gBAAMkE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,gBAAME,GAAG,GAAGF,MAAM,CAACG,gBAAP,CAAwBtE,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAMsE,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,gBAAMU,QAA6B,GAAG,EAAtC;AACA,gBAAMC,SAAS,GAAG,CAAlB,CAN8E,CAMzD;;AACrB,gBAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AACA;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,SAA5B,EAAuCE,KAAK,EAA5C,EAAgD;AAC5C,kBAAIxG,aAAa,CAAC4F,QAAd,CAAuBC,cAAvB,CAAsCP,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,sBAAMM,MAAM,GAAG9F,aAAa,CAAC4F,QAAd,CAAuBG,oBAAtC;AACA,sBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgBxD,IAAhC;AACA,sBAAMyD,QAAQ,GAAGF,OAAO,CAACtD,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAIwD,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACE,QAAQ,CAACI,IAAT,CAAcnD,IAAI,IAAIA,IAAI,CAACb,IAAL,KAAcuD,OAApC,CAAL,EAAmD;AAC/CK,oBAAAA,QAAQ,CAACK,IAAT,CAAcR,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,wBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACU,OAAb,EAAsB;AAClBV,oBAAAA,QAAQ,CAACU,OAAT,GAAmB,KAAnB;AACAJ,oBAAAA,iBAAiB,CAACG,IAAlB,CAAuBT,QAAvB;AACH;AACJ,iBAZD,MAYO;AACH,wBADG,CACI;AACV;AACJ,eApBD,MAoBO;AACH,sBADG,CACI;AACV;AACJ;AACJ,WA3BD,SA2BU;AACN;AACAM,YAAAA,iBAAiB,CAACK,OAAlB,CAA0BX,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACY,OAAzB,EAAkC;AAC9BZ,gBAAAA,QAAQ,CAACU,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH;;AAED,iBAAON,QAAP;AACH;AAED;AACJ;AACA;;;AACYS,QAAAA,0BAA0B,CAC9BC,KAD8B,EAE9BC,YAF8B,EAGN;AACxB,cAAID,KAAK,CAACE,MAAN,IAAgB,CAApB,EAAuB;AACnB,mBAAOF,KAAK,CAAC,CAAD,CAAL,IAAY,IAAnB;AACH;;AAED,gBAAM3D,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,gBAAM8D,UAAU,GAAGF,YAAY,GAAG,KAAKG,iBAAvC,CANwB,CAQxB;;AACA,cAAI,CAACD,UAAL,EAAiB;AACb,iBAAKE,gBAAL;AACA,mBAAOL,KAAK,CAAC,CAAD,CAAZ;AACH,WAZuB,CAcxB;;;AACA,cAAI,CAAC,KAAKM,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyBlE,GAAzB;AACA,iBAAKmE,gBAAL,GAAwB,CAAxB;AACA,iBAAKC,cAAL,GAAsBT,KAAK,CAACU,GAAN,CAAUnE,IAAI,KAAK;AAAEA,cAAAA,IAAF;AAAQoE,cAAAA,KAAK,EAAE;AAAf,aAAL,CAAd,CAAtB,CAJwB,CAKxB;;AACA,mBAAOX,KAAK,CAAC,CAAD,CAAZ;AACH,WAtBuB,CAwBxB;;;AACA,gBAAMY,gBAAgB,GAAGvE,GAAG,GAAG,KAAKkE,iBAApC;;AACA,cAAIK,gBAAgB,GAAG,KAAKC,oBAA5B,EAAkD;AAC9C;AACA,mBAAOb,KAAK,CAAC,CAAD,CAAZ;AACH,WA7BuB,CA+BxB;;;AACA,cAAIY,gBAAgB,IAAI,KAAKC,oBAA7B,EAAmD;AAC/C;AACA,gBAAI,KAAKC,kBAAL,KAA4B,CAAhC,EAAmC;AAC/B,mBAAKA,kBAAL,GAA0BzE,GAA1B;AACAI,cAAAA,OAAO,CAACnB,GAAR,CACK,YAAW,KAAKuF,oBAAqB,eAAcb,KAAK,CAACE,MAAO,IADrE;AAGH,aAP8C,CAS/C;;;AACA,gBAAI7D,GAAG,GAAG,KAAKyE,kBAAX,GAAgC,KAAKC,eAAzC,EAA0D;AAAA;;AACtD,mBAAKP,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8BR,KAAK,CAACE,MAA5D;AACA,mBAAKY,kBAAL,GAA0BzE,GAA1B;AAEAI,cAAAA,OAAO,CAACnB,GAAR,CACK,UAAS,KAAKkF,gBAAL,GAAwB,CAAE,MAAK,0BAAAR,KAAK,CAAC,KAAKQ,gBAAN,CAAL,CAA6B9E,IAA7B,2CAAmCsF,IAAnC,KAA2C,IAAK,EAD7F;AAGH;AACJ,WAlDuB,CAoDxB;;;AACA,cAAIJ,gBAAgB,GAAG,KAAKK,eAA5B,EAA6C;AACzC,iBAAKZ,gBAAL;AACA,mBAAOL,KAAK,CAAC,CAAD,CAAZ,CAFyC,CAExB;AACpB;;AAED,iBAAOA,KAAK,CAAC,KAAKQ,gBAAN,CAAL,IAAgCR,KAAK,CAAC,CAAD,CAA5C;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,kBAAkB,CACtBC,YADsB,EAEtBC,WAFsB,EAGtBC,MAHsB,EAItBC,MAJsB,EAK2B;AACjD,gBAAMC,UAAU,GAAG,IAAIC,GAAJ,EAAnB;AACA,gBAAMnD,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC,CAFiD,CAIjD;;AACA,eAAK,MAAM9B,IAAX,IAAmB4E,YAAnB,EAAiC;AAC7B,kBAAMM,QAAQ,GAAGlF,IAAI,CAACb,IAAL,CAAUgG,aAA3B;AACA,kBAAMC,SAAS,GAAGtD,MAAM,CAACuD,aAAP,CAAqBH,QAArB,CAAlB;AACA,kBAAMzD,QAAQ,GAAGH,IAAI,CAACI,IAAL,CACbJ,IAAI,CAACgE,GAAL,CAASF,SAAS,CAACzH,CAAV,GAAcmH,MAAvB,EAA+B,CAA/B,IAAoCxD,IAAI,CAACgE,GAAL,CAASF,SAAS,CAACxH,CAAV,GAAcmH,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;AAIAC,YAAAA,UAAU,CAACO,GAAX,CAAevF,IAAI,CAACb,IAAL,CAAUqG,IAAzB,EAA+B;AAC3BxF,cAAAA,IAD2B;AAE3BoE,cAAAA,KAAK,EAAE,MAAM3C,QAAQ,GAAG,GAFG,CAEE;;AAFF,aAA/B;AAIH,WAhBgD,CAkBjD;;;AACA,eAAK,MAAMzB,IAAX,IAAmB6E,WAAnB,EAAgC;AAC5B,gBAAI,CAACG,UAAU,CAACpE,GAAX,CAAeZ,IAAI,CAACb,IAAL,CAAUqG,IAAzB,CAAL,EAAqC;AACjC,oBAAMN,QAAQ,GAAGlF,IAAI,CAACb,IAAL,CAAUgG,aAA3B;AACA,oBAAMC,SAAS,GAAGtD,MAAM,CAACuD,aAAP,CAAqBH,QAArB,CAAlB;AACA,oBAAMzD,QAAQ,GAAGH,IAAI,CAACI,IAAL,CACbJ,IAAI,CAACgE,GAAL,CAASF,SAAS,CAACzH,CAAV,GAAcmH,MAAvB,EAA+B,CAA/B,IAAoCxD,IAAI,CAACgE,GAAL,CAASF,SAAS,CAACxH,CAAV,GAAcmH,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;AAIAC,cAAAA,UAAU,CAACO,GAAX,CAAevF,IAAI,CAACb,IAAL,CAAUqG,IAAzB,EAA+B;AAC3BxF,gBAAAA,IAD2B;AAE3BoE,gBAAAA,KAAK,EAAE,KAAK3C,QAAQ,GAAG,GAFI,CAEC;;AAFD,eAA/B;AAIH;AACJ;;AAED,iBAAOgE,KAAK,CAACC,IAAN,CAAWV,UAAU,CAACW,MAAX,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,cAAc,CAClBZ,UADkB,EAEM;AACxB,cAAIA,UAAU,CAACrB,MAAX,KAAsB,CAA1B,EAA6B;AACzB,mBAAO,IAAP;AACH,WAHuB,CAKxB;;;AACAqB,UAAAA,UAAU,CAACa,IAAX,CAAgB,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAAC3B,KAAF,GAAU0B,CAAC,CAAC1B,KAAtC;AAEA,gBAAM4B,aAAa,GAAGhB,UAAU,CAAC,CAAD,CAAhC,CARwB,CAUxB;;AACA,cAAIA,UAAU,CAACrB,MAAX,GAAoB,CAAxB,EAA2B;AACvB,mBAAO,KAAKsC,wBAAL,CAA8BjB,UAA9B,CAAP;AACH;;AAED,iBAAOgB,aAAa,CAAChG,IAArB;AACH;AAED;AACJ;AACA;;;AACYiG,QAAAA,wBAAwB,CAC5BjB,UAD4B,EAEJ;AAAA;;AACxB,gBAAMlF,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CADwB,CAGxB;;AACA,cAAI,CAAC,KAAKiE,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyBlE,GAAzB;AACA,iBAAKoE,cAAL,GAAsBc,UAAtB;AACA,iBAAKf,gBAAL,GAAwB,CAAxB;AACA/D,YAAAA,OAAO,CAACnB,GAAR,CAAa,SAAQiG,UAAU,CAACrB,MAAO,gBAAvC;AACH,WAVuB,CAYxB;;;AACA,cAAI7D,GAAG,GAAG,KAAKkE,iBAAX,GAA+B,KAAKU,eAAxC,EAAyD;AACrD,iBAAKZ,gBAAL;AACA,mBAAOkB,UAAU,CAAC,CAAD,CAAV,CAAchF,IAArB,CAFqD,CAE1B;AAC9B,WAhBuB,CAkBxB;;;AACA,cAAIF,GAAG,GAAG,KAAKyE,kBAAX,GAAgC,KAAKC,eAAzC,EAA0D;AAAA;;AACtD,iBAAKP,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8Be,UAAU,CAACrB,MAAjE;AACA,iBAAKY,kBAAL,GAA0BzE,GAA1B;AAEA,kBAAMoG,WAAW,GAAGlB,UAAU,CAAC,KAAKf,gBAAN,CAA9B;AACA/D,YAAAA,OAAO,CAACnB,GAAR,CACK,eAAc,0BAAAmH,WAAW,CAAClG,IAAZ,CAAiBb,IAAjB,2CAAuBsF,IAAvB,KAA+B,IAAK,KAAI,KAAKR,gBAAL,GAAwB,CAAE,IAAGe,UAAU,CAACrB,MAAO,GAD1G;AAGH;;AAED,iBAAO,0BAAAqB,UAAU,CAAC,KAAKf,gBAAN,CAAV,2CAAmCjE,IAAnC,KAA2CgF,UAAU,CAAC,CAAD,CAAV,CAAchF,IAAhE;AACH;AAED;AACJ;AACA;;;AACYmG,QAAAA,wBAAwB,CAACxI,CAAD,EAAYC,CAAZ,EAA4C;AACxE,gBAAMwI,SAAS,GAAGC,WAAW,CAACvG,GAAZ,EAAlB;AACA,gBAAMgC,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,gBAAME,GAAG,GAAGF,MAAM,CAACG,gBAAP,CAAwBtE,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAMsE,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,gBAAMU,QAA6B,GAAG,EAAtC;AACA,gBAAMC,SAAS,GAAG,CAAlB,CAPwE,CAOnD;;AACrB,gBAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AACA;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,SAA5B,EAAuCE,KAAK,EAA5C,EAAgD;AAC5C,kBAAIxG,aAAa,CAAC4F,QAAd,CAAuBC,cAAvB,CAAsCP,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,sBAAMM,MAAM,GAAG9F,aAAa,CAAC4F,QAAd,CAAuBG,oBAAtC;AACA,sBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgBxD,IAAhC;AACA,sBAAMyD,QAAQ,GAAGF,OAAO,CAACtD,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAIwD,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACE,QAAQ,CAACI,IAAT,CAAcnD,IAAI,IAAIA,IAAI,CAACb,IAAL,KAAcuD,OAApC,CAAL,EAAmD;AAC/CK,oBAAAA,QAAQ,CAACK,IAAT,CAAcR,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,wBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACU,OAAb,EAAsB;AAClBV,oBAAAA,QAAQ,CAACU,OAAT,GAAmB,KAAnB;AACAJ,oBAAAA,iBAAiB,CAACG,IAAlB,CAAuBT,QAAvB;AACH;AACJ,iBAZD,MAYO;AACH,wBADG,CACI;AACV;AACJ,eApBD,MAoBO;AACH,sBADG,CACI;AACV;AACJ;AACJ,WA3BD,SA2BU;AACN;AACAM,YAAAA,iBAAiB,CAACK,OAAlB,CAA0BX,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACY,OAAzB,EAAkC;AAC9BZ,gBAAAA,QAAQ,CAACU,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH,WA5CuE,CA8CxE;;;AACA,gBAAMiD,OAAO,GAAGD,WAAW,CAACvG,GAAZ,EAAhB;AACA,gBAAMyG,QAAQ,GAAGD,OAAO,GAAGF,SAA3B;;AAEA,cAAIG,QAAQ,GAAG,CAAf,EAAkB;AACd;AACArG,YAAAA,OAAO,CAACC,IAAR,CACK,gBAAeoG,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CAAoB,SAAQzD,QAAQ,CAACY,MAAO,KADhE;AAGH;;AAED,iBAAOZ,QAAP;AACH;AAED;AACJ;AACA;;;AACYe,QAAAA,gBAAgB,GAAS;AAC7B,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,GAAwB,KAAxB;AACA,iBAAKE,gBAAL,GAAwB,CAAxB;AACA,iBAAKC,cAAL,GAAsB,EAAtB;AACA,iBAAKK,kBAAL,GAA0B,CAA1B,CAJuB,CAIM;;AAC7BrE,YAAAA,OAAO,CAACnB,GAAR,CAAY,kBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACY0H,QAAAA,mBAAmB,GAAS;AAChC,eAAK3C,gBAAL;AACA,eAAKS,kBAAL,GAA0B1E,IAAI,CAACC,GAAL,KAAa,IAAvC,CAFgC,CAEa;;AAC7CI,UAAAA,OAAO,CAACnB,GAAR,CAAa,wBAAb;AACH;AAED;AACJ;AACA;;;AACY2H,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKjH,YAAL,CAAkBkH,IAAlB,GAAyB,EAA7B,EAAiC;AAC7B,kBAAM7G,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,kBAAM8G,YAAsB,GAAG,EAA/B;AAEA,iBAAKnH,YAAL,CAAkB6D,OAAlB,CAA0B,CAACuD,KAAD,EAAQC,GAAR,KAAgB;AACtC,kBAAIhH,GAAG,GAAG+G,KAAK,CAACE,SAAZ,GAAwB,KAAKC,mBAAL,GAA2B,CAAvD,EAA0D;AACtDJ,gBAAAA,YAAY,CAACxD,IAAb,CAAkB0D,GAAlB;AACH;AACJ,aAJD;AAMAF,YAAAA,YAAY,CAACtD,OAAb,CAAqBwD,GAAG,IAAI,KAAKrH,YAAL,CAAkBwH,MAAlB,CAAyBH,GAAzB,CAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACYI,QAAAA,yBAAyB,GAAS;AACtC,gBAAMpH,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;;AACA,cAAIA,GAAG,GAAG,KAAKqH,qBAAX,GAAmC,KAAvC,EAA8C;AAC1C;AACA,kBAAMC,YAAY,GACd,KAAKC,YAAL,GAAoB,CAApB,GAAyB,KAAKC,aAAL,GAAqB,KAAKD,YAA3B,GAA2C,GAAnE,GAAyE,CAD7E;;AAGA,gBAAI,KAAKA,YAAL,GAAoB,CAAxB,EAA2B;AACvBnH,cAAAA,OAAO,CAACnB,GAAR,CACK,oBAAmB,KAAKsI,YAAa,WAAUD,YAAY,CAACZ,OAAb,CAAqB,CAArB,CAAwB,GAD5E;;AAIA,kBAAIY,YAAY,GAAG,EAAnB,EAAuB;AACnBlH,gBAAAA,OAAO,CAACC,IAAR,CACK,eAAciH,YAAY,CAACZ,OAAb,CAAqB,CAArB,CAAwB,YAD3C;AAGH;AACJ;;AAED,iBAAKW,qBAAL,GAA6BrH,GAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACYyH,QAAAA,gBAAgB,CAAC3E,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAACD,QAAQ,CAACC,SAAT,CAAmB2E,QAA1D,EAAoE;AAChE5E,YAAAA,QAAQ,CAACC,SAAT,CAAmB2E,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAO5E,QAAQ,CAAC6E,OAAhB,KAA4B,UAAhC,EAA4C;AACxC7E,cAAAA,QAAQ,CAAC6E,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAAC9E,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkCD,QAAQ,CAACC,SAAT,CAAmB2E,QAAzD,EAAmE;AAC/D5E,YAAAA,QAAQ,CAACC,SAAT,CAAmB2E,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAO5E,QAAQ,CAAC+E,aAAhB,KAAkC,UAAtC,EAAkD;AAC9C/E,cAAAA,QAAQ,CAAC+E,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYjJ,QAAAA,YAAY,CAACsC,KAAD,EAAoB;AACpC,gBAAMlB,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CADoC,CAGpC;;AACA,eAAKpC,gBAAL,CAAsBC,CAAtB,GAA0BqD,KAAK,CAACE,YAAN,EAA1B;AACA,eAAKxD,gBAAL,CAAsBE,CAAtB,GAA0BoD,KAAK,CAACI,YAAN,EAA1B;AACA,eAAK5D,YAAL,GAAoBsC,GAApB,CANoC,CAQpC;;AACA,eAAK5B,cAAL,GAAsB,KAAtB;AACA,eAAKC,kBAAL,GAA0B2B,GAA1B,CAVoC,CAYpC;AACA;;AAEA,gBAAM8H,OAAO,GAAG,KAAK7G,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAI4G,OAAJ,EAAa;AACT,iBAAKxK,kBAAL,GAA0BwK,OAA1B;AACA,iBAAKL,gBAAL,CAAsBK,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACYhJ,QAAAA,WAAW,CAACoC,KAAD,EAAoB;AACnC,gBAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,gBAAMmB,QAAQ,GAAGD,KAAK,CAACE,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAIxB,WAAW,GAAG,KAAKpC,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,gBAAM4D,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKvD,gBAAL,CAAsBC,CAA1C,CAAf;AACA,gBAAM6D,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKzD,gBAAL,CAAsBE,CAA1C,CAAf;AACA,gBAAM6D,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAIC,QAAQ,GAAG,KAAK5D,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,cAAI,KAAKT,kBAAL,IAA2BqE,QAAQ,GAAG,KAAK5D,qBAAL,GAA6B,CAAvE,EAA0E;AACtE;AACA,iBAAKL,YAAL,GAAoBoC,WAApB;AACA,iBAAKlC,gBAAL,CAAsBC,CAAtB,GAA0BsD,QAA1B;AACA,iBAAKvD,gBAAL,CAAsBE,CAAtB,GAA0BuD,QAA1B;AACA;AACH,WA1BkC,CA4BnC;;;AACA,eAAK3D,YAAL,GAAoBoC,WAApB;AACA,eAAKlC,gBAAL,CAAsBC,CAAtB,GAA0BsD,QAA1B;AACA,eAAKvD,gBAAL,CAAsBE,CAAtB,GAA0BuD,QAA1B,CA/BmC,CAiCnC;;AACA,gBAAMyG,OAAO,GAAG,KAAK7G,oBAAL,CAA0BC,KAA1B,CAAhB,CAlCmC,CAoCnC;;AACA,cAAI,KAAK5D,kBAAL,KAA4BwK,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAKxK,kBAAT,EAA6B;AACzB,mBAAKsK,iBAAL,CAAuB,KAAKtK,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAIwK,OAAJ,EAAa;AACT,mBAAKL,gBAAL,CAAsBK,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAKxK,kBAAL,GAA0BwK,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACY9I,QAAAA,UAAU,CAACkC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAKvC,kBAAT,EAA6B;AACzB,mBAAKsK,iBAAL,CAAuB,KAAKtK,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,gBAAMwK,OAAO,GAAG,KAAK7G,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAK5D,kBAAL,IAA2BwK,OAAO,KAAK,KAAKxK,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAKsK,iBAAL,CAAuB,KAAKtK,kBAA5B;AAEA;AAAA;AAAA,8BAAK2B,GAAL,CAASC,WAAT,CACK,cAAa,+BAAK5B,kBAAL,CAAwB+B,IAAxB,2CAA8BsF,IAA9B,KAAsC,MAAO,EAD/D,EAJgE,CAQhE;;AACA,gBAAI,KAAK1E,wBAAL,CAA8B,KAAK3C,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqBwC,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAK3C,UAAL,IAAmB,KAAKA,UAAL,CAAgB8C,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAK9C,UAAL,CAAgB8C,kBAAhB,CAAmC4H,UAAnC,CAA8C,KAAKzK,kBAAL,CAAwB+B,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,CACK,gBAAe,gCAAK5B,kBAAL,CAAwB+B,IAAxB,4CAA8BsF,IAA9B,KAAsC,MAAO,EADjE,EAFA,CAMA;;AACA,uBAAKgC,mBAAL;AACH,iBARD,CAQE,OAAOqB,KAAP,EAAc;AACZ5H,kBAAAA,OAAO,CAAC4H,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAlBuD,CAoBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKxK,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aAxBD,MAwBO,CACN;AACJ,WAnCD,MAmCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAKsK,iBAAL,CAAuB,KAAKtK,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WAxDiC,CA0DlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AAptBiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流 - 增强版\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测\n\n    // 🎯 性能优化配置\n    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private lastRaycastTime: number = 0;\n    private raycastThrottleDelay: number = 16; // 60FPS节流，约16ms\n    private moveDistanceThreshold: number = 5; // 移动5px以上才重新检测\n    private lastDetectedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 长按优化\n    private isLongPressing: boolean = false;\n    private longPressStartTime: number = 0;\n    private longPressThreshold: number = 500; // 500ms算长按\n    private longPressRaycastDelay: number = 33; // 长按时30FPS检测\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n\n        // 🧹 清理射线检测缓存\n        this.raycastCache.clear();\n        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🚀 整合优化的射线检测 - 高性能精确检测\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n        const now = Date.now();\n\n        // 🎯 计算移动距离\n        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);\n        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        // 🎯 性能优化：移动距离小且时间间隔短，返回上次结果\n        if (\n            distance < this.moveDistanceThreshold &&\n            now - this.lastRaycastTime < this.raycastThrottleDelay &&\n            this.lastDetectedItem\n        ) {\n            return this.lastDetectedItem;\n        }\n\n        // 🎯 长按检测优化\n        const raycastDelay = this.isLongPressing\n            ? this.longPressRaycastDelay\n            : this.raycastThrottleDelay;\n        if (now - this.lastRaycastTime < raycastDelay) {\n            return this.lastDetectedItem;\n        }\n\n        // 🎯 执行射线检测\n        const detectedItem = this.performOptimizedRaycast(currentX, currentY);\n\n        // 🎯 更新缓存\n        this.lastRaycastPosition.x = currentX;\n        this.lastRaycastPosition.y = currentY;\n        this.lastRaycastTime = now;\n        this.lastDetectedItem = detectedItem;\n\n        return detectedItem;\n    }\n\n    /**\n     * 🎯 优化的射线检测 - 整合所有检测逻辑\n     */\n    private performOptimizedRaycast(x: number, y: number): ItemSceneViewComp | null {\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        // 🎯 执行精确射线检测 - 只检测最前面的可见物品\n        if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n            const result = PhysicsSystem.instance.raycastClosestResult;\n            const hitNode = result.collider.node;\n            const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n            if (itemComp && itemComp.ItemModel) {\n                return itemComp;\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * 🎯 简化多层射线检测 - 只使用单点射线，逐层检测\n     */\n    private performSimpleMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 改进的层级切换 - 需要持续慢速移动才触发\n     */\n    private handleSimpleLayerSwitching(\n        items: ItemSceneViewComp[],\n        moveDistance: number\n    ): ItemSceneViewComp | null {\n        if (items.length <= 1) {\n            return items[0] || null;\n        }\n\n        const now = Date.now();\n        const isSlowMove = moveDistance < this.slowMoveThreshold;\n\n        // 🎯 如果不是慢速移动，直接返回顶层物品\n        if (!isSlowMove) {\n            this.exitSlowMoveMode();\n            return items[0];\n        }\n\n        // 🎯 开始记录慢速移动时间\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.currentItemIndex = 0;\n            this.candidateItems = items.map(item => ({ item, score: 100 }));\n            // 不立即开始切换，先返回顶层物品\n            return items[0];\n        }\n\n        // 🎯 检查是否持续慢速移动足够长时间\n        const slowMoveDuration = now - this.slowMoveStartTime;\n        if (slowMoveDuration < this.slowMoveRequiredTime) {\n            // 还没有持续足够长时间，继续返回顶层物品\n            return items[0];\n        }\n\n        // 🎯 现在开始层级切换\n        if (slowMoveDuration >= this.slowMoveRequiredTime) {\n            // 第一次进入切换模式\n            if (this.lastItemSwitchTime === 0) {\n                this.lastItemSwitchTime = now;\n                console.log(\n                    `🎯 持续慢速移动${this.slowMoveRequiredTime}ms，开始层级切换 (共${items.length}层)`\n                );\n            }\n\n            // 检查是否应该切换到下一层\n            if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n                this.currentItemIndex = (this.currentItemIndex + 1) % items.length;\n                this.lastItemSwitchTime = now;\n\n                console.log(\n                    `🔄 切换到第${this.currentItemIndex + 1}层: ${items[this.currentItemIndex].node?.name || '未知'}`\n                );\n            }\n        }\n\n        // 🎯 检查是否超时\n        if (slowMoveDuration > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return items[0]; // 返回顶层物品\n        }\n\n        return items[this.currentItemIndex] || items[0];\n    }\n\n    /**\n     * 🎯 合并和评分物品\n     */\n    private mergeAndScoreItems(\n        raycastItems: ItemSceneViewComp[],\n        screenItems: ItemSceneViewComp[],\n        touchX: number,\n        touchY: number\n    ): Array<{ item: ItemSceneViewComp; score: number }> {\n        const candidates = new Map<string, { item: ItemSceneViewComp; score: number }>();\n        const camera = smc.camera.CameraModel.camera;\n\n        // 处理射线检测结果（高优先级）\n        for (const item of raycastItems) {\n            const worldPos = item.node.worldPosition;\n            const screenPos = camera.worldToScreen(worldPos);\n            const distance = Math.sqrt(\n                Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n            );\n\n            candidates.set(item.node.uuid, {\n                item,\n                score: 100 - distance * 0.5, // 射线检测基础分100，距离越近分数越高\n            });\n        }\n\n        // 处理屏幕距离检测结果（低优先级）\n        for (const item of screenItems) {\n            if (!candidates.has(item.node.uuid)) {\n                const worldPos = item.node.worldPosition;\n                const screenPos = camera.worldToScreen(worldPos);\n                const distance = Math.sqrt(\n                    Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n                );\n\n                candidates.set(item.node.uuid, {\n                    item,\n                    score: 50 - distance * 0.3, // 屏幕距离基础分50\n                });\n            }\n        }\n\n        return Array.from(candidates.values());\n    }\n\n    /**\n     * 🎯 选择最佳物品\n     */\n    private selectBestItem(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        if (candidates.length === 0) {\n            return null;\n        }\n\n        // 按分数排序，选择最高分的物品\n        candidates.sort((a, b) => b.score - a.score);\n\n        const bestCandidate = candidates[0];\n\n        // 如果有多个候选物品，可以在这里实现慢速移动时的切换逻辑\n        if (candidates.length > 1) {\n            return this.handleMultipleCandidates(candidates);\n        }\n\n        return bestCandidate.item;\n    }\n\n    /**\n     * 🎯 处理多个候选物品\n     */\n    private handleMultipleCandidates(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        const now = Date.now();\n\n        // 检查是否为慢速移动模式\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.candidateItems = candidates;\n            this.currentItemIndex = 0;\n            console.log(`🎯 检测到${candidates.length}个候选物品，进入慢速选择模式`);\n        }\n\n        // 检查是否超时\n        if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return candidates[0].item; // 返回最高分物品\n        }\n\n        // 检查是否应该切换\n        if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n            this.currentItemIndex = (this.currentItemIndex + 1) % candidates.length;\n            this.lastItemSwitchTime = now;\n\n            const currentItem = candidates[this.currentItemIndex];\n            console.log(\n                `🔄 切换到候选物品: ${currentItem.item.node?.name || '未知'} (${this.currentItemIndex + 1}/${candidates.length})`\n            );\n        }\n\n        return candidates[this.currentItemIndex]?.item || candidates[0].item;\n    }\n\n    /**\n     * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品\n     */\n    private performMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const startTime = performance.now();\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层，平衡性能和功能\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        // 🎯 记录性能数据\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n\n        if (duration > 3) {\n            // 多层检测阈值稍高\n            console.warn(\n                `⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 退出慢速移动模式\n     */\n    private exitSlowMoveMode(): void {\n        if (this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = false;\n            this.currentItemIndex = 0;\n            this.candidateItems = [];\n            this.lastItemSwitchTime = 0; // 重置切换时间\n            console.log('🏁 退出慢速移动模式，回到顶层');\n        }\n    }\n\n    /**\n     * 🎯 暂停物品切换 - 选择物品后暂停一段时间\n     */\n    private pauseLayerSwitching(): void {\n        this.exitSlowMoveMode();\n        this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒\n        console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);\n    }\n\n    /**\n     * 🎯 清理过期缓存\n     */\n    private cleanupCache(): void {\n        if (this.raycastCache.size > 50) {\n            const now = Date.now();\n            const keysToDelete: string[] = [];\n\n            this.raycastCache.forEach((value, key) => {\n                if (now - value.timestamp > this.raycastCacheTimeout * 2) {\n                    keysToDelete.push(key);\n                }\n            });\n\n            keysToDelete.forEach(key => this.raycastCache.delete(key));\n        }\n    }\n\n    /**\n     * 🎯 性能报告（如果需要）\n     */\n    private reportPerformanceIfNeeded(): void {\n        const now = Date.now();\n        if (now - this.lastPerformanceReport > 10000) {\n            // 每10秒报告一次\n            const cacheHitRate =\n                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;\n\n            if (this.raycastCount > 0) {\n                console.log(\n                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`\n                );\n\n                if (cacheHitRate < 30) {\n                    console.warn(\n                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`\n                    );\n                }\n            }\n\n            this.lastPerformanceReport = now;\n        }\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件 - 添加长按检测\n     */\n    private onTouchStart(event: EventTouch) {\n        const now = Date.now();\n\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = now;\n\n        // 🎯 初始化长按检测\n        this.isLongPressing = false;\n        this.longPressStartTime = now;\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到30FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率\n        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {\n            // 更新位置但不执行射线检测，减少性能消耗\n            this.lastMoveTime = currentTime;\n            this.lastMovePosition.x = currentX;\n            this.lastMovePosition.y = currentY;\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n\n                        // 🎯 选择物品后暂停层级切换\n                        this.pauseLayerSwitching();\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}