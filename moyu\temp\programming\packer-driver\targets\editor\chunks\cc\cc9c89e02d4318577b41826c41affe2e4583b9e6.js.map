{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "PhysicsSystem", "oops", "PHY_GROUP", "smc", "ItemSceneViewComp", "GameSceneViewComp", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "camera", "CameraModel", "ray", "screenPointToRay", "getLocationX", "getLocationY", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "instance", "raycastClosest", "raycastResult", "raycastClosestResult", "hitNode", "collider", "itemSceneViewComp", "ItemModel", "applyTouchEffect", "itemComp", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "hitItem", "currentX", "currentY", "deltaX", "Math", "abs", "deltaY", "distance", "sqrt", "name", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,a,OAAAA,a;;AACjDC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;wCAEaY,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CX,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CY,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,CAbU;AAAA;;AAaP;AAE3CC,QAAAA,MAAM,GAAG;AACLtB,UAAAA,KAAK,CAACuB,EAAN,CAAStB,KAAK,CAACuB,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACA1B,UAAAA,KAAK,CAACuB,EAAN,CAAStB,KAAK,CAACuB,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACA5B,UAAAA,KAAK,CAACuB,EAAN,CAAStB,KAAK,CAACuB,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAK1B,UAAL,GAAkBuB,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,CACK,0CAAyC,KAAKrB,UAAL,GAAkB,IAAlB,GAAyB,IAAK,EAD5E;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAKoB,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACRvC,UAAAA,KAAK,CAACwC,GAAN,CAAUvC,KAAK,CAACuB,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACA1B,UAAAA,KAAK,CAACwC,GAAN,CAAUvC,KAAK,CAACuB,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACA5B,UAAAA,KAAK,CAACwC,GAAN,CAAUvC,KAAK,CAACuB,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,eAAe,GAAY;AAC/B,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAK7B,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACY8B,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAKnC,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgBoC,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,gBAAMC,QAAQ,GAAGJ,IAAI,CAACX,IAAtB;AACA,gBAAMY,kBAAkB,GAAG,KAAKpC,UAAL,CAAgBoC,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,kBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,gBAAMC,YAAY,GAAG,KAAK5C,UAAL,CAAgB6C,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAKnD,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH;;AAED,gBAAMoD,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,gBAAME,GAAG,GAAGF,MAAM,CAACG,gBAAP,CAAwBJ,KAAK,CAACK,YAAN,EAAxB,EAA8CL,KAAK,CAACM,YAAN,EAA9C,CAAZ;AACA,gBAAMC,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;;AAEA,cAAItE,aAAa,CAACuE,QAAd,CAAuBC,cAAvB,CAAsCT,GAAtC,EAA2CI,WAA3C,CAAJ,EAA6D;AACzD,kBAAMM,aAAa,GAAGzE,aAAa,CAACuE,QAAd,CAAuBG,oBAA7C;AACA,kBAAMC,OAAO,GAAGF,aAAa,CAACG,QAAd,CAAuB3C,IAAvC;AACA,kBAAM4C,iBAAiB,GAAGF,OAAO,CAACzC,YAAR;AAAA;AAAA,uDAA1B;;AAEA,gBAAI2C,iBAAiB,IAAIA,iBAAiB,CAACC,SAA3C,EAAsD;AAClD,qBAAOD,iBAAP;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYE,QAAAA,gBAAgB,CAACC,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACF,SAArB,IAAkC,CAACE,QAAQ,CAACF,SAAT,CAAmBG,QAA1D,EAAoE;AAChED,YAAAA,QAAQ,CAACF,SAAT,CAAmBG,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAOD,QAAQ,CAACE,OAAhB,KAA4B,UAAhC,EAA4C;AACxCF,cAAAA,QAAQ,CAACE,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAACH,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACF,SAArB,IAAkCE,QAAQ,CAACF,SAAT,CAAmBG,QAAzD,EAAmE;AAC/DD,YAAAA,QAAQ,CAACF,SAAT,CAAmBG,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAOD,QAAQ,CAACI,aAAhB,KAAkC,UAAtC,EAAkD;AAC9CJ,cAAAA,QAAQ,CAACI,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACY5D,QAAAA,YAAY,CAACoC,KAAD,EAAoB;AACpC;AACA,eAAK5C,gBAAL,CAAsBC,CAAtB,GAA0B2C,KAAK,CAACK,YAAN,EAA1B;AACA,eAAKjD,gBAAL,CAAsBE,CAAtB,GAA0B0C,KAAK,CAACM,YAAN,EAA1B;AACA,eAAKpD,YAAL,GAAoB2B,IAAI,CAACC,GAAL,EAApB,CAJoC,CAMpC;AACA;;AAEA,gBAAM2C,OAAO,GAAG,KAAK1B,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAIyB,OAAJ,EAAa;AACT,iBAAK3E,kBAAL,GAA0B2E,OAA1B;AACA,iBAAKN,gBAAL,CAAsBM,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACY3D,QAAAA,WAAW,CAACkC,KAAD,EAAoB;AACnC,gBAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,gBAAM4C,QAAQ,GAAG1B,KAAK,CAACK,YAAN,EAAjB;AACA,gBAAMsB,QAAQ,GAAG3B,KAAK,CAACM,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAI1B,WAAW,GAAG,KAAK1B,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,gBAAMyE,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKtE,gBAAL,CAAsBC,CAA1C,CAAf;AACA,gBAAM0E,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASH,QAAQ,GAAG,KAAKvE,gBAAL,CAAsBE,CAA1C,CAAf;AACA,gBAAM0E,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAIC,QAAQ,GAAG,KAAKzE,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,eAAKL,YAAL,GAAoB0B,WAApB;AACA,eAAKxB,gBAAL,CAAsBC,CAAtB,GAA0BqE,QAA1B;AACA,eAAKtE,gBAAL,CAAsBE,CAAtB,GAA0BqE,QAA1B,CAtBmC,CAwBnC;;AACA,gBAAMF,OAAO,GAAG,KAAK1B,oBAAL,CAA0BC,KAA1B,CAAhB,CAzBmC,CA2BnC;;AACA,cAAI,KAAKlD,kBAAL,KAA4B2E,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAK3E,kBAAT,EAA6B;AACzB,mBAAKyE,iBAAL,CAAuB,KAAKzE,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAI2E,OAAJ,EAAa;AACT,mBAAKN,gBAAL,CAAsBM,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAK3E,kBAAL,GAA0B2E,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACYzD,QAAAA,UAAU,CAACgC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAK7B,kBAAT,EAA6B;AACzB,mBAAKyE,iBAAL,CAAuB,KAAKzE,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,gBAAM2E,OAAO,GAAG,KAAK1B,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAKlD,kBAAL,IAA2B2E,OAAO,KAAK,KAAK3E,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAKyE,iBAAL,CAAuB,KAAKzE,kBAA5B;AAEA;AAAA;AAAA,8BAAKmB,GAAL,CAASC,WAAT,CACK,cAAa,+BAAKpB,kBAAL,CAAwBuB,IAAxB,2CAA8B6D,IAA9B,KAAsC,MAAO,EAD/D,EAJgE,CAQhE;;AACA,gBAAI,KAAKnD,wBAAL,CAA8B,KAAKjC,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqB8B,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAKjC,UAAL,IAAmB,KAAKA,UAAL,CAAgBoC,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAKpC,UAAL,CAAgBoC,kBAAhB,CAAmCkD,UAAnC,CAA8C,KAAKrF,kBAAL,CAAwBuB,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,CACK,gBAAe,gCAAKpB,kBAAL,CAAwBuB,IAAxB,4CAA8B6D,IAA9B,KAAsC,MAAO,EADjE;AAGH,iBALD,CAKE,OAAOE,KAAP,EAAc;AACZlD,kBAAAA,OAAO,CAACkD,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAfuD,CAiBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKpF,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aArBD,MAqBO,CACN;AACJ,WAhCD,MAgCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAKyE,iBAAL,CAAuB,KAAKzE,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WArDiC,CAuDlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AAtSiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 16; // 60FPS限制，约16ms\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 5; // 像素阈值，小于此距离不触发检测\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 使用射线检测当前触摸位置的物品\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const ray = camera.screenPointToRay(event.getLocationX(), event.getLocationY());\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n            const raycastResult = PhysicsSystem.instance.raycastClosestResult;\n            const hitNode = raycastResult.collider.node;\n            const itemSceneViewComp = hitNode.getComponent(ItemSceneViewComp);\n\n            if (itemSceneViewComp && itemSceneViewComp.ItemModel) {\n                return itemSceneViewComp;\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件\n     */\n    private onTouchStart(event: EventTouch) {\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = Date.now();\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 优化版触摸移动事件处理 - 添加节流和距离阈值\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到60FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}