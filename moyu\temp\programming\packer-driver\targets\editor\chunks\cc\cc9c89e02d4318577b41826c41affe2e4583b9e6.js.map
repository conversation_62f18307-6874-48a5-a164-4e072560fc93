{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "oops", "ItemSceneViewComp", "GameSceneViewComp", "SimpleItemDetector", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "raycastCache", "Map", "raycastCacheTimeout", "lastRaycastPosition", "raycastPositionThreshold", "raycastCount", "cacheHitCount", "lastPerformanceReport", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "clear", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "detectItem", "performPreciseRaycast", "camera", "smc", "CameraModel", "ray", "screenPointToRay", "raycastMask", "PHY_GROUP", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "PhysicsSystem", "instance", "raycastClosest", "result", "raycastClosestResult", "hitNode", "collider", "itemComp", "ItemModel", "performSimpleMultiLayerRaycast", "allItems", "maxLayers", "disabledColliders", "layer", "find", "push", "enabled", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "handleSimpleLayerSwitching", "items", "moveDistance", "length", "isSlowMove", "slowMoveThreshold", "exitSlowMoveMode", "isInSlowMoveMode", "slowMoveStartTime", "currentItemIndex", "candidate<PERSON><PERSON>s", "map", "score", "slowMoveDuration", "slowMoveRequiredTime", "lastItemSwitchTime", "itemSwitchDelay", "name", "maxSlowMoveTime", "mergeAndScoreItems", "raycastItems", "screenItems", "touchX", "touchY", "candidates", "worldPos", "worldPosition", "screenPos", "worldToScreen", "distance", "Math", "sqrt", "pow", "set", "uuid", "Array", "from", "values", "selectBestItem", "sort", "a", "b", "bestCandidate", "handleMultipleCandidates", "currentItem", "performMultiLayerRaycast", "startTime", "performance", "endTime", "duration", "toFixed", "pauseLayerSwitching", "cleanupCache", "size", "keysToDelete", "value", "key", "timestamp", "delete", "reportPerformanceIfNeeded", "cacheHitRate", "applyTouchEffect", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "getLocationX", "getLocationY", "hitItem", "currentX", "currentY", "deltaX", "abs", "deltaY", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AAC1CC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;AAE9B;AACA;AACA;AACA;;wCAEaU,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CT,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CU,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,EAbU;AAaN;AAE5C;AAfkD,eAgB1CC,YAhB0C,GAiB9C,IAAIC,GAAJ,EAjB8C;AAAA,eAkB1CC,mBAlB0C,GAkBZ,GAlBY;AAkBP;AAlBO,eAmB1CC,mBAnB0C,GAmBM;AAAEN,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAnBN;AAAA,eAoB1CM,wBApB0C,GAoBP,CApBO;AAoBJ;AAE9C;AAtBkD,eAuB1CL,qBAvB0C,GAuBV,EAvBU;AAuBN;AAE5C;AAzBkD,eA0B1CM,YA1B0C,GA0BnB,CA1BmB;AAAA,eA2B1CC,aA3B0C,GA2BlB,CA3BkB;AAAA,eA4B1CC,qBA5B0C,GA4BV,CA5BU;AAAA;;AA8BlDC,QAAAA,MAAM,GAAG;AACL5B,UAAAA,KAAK,CAAC6B,EAAN,CAAS5B,KAAK,CAAC6B,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACAhC,UAAAA,KAAK,CAAC6B,EAAN,CAAS5B,KAAK,CAAC6B,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACAlC,UAAAA,KAAK,CAAC6B,EAAN,CAAS5B,KAAK,CAAC6B,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAKlC,UAAL,GAAkB+B,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,CACK,0CAAyC,KAAK7B,UAAL,GAAkB,IAAlB,GAAyB,IAAK,EAD5E;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAK4B,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR7C,UAAAA,KAAK,CAAC8C,GAAN,CAAU7C,KAAK,CAAC6B,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACAhC,UAAAA,KAAK,CAAC8C,GAAN,CAAU7C,KAAK,CAAC6B,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACAlC,UAAAA,KAAK,CAAC8C,GAAN,CAAU7C,KAAK,CAAC6B,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD,EAHQ,CAKR;;AACA,eAAKhB,YAAL,CAAkB2B,KAAlB;AACA;AAAA;AAAA,4BAAKV,GAAL,CAASC,WAAT,CAAqB,uCAArB;AACH;AAED;AACJ;AACA;;;AACYU,QAAAA,eAAe,GAAY;AAC/B,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAKtC,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYuC,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAK5C,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgB6C,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,gBAAMC,QAAQ,GAAGJ,IAAI,CAACZ,IAAtB;AACA,gBAAMa,kBAAkB,GAAG,KAAK7C,UAAL,CAAgB6C,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,kBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,gBAAMC,YAAY,GAAG,KAAKrD,UAAL,CAAgBsD,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAK5D,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH,WAHqE,CAKtE;;;AACA,iBAAO;AAAA;AAAA,wDAAmB6D,UAAnB,CAA8BD,KAA9B,EAAqC,SAArC,CAAP;AACH;AAED;AACJ;AACA;;;AACYE,QAAAA,qBAAqB,CAACtD,CAAD,EAAYC,CAAZ,EAAiD;AAC1E,gBAAMsD,MAAM,GAAGC,GAAG,CAACD,MAAJ,CAAWE,WAAX,CAAuBF,MAAtC;AACA,gBAAMG,GAAG,GAAGH,MAAM,CAACI,gBAAP,CAAwB3D,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAM2D,WAAW,GAAGC,SAAS,CAACC,IAAV,GAAiBD,SAAS,CAACE,QAA3B,GAAsCF,SAAS,CAACG,cAApE,CAH0E,CAK1E;;AACA,cAAIC,aAAa,CAACC,QAAd,CAAuBC,cAAvB,CAAsCT,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,kBAAMQ,MAAM,GAAGH,aAAa,CAACC,QAAd,CAAuBG,oBAAtC;AACA,kBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB/C,IAAhC;AACA,kBAAMgD,QAAQ,GAAGF,OAAO,CAAC7C,YAAR;AAAA;AAAA,uDAAjB;;AAEA,gBAAI+C,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC,qBAAOD,QAAP;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYE,QAAAA,8BAA8B,CAAC1E,CAAD,EAAYC,CAAZ,EAA4C;AAC9E,gBAAMsD,MAAM,GAAGC,GAAG,CAACD,MAAJ,CAAWE,WAAX,CAAuBF,MAAtC;AACA,gBAAMG,GAAG,GAAGH,MAAM,CAACI,gBAAP,CAAwB3D,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAM2D,WAAW,GAAGC,SAAS,CAACC,IAAV,GAAiBD,SAAS,CAACE,QAA3B,GAAsCF,SAAS,CAACG,cAApE;AAEA,gBAAMW,QAA6B,GAAG,EAAtC;AACA,gBAAMC,SAAS,GAAG,CAAlB,CAN8E,CAMzD;;AACrB,gBAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AACA;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,SAA5B,EAAuCE,KAAK,EAA5C,EAAgD;AAC5C,kBAAIb,aAAa,CAACC,QAAd,CAAuBC,cAAvB,CAAsCT,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,sBAAMQ,MAAM,GAAGH,aAAa,CAACC,QAAd,CAAuBG,oBAAtC;AACA,sBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB/C,IAAhC;AACA,sBAAMgD,QAAQ,GAAGF,OAAO,CAAC7C,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAI+C,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACE,QAAQ,CAACI,IAAT,CAAc3C,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAc8C,OAApC,CAAL,EAAmD;AAC/CK,oBAAAA,QAAQ,CAACK,IAAT,CAAcR,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,wBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACU,OAAb,EAAsB;AAClBV,oBAAAA,QAAQ,CAACU,OAAT,GAAmB,KAAnB;AACAJ,oBAAAA,iBAAiB,CAACG,IAAlB,CAAuBT,QAAvB;AACH;AACJ,iBAZD,MAYO;AACH,wBADG,CACI;AACV;AACJ,eApBD,MAoBO;AACH,sBADG,CACI;AACV;AACJ;AACJ,WA3BD,SA2BU;AACN;AACAM,YAAAA,iBAAiB,CAACK,OAAlB,CAA0BX,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACY,OAAzB,EAAkC;AAC9BZ,gBAAAA,QAAQ,CAACU,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH;;AAED,iBAAON,QAAP;AACH;AAED;AACJ;AACA;;;AACYS,QAAAA,0BAA0B,CAC9BC,KAD8B,EAE9BC,YAF8B,EAGN;AACxB,cAAID,KAAK,CAACE,MAAN,IAAgB,CAApB,EAAuB;AACnB,mBAAOF,KAAK,CAAC,CAAD,CAAL,IAAY,IAAnB;AACH;;AAED,gBAAMnD,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,gBAAMsD,UAAU,GAAGF,YAAY,GAAG,KAAKG,iBAAvC,CANwB,CAQxB;;AACA,cAAI,CAACD,UAAL,EAAiB;AACb,iBAAKE,gBAAL;AACA,mBAAOL,KAAK,CAAC,CAAD,CAAZ;AACH,WAZuB,CAcxB;;;AACA,cAAI,CAAC,KAAKM,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyB1D,GAAzB;AACA,iBAAK2D,gBAAL,GAAwB,CAAxB;AACA,iBAAKC,cAAL,GAAsBT,KAAK,CAACU,GAAN,CAAU3D,IAAI,KAAK;AAAEA,cAAAA,IAAF;AAAQ4D,cAAAA,KAAK,EAAE;AAAf,aAAL,CAAd,CAAtB,CAJwB,CAKxB;;AACA,mBAAOX,KAAK,CAAC,CAAD,CAAZ;AACH,WAtBuB,CAwBxB;;;AACA,gBAAMY,gBAAgB,GAAG/D,GAAG,GAAG,KAAK0D,iBAApC;;AACA,cAAIK,gBAAgB,GAAG,KAAKC,oBAA5B,EAAkD;AAC9C;AACA,mBAAOb,KAAK,CAAC,CAAD,CAAZ;AACH,WA7BuB,CA+BxB;;;AACA,cAAIY,gBAAgB,IAAI,KAAKC,oBAA7B,EAAmD;AAC/C;AACA,gBAAI,KAAKC,kBAAL,KAA4B,CAAhC,EAAmC;AAC/B,mBAAKA,kBAAL,GAA0BjE,GAA1B;AACAI,cAAAA,OAAO,CAAClB,GAAR,CACK,YAAW,KAAK8E,oBAAqB,eAAcb,KAAK,CAACE,MAAO,IADrE;AAGH,aAP8C,CAS/C;;;AACA,gBAAIrD,GAAG,GAAG,KAAKiE,kBAAX,GAAgC,KAAKC,eAAzC,EAA0D;AAAA;;AACtD,mBAAKP,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8BR,KAAK,CAACE,MAA5D;AACA,mBAAKY,kBAAL,GAA0BjE,GAA1B;AAEAI,cAAAA,OAAO,CAAClB,GAAR,CACK,UAAS,KAAKyE,gBAAL,GAAwB,CAAE,MAAK,0BAAAR,KAAK,CAAC,KAAKQ,gBAAN,CAAL,CAA6BrE,IAA7B,2CAAmC6E,IAAnC,KAA2C,IAAK,EAD7F;AAGH;AACJ,WAlDuB,CAoDxB;;;AACA,cAAIJ,gBAAgB,GAAG,KAAKK,eAA5B,EAA6C;AACzC,iBAAKZ,gBAAL;AACA,mBAAOL,KAAK,CAAC,CAAD,CAAZ,CAFyC,CAExB;AACpB;;AAED,iBAAOA,KAAK,CAAC,KAAKQ,gBAAN,CAAL,IAAgCR,KAAK,CAAC,CAAD,CAA5C;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,kBAAkB,CACtBC,YADsB,EAEtBC,WAFsB,EAGtBC,MAHsB,EAItBC,MAJsB,EAK2B;AACjD,gBAAMC,UAAU,GAAG,IAAIxG,GAAJ,EAAnB;AACA,gBAAMmD,MAAM,GAAGC,GAAG,CAACD,MAAJ,CAAWE,WAAX,CAAuBF,MAAtC,CAFiD,CAIjD;;AACA,eAAK,MAAMnB,IAAX,IAAmBoE,YAAnB,EAAiC;AAC7B,kBAAMK,QAAQ,GAAGzE,IAAI,CAACZ,IAAL,CAAUsF,aAA3B;AACA,kBAAMC,SAAS,GAAGxD,MAAM,CAACyD,aAAP,CAAqBH,QAArB,CAAlB;AACA,kBAAMI,QAAQ,GAAGC,IAAI,CAACC,IAAL,CACbD,IAAI,CAACE,GAAL,CAASL,SAAS,CAAC/G,CAAV,GAAc0G,MAAvB,EAA+B,CAA/B,IAAoCQ,IAAI,CAACE,GAAL,CAASL,SAAS,CAAC9G,CAAV,GAAc0G,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;AAIAC,YAAAA,UAAU,CAACS,GAAX,CAAejF,IAAI,CAACZ,IAAL,CAAU8F,IAAzB,EAA+B;AAC3BlF,cAAAA,IAD2B;AAE3B4D,cAAAA,KAAK,EAAE,MAAMiB,QAAQ,GAAG,GAFG,CAEE;;AAFF,aAA/B;AAIH,WAhBgD,CAkBjD;;;AACA,eAAK,MAAM7E,IAAX,IAAmBqE,WAAnB,EAAgC;AAC5B,gBAAI,CAACG,UAAU,CAAC5D,GAAX,CAAeZ,IAAI,CAACZ,IAAL,CAAU8F,IAAzB,CAAL,EAAqC;AACjC,oBAAMT,QAAQ,GAAGzE,IAAI,CAACZ,IAAL,CAAUsF,aAA3B;AACA,oBAAMC,SAAS,GAAGxD,MAAM,CAACyD,aAAP,CAAqBH,QAArB,CAAlB;AACA,oBAAMI,QAAQ,GAAGC,IAAI,CAACC,IAAL,CACbD,IAAI,CAACE,GAAL,CAASL,SAAS,CAAC/G,CAAV,GAAc0G,MAAvB,EAA+B,CAA/B,IAAoCQ,IAAI,CAACE,GAAL,CAASL,SAAS,CAAC9G,CAAV,GAAc0G,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;AAIAC,cAAAA,UAAU,CAACS,GAAX,CAAejF,IAAI,CAACZ,IAAL,CAAU8F,IAAzB,EAA+B;AAC3BlF,gBAAAA,IAD2B;AAE3B4D,gBAAAA,KAAK,EAAE,KAAKiB,QAAQ,GAAG,GAFI,CAEC;;AAFD,eAA/B;AAIH;AACJ;;AAED,iBAAOM,KAAK,CAACC,IAAN,CAAWZ,UAAU,CAACa,MAAX,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,cAAc,CAClBd,UADkB,EAEM;AACxB,cAAIA,UAAU,CAACrB,MAAX,KAAsB,CAA1B,EAA6B;AACzB,mBAAO,IAAP;AACH,WAHuB,CAKxB;;;AACAqB,UAAAA,UAAU,CAACe,IAAX,CAAgB,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAAC7B,KAAF,GAAU4B,CAAC,CAAC5B,KAAtC;AAEA,gBAAM8B,aAAa,GAAGlB,UAAU,CAAC,CAAD,CAAhC,CARwB,CAUxB;;AACA,cAAIA,UAAU,CAACrB,MAAX,GAAoB,CAAxB,EAA2B;AACvB,mBAAO,KAAKwC,wBAAL,CAA8BnB,UAA9B,CAAP;AACH;;AAED,iBAAOkB,aAAa,CAAC1F,IAArB;AACH;AAED;AACJ;AACA;;;AACY2F,QAAAA,wBAAwB,CAC5BnB,UAD4B,EAEJ;AAAA;;AACxB,gBAAM1E,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CADwB,CAGxB;;AACA,cAAI,CAAC,KAAKyD,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyB1D,GAAzB;AACA,iBAAK4D,cAAL,GAAsBc,UAAtB;AACA,iBAAKf,gBAAL,GAAwB,CAAxB;AACAvD,YAAAA,OAAO,CAAClB,GAAR,CAAa,SAAQwF,UAAU,CAACrB,MAAO,gBAAvC;AACH,WAVuB,CAYxB;;;AACA,cAAIrD,GAAG,GAAG,KAAK0D,iBAAX,GAA+B,KAAKU,eAAxC,EAAyD;AACrD,iBAAKZ,gBAAL;AACA,mBAAOkB,UAAU,CAAC,CAAD,CAAV,CAAcxE,IAArB,CAFqD,CAE1B;AAC9B,WAhBuB,CAkBxB;;;AACA,cAAIF,GAAG,GAAG,KAAKiE,kBAAX,GAAgC,KAAKC,eAAzC,EAA0D;AAAA;;AACtD,iBAAKP,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8Be,UAAU,CAACrB,MAAjE;AACA,iBAAKY,kBAAL,GAA0BjE,GAA1B;AAEA,kBAAM8F,WAAW,GAAGpB,UAAU,CAAC,KAAKf,gBAAN,CAA9B;AACAvD,YAAAA,OAAO,CAAClB,GAAR,CACK,eAAc,0BAAA4G,WAAW,CAAC5F,IAAZ,CAAiBZ,IAAjB,2CAAuB6E,IAAvB,KAA+B,IAAK,KAAI,KAAKR,gBAAL,GAAwB,CAAE,IAAGe,UAAU,CAACrB,MAAO,GAD1G;AAGH;;AAED,iBAAO,0BAAAqB,UAAU,CAAC,KAAKf,gBAAN,CAAV,2CAAmCzD,IAAnC,KAA2CwE,UAAU,CAAC,CAAD,CAAV,CAAcxE,IAAhE;AACH;AAED;AACJ;AACA;;;AACY6F,QAAAA,wBAAwB,CAACjI,CAAD,EAAYC,CAAZ,EAA4C;AACxE,gBAAMiI,SAAS,GAAGC,WAAW,CAACjG,GAAZ,EAAlB;AACA,gBAAMqB,MAAM,GAAGC,GAAG,CAACD,MAAJ,CAAWE,WAAX,CAAuBF,MAAtC;AACA,gBAAMG,GAAG,GAAGH,MAAM,CAACI,gBAAP,CAAwB3D,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAM2D,WAAW,GAAGC,SAAS,CAACC,IAAV,GAAiBD,SAAS,CAACE,QAA3B,GAAsCF,SAAS,CAACG,cAApE;AAEA,gBAAMW,QAA6B,GAAG,EAAtC;AACA,gBAAMC,SAAS,GAAG,CAAlB,CAPwE,CAOnD;;AACrB,gBAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AACA;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,SAA5B,EAAuCE,KAAK,EAA5C,EAAgD;AAC5C,kBAAIb,aAAa,CAACC,QAAd,CAAuBC,cAAvB,CAAsCT,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,sBAAMQ,MAAM,GAAGH,aAAa,CAACC,QAAd,CAAuBG,oBAAtC;AACA,sBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB/C,IAAhC;AACA,sBAAMgD,QAAQ,GAAGF,OAAO,CAAC7C,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAI+C,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACE,QAAQ,CAACI,IAAT,CAAc3C,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAc8C,OAApC,CAAL,EAAmD;AAC/CK,oBAAAA,QAAQ,CAACK,IAAT,CAAcR,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,wBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACU,OAAb,EAAsB;AAClBV,oBAAAA,QAAQ,CAACU,OAAT,GAAmB,KAAnB;AACAJ,oBAAAA,iBAAiB,CAACG,IAAlB,CAAuBT,QAAvB;AACH;AACJ,iBAZD,MAYO;AACH,wBADG,CACI;AACV;AACJ,eApBD,MAoBO;AACH,sBADG,CACI;AACV;AACJ;AACJ,WA3BD,SA2BU;AACN;AACAM,YAAAA,iBAAiB,CAACK,OAAlB,CAA0BX,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACY,OAAzB,EAAkC;AAC9BZ,gBAAAA,QAAQ,CAACU,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH,WA5CuE,CA8CxE;;;AACA,gBAAMmD,OAAO,GAAGD,WAAW,CAACjG,GAAZ,EAAhB;AACA,gBAAMmG,QAAQ,GAAGD,OAAO,GAAGF,SAA3B;;AAEA,cAAIG,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA/F,YAAAA,OAAO,CAACC,IAAR,CACK,gBAAe8F,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CAAoB,SAAQ3D,QAAQ,CAACY,MAAO,KADhE;AAGH;;AAED,iBAAOZ,QAAP;AACH;AAED;AACJ;AACA;;;AACYe,QAAAA,gBAAgB,GAAS;AAC7B,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,GAAwB,KAAxB;AACA,iBAAKE,gBAAL,GAAwB,CAAxB;AACA,iBAAKC,cAAL,GAAsB,EAAtB;AACA,iBAAKK,kBAAL,GAA0B,CAA1B,CAJuB,CAIM;;AAC7B7D,YAAAA,OAAO,CAAClB,GAAR,CAAY,kBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYmH,QAAAA,mBAAmB,GAAS;AAChC,eAAK7C,gBAAL;AACA,eAAKS,kBAAL,GAA0BlE,IAAI,CAACC,GAAL,KAAa,IAAvC,CAFgC,CAEa;;AAC7CI,UAAAA,OAAO,CAAClB,GAAR,CAAa,wBAAb;AACH;AAED;AACJ;AACA;;;AACYoH,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKrI,YAAL,CAAkBsI,IAAlB,GAAyB,EAA7B,EAAiC;AAC7B,kBAAMvG,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,kBAAMwG,YAAsB,GAAG,EAA/B;AAEA,iBAAKvI,YAAL,CAAkB+E,OAAlB,CAA0B,CAACyD,KAAD,EAAQC,GAAR,KAAgB;AACtC,kBAAI1G,GAAG,GAAGyG,KAAK,CAACE,SAAZ,GAAwB,KAAKxI,mBAAL,GAA2B,CAAvD,EAA0D;AACtDqI,gBAAAA,YAAY,CAAC1D,IAAb,CAAkB4D,GAAlB;AACH;AACJ,aAJD;AAMAF,YAAAA,YAAY,CAACxD,OAAb,CAAqB0D,GAAG,IAAI,KAAKzI,YAAL,CAAkB2I,MAAlB,CAAyBF,GAAzB,CAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACYG,QAAAA,yBAAyB,GAAS;AACtC,gBAAM7G,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;;AACA,cAAIA,GAAG,GAAG,KAAKxB,qBAAX,GAAmC,KAAvC,EAA8C;AAC1C;AACA,kBAAMsI,YAAY,GACd,KAAKxI,YAAL,GAAoB,CAApB,GAAyB,KAAKC,aAAL,GAAqB,KAAKD,YAA3B,GAA2C,GAAnE,GAAyE,CAD7E;;AAGA,gBAAI,KAAKA,YAAL,GAAoB,CAAxB,EAA2B;AACvB8B,cAAAA,OAAO,CAAClB,GAAR,CACK,oBAAmB,KAAKZ,YAAa,WAAUwI,YAAY,CAACV,OAAb,CAAqB,CAArB,CAAwB,GAD5E;;AAIA,kBAAIU,YAAY,GAAG,EAAnB,EAAuB;AACnB1G,gBAAAA,OAAO,CAACC,IAAR,CACK,eAAcyG,YAAY,CAACV,OAAb,CAAqB,CAArB,CAAwB,YAD3C;AAGH;AACJ;;AAED,iBAAK5H,qBAAL,GAA6BwB,GAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACY+G,QAAAA,gBAAgB,CAACzE,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAACD,QAAQ,CAACC,SAAT,CAAmByE,QAA1D,EAAoE;AAChE1E,YAAAA,QAAQ,CAACC,SAAT,CAAmByE,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAO1E,QAAQ,CAAC2E,OAAhB,KAA4B,UAAhC,EAA4C;AACxC3E,cAAAA,QAAQ,CAAC2E,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAAC5E,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkCD,QAAQ,CAACC,SAAT,CAAmByE,QAAzD,EAAmE;AAC/D1E,YAAAA,QAAQ,CAACC,SAAT,CAAmByE,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAO1E,QAAQ,CAAC6E,aAAhB,KAAkC,UAAtC,EAAkD;AAC9C7E,cAAAA,QAAQ,CAAC6E,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYtI,QAAAA,YAAY,CAACqC,KAAD,EAAoB;AACpC;AACA,eAAKrD,gBAAL,CAAsBC,CAAtB,GAA0BoD,KAAK,CAACkG,YAAN,EAA1B;AACA,eAAKvJ,gBAAL,CAAsBE,CAAtB,GAA0BmD,KAAK,CAACmG,YAAN,EAA1B;AACA,eAAK1J,YAAL,GAAoBoC,IAAI,CAACC,GAAL,EAApB,CAJoC,CAMpC;;AACA,eAAKwD,gBAAL,GAPoC,CASpC;AACA;;AAEA,gBAAM8D,OAAO,GAAG,KAAKrG,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAIoG,OAAJ,EAAa;AACT,iBAAK/J,kBAAL,GAA0B+J,OAA1B;AACA,iBAAKP,gBAAL,CAAsBO,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACYvI,QAAAA,WAAW,CAACmC,KAAD,EAAoB;AACnC,gBAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,gBAAMuH,QAAQ,GAAGrG,KAAK,CAACkG,YAAN,EAAjB;AACA,gBAAMI,QAAQ,GAAGtG,KAAK,CAACmG,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAIvH,WAAW,GAAG,KAAKnC,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,gBAAM6J,MAAM,GAAGzC,IAAI,CAAC0C,GAAL,CAASH,QAAQ,GAAG,KAAK1J,gBAAL,CAAsBC,CAA1C,CAAf;AACA,gBAAM6J,MAAM,GAAG3C,IAAI,CAAC0C,GAAL,CAASF,QAAQ,GAAG,KAAK3J,gBAAL,CAAsBE,CAA1C,CAAf;AACA,gBAAMgH,QAAQ,GAAGC,IAAI,CAACC,IAAL,CAAUwC,MAAM,GAAGA,MAAT,GAAkBE,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAI5C,QAAQ,GAAG,KAAK/G,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,cAAI,KAAKT,kBAAL,IAA2BwH,QAAQ,GAAG,KAAK/G,qBAAL,GAA6B,CAAvE,EAA0E;AACtE;AACA,iBAAKL,YAAL,GAAoBmC,WAApB;AACA,iBAAKjC,gBAAL,CAAsBC,CAAtB,GAA0ByJ,QAA1B;AACA,iBAAK1J,gBAAL,CAAsBE,CAAtB,GAA0ByJ,QAA1B;AACA;AACH,WA1BkC,CA4BnC;;;AACA,eAAK7J,YAAL,GAAoBmC,WAApB;AACA,eAAKjC,gBAAL,CAAsBC,CAAtB,GAA0ByJ,QAA1B;AACA,eAAK1J,gBAAL,CAAsBE,CAAtB,GAA0ByJ,QAA1B,CA/BmC,CAiCnC;;AACA,gBAAMF,OAAO,GAAG,KAAKrG,oBAAL,CAA0BC,KAA1B,CAAhB,CAlCmC,CAoCnC;;AACA,cAAI,KAAK3D,kBAAL,KAA4B+J,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAK/J,kBAAT,EAA6B;AACzB,mBAAK2J,iBAAL,CAAuB,KAAK3J,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAI+J,OAAJ,EAAa;AACT,mBAAKP,gBAAL,CAAsBO,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAK/J,kBAAL,GAA0B+J,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACYrI,QAAAA,UAAU,CAACiC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAKtC,kBAAT,EAA6B;AACzB,mBAAK2J,iBAAL,CAAuB,KAAK3J,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,gBAAM+J,OAAO,GAAG,KAAKrG,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAK3D,kBAAL,IAA2B+J,OAAO,KAAK,KAAK/J,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAK2J,iBAAL,CAAuB,KAAK3J,kBAA5B;AAEA;AAAA;AAAA,8BAAK2B,GAAL,CAASC,WAAT,CACK,cAAa,+BAAK5B,kBAAL,CAAwB+B,IAAxB,2CAA8B6E,IAA9B,KAAsC,MAAO,EAD/D,EAJgE,CAQhE;;AACA,gBAAI,KAAKlE,wBAAL,CAA8B,KAAK1C,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqBuC,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAK1C,UAAL,IAAmB,KAAKA,UAAL,CAAgB6C,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAK7C,UAAL,CAAgB6C,kBAAhB,CAAmCyH,UAAnC,CAA8C,KAAKrK,kBAAL,CAAwB+B,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,CACK,gBAAe,gCAAK5B,kBAAL,CAAwB+B,IAAxB,4CAA8B6E,IAA9B,KAAsC,MAAO,EADjE,EAFA,CAMA;;AACA,uBAAKkC,mBAAL;AACH,iBARD,CAQE,OAAOwB,KAAP,EAAc;AACZzH,kBAAAA,OAAO,CAACyH,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAlBuD,CAoBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKpK,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aAxBD,MAwBO,CACN;AACJ,WAnCD,MAmCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAK2J,iBAAL,CAAuB,KAAK3J,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WAxDiC,CA0DlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AA7qBiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\nimport { SimpleItemDetector } from './SimpleItemDetector';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流 - 增强版\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测\n\n    // 🎯 射线检测缓存优化\n    private raycastCache: Map<string, { items: ItemSceneViewComp[]; timestamp: number }> =\n        new Map();\n    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算\n    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private raycastPositionThreshold: number = 8; // 射线检测位置阈值\n\n    // 🎯 简化配置 - 只保留必要的参数\n    private moveDistanceThreshold: number = 10; // 移动距离阈值，用于性能优化\n\n    // 🎯 简单性能监控\n    private raycastCount: number = 0;\n    private cacheHitCount: number = 0;\n    private lastPerformanceReport: number = 0;\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n\n        // 🧹 清理射线检测缓存\n        this.raycastCache.clear();\n        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🚀 精确射线检测 - 使用简化检测器\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        // 🎯 使用简化检测器 - 总是检测最前面的可见物品\n        return SimpleItemDetector.detectItem(event, 'precise');\n    }\n\n    /**\n     * 🎯 精确射线检测 - 只检测最前面的可见物品\n     */\n    private performPreciseRaycast(x: number, y: number): ItemSceneViewComp | null {\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        // 🎯 只执行一次射线检测，获取最前面的物品\n        if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n            const result = PhysicsSystem.instance.raycastClosestResult;\n            const hitNode = result.collider.node;\n            const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n            if (itemComp && itemComp.ItemModel) {\n                return itemComp;\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * 🎯 简化多层射线检测 - 只使用单点射线，逐层检测\n     */\n    private performSimpleMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 改进的层级切换 - 需要持续慢速移动才触发\n     */\n    private handleSimpleLayerSwitching(\n        items: ItemSceneViewComp[],\n        moveDistance: number\n    ): ItemSceneViewComp | null {\n        if (items.length <= 1) {\n            return items[0] || null;\n        }\n\n        const now = Date.now();\n        const isSlowMove = moveDistance < this.slowMoveThreshold;\n\n        // 🎯 如果不是慢速移动，直接返回顶层物品\n        if (!isSlowMove) {\n            this.exitSlowMoveMode();\n            return items[0];\n        }\n\n        // 🎯 开始记录慢速移动时间\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.currentItemIndex = 0;\n            this.candidateItems = items.map(item => ({ item, score: 100 }));\n            // 不立即开始切换，先返回顶层物品\n            return items[0];\n        }\n\n        // 🎯 检查是否持续慢速移动足够长时间\n        const slowMoveDuration = now - this.slowMoveStartTime;\n        if (slowMoveDuration < this.slowMoveRequiredTime) {\n            // 还没有持续足够长时间，继续返回顶层物品\n            return items[0];\n        }\n\n        // 🎯 现在开始层级切换\n        if (slowMoveDuration >= this.slowMoveRequiredTime) {\n            // 第一次进入切换模式\n            if (this.lastItemSwitchTime === 0) {\n                this.lastItemSwitchTime = now;\n                console.log(\n                    `🎯 持续慢速移动${this.slowMoveRequiredTime}ms，开始层级切换 (共${items.length}层)`\n                );\n            }\n\n            // 检查是否应该切换到下一层\n            if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n                this.currentItemIndex = (this.currentItemIndex + 1) % items.length;\n                this.lastItemSwitchTime = now;\n\n                console.log(\n                    `🔄 切换到第${this.currentItemIndex + 1}层: ${items[this.currentItemIndex].node?.name || '未知'}`\n                );\n            }\n        }\n\n        // 🎯 检查是否超时\n        if (slowMoveDuration > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return items[0]; // 返回顶层物品\n        }\n\n        return items[this.currentItemIndex] || items[0];\n    }\n\n    /**\n     * 🎯 合并和评分物品\n     */\n    private mergeAndScoreItems(\n        raycastItems: ItemSceneViewComp[],\n        screenItems: ItemSceneViewComp[],\n        touchX: number,\n        touchY: number\n    ): Array<{ item: ItemSceneViewComp; score: number }> {\n        const candidates = new Map<string, { item: ItemSceneViewComp; score: number }>();\n        const camera = smc.camera.CameraModel.camera;\n\n        // 处理射线检测结果（高优先级）\n        for (const item of raycastItems) {\n            const worldPos = item.node.worldPosition;\n            const screenPos = camera.worldToScreen(worldPos);\n            const distance = Math.sqrt(\n                Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n            );\n\n            candidates.set(item.node.uuid, {\n                item,\n                score: 100 - distance * 0.5, // 射线检测基础分100，距离越近分数越高\n            });\n        }\n\n        // 处理屏幕距离检测结果（低优先级）\n        for (const item of screenItems) {\n            if (!candidates.has(item.node.uuid)) {\n                const worldPos = item.node.worldPosition;\n                const screenPos = camera.worldToScreen(worldPos);\n                const distance = Math.sqrt(\n                    Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n                );\n\n                candidates.set(item.node.uuid, {\n                    item,\n                    score: 50 - distance * 0.3, // 屏幕距离基础分50\n                });\n            }\n        }\n\n        return Array.from(candidates.values());\n    }\n\n    /**\n     * 🎯 选择最佳物品\n     */\n    private selectBestItem(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        if (candidates.length === 0) {\n            return null;\n        }\n\n        // 按分数排序，选择最高分的物品\n        candidates.sort((a, b) => b.score - a.score);\n\n        const bestCandidate = candidates[0];\n\n        // 如果有多个候选物品，可以在这里实现慢速移动时的切换逻辑\n        if (candidates.length > 1) {\n            return this.handleMultipleCandidates(candidates);\n        }\n\n        return bestCandidate.item;\n    }\n\n    /**\n     * 🎯 处理多个候选物品\n     */\n    private handleMultipleCandidates(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        const now = Date.now();\n\n        // 检查是否为慢速移动模式\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.candidateItems = candidates;\n            this.currentItemIndex = 0;\n            console.log(`🎯 检测到${candidates.length}个候选物品，进入慢速选择模式`);\n        }\n\n        // 检查是否超时\n        if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return candidates[0].item; // 返回最高分物品\n        }\n\n        // 检查是否应该切换\n        if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n            this.currentItemIndex = (this.currentItemIndex + 1) % candidates.length;\n            this.lastItemSwitchTime = now;\n\n            const currentItem = candidates[this.currentItemIndex];\n            console.log(\n                `🔄 切换到候选物品: ${currentItem.item.node?.name || '未知'} (${this.currentItemIndex + 1}/${candidates.length})`\n            );\n        }\n\n        return candidates[this.currentItemIndex]?.item || candidates[0].item;\n    }\n\n    /**\n     * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品\n     */\n    private performMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const startTime = performance.now();\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层，平衡性能和功能\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        // 🎯 记录性能数据\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n\n        if (duration > 3) {\n            // 多层检测阈值稍高\n            console.warn(\n                `⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 退出慢速移动模式\n     */\n    private exitSlowMoveMode(): void {\n        if (this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = false;\n            this.currentItemIndex = 0;\n            this.candidateItems = [];\n            this.lastItemSwitchTime = 0; // 重置切换时间\n            console.log('🏁 退出慢速移动模式，回到顶层');\n        }\n    }\n\n    /**\n     * 🎯 暂停物品切换 - 选择物品后暂停一段时间\n     */\n    private pauseLayerSwitching(): void {\n        this.exitSlowMoveMode();\n        this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒\n        console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);\n    }\n\n    /**\n     * 🎯 清理过期缓存\n     */\n    private cleanupCache(): void {\n        if (this.raycastCache.size > 50) {\n            const now = Date.now();\n            const keysToDelete: string[] = [];\n\n            this.raycastCache.forEach((value, key) => {\n                if (now - value.timestamp > this.raycastCacheTimeout * 2) {\n                    keysToDelete.push(key);\n                }\n            });\n\n            keysToDelete.forEach(key => this.raycastCache.delete(key));\n        }\n    }\n\n    /**\n     * 🎯 性能报告（如果需要）\n     */\n    private reportPerformanceIfNeeded(): void {\n        const now = Date.now();\n        if (now - this.lastPerformanceReport > 10000) {\n            // 每10秒报告一次\n            const cacheHitRate =\n                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;\n\n            if (this.raycastCount > 0) {\n                console.log(\n                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`\n                );\n\n                if (cacheHitRate < 30) {\n                    console.warn(\n                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`\n                    );\n                }\n            }\n\n            this.lastPerformanceReport = now;\n        }\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件\n     */\n    private onTouchStart(event: EventTouch) {\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = Date.now();\n\n        // 🎯 触摸开始时重置慢速移动模式\n        this.exitSlowMoveMode();\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到30FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率\n        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {\n            // 更新位置但不执行射线检测，减少性能消耗\n            this.lastMoveTime = currentTime;\n            this.lastMovePosition.x = currentX;\n            this.lastMovePosition.y = currentY;\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n\n                        // 🎯 选择物品后暂停层级切换\n                        this.pauseLayerSwitching();\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}