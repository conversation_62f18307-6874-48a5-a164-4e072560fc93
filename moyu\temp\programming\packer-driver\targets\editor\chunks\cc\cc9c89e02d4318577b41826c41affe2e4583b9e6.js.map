{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "PhysicsSystem", "oops", "PHY_GROUP", "smc", "ItemSceneViewComp", "GameSceneViewComp", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "raycastCache", "Map", "raycastCacheTimeout", "lastRaycastPosition", "raycastPositionThreshold", "continuousDetectionEnabled", "detectionMethod", "raycastRadius", "maxDetectionDistance", "screenDistanceThreshold", "useScreenDistanceFallback", "currentBestItem", "itemSwitchDelay", "lastItemSwitchTime", "candidate<PERSON><PERSON>s", "currentItemIndex", "slowMoveThreshold", "isInSlowMoveMode", "slowMoveStartTime", "maxSlowMoveTime", "raycastCount", "cacheHitCount", "lastPerformanceReport", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "clear", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "camera", "CameraModel", "currentX", "getLocationX", "currentY", "getLocationY", "deltaX", "Math", "abs", "deltaY", "distance", "sqrt", "allItems", "performSimpleMultiLayerRaycast", "length", "exitSlowMoveMode", "handleSimpleLayerSwitching", "ray", "screenPointToRay", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "maxLayers", "disabledColliders", "layer", "instance", "raycastClosest", "result", "raycastClosestResult", "hitNode", "collider", "itemComp", "ItemModel", "find", "push", "enabled", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "items", "moveDistance", "isSlowMove", "name", "map", "score", "performHybridDetection", "startTime", "performance", "raycastItems", "performExpandedRaycast", "screenDistanceItems", "performScreenDistanceDetection", "allCandidates", "mergeAndScoreItems", "bestItem", "selectBestItem", "endTime", "duration", "toFixed", "foundItems", "Set", "radius", "points", "point", "uuid", "add", "touchX", "touchY", "getAllSceneItems", "worldPos", "worldPosition", "screenPos", "worldToScreen", "pow", "size", "screenItems", "candidates", "set", "Array", "from", "values", "sort", "a", "b", "bestCandidate", "handleMultipleCandidates", "currentItem", "performMultiLayerRaycast", "pauseLayerSwitching", "cleanupCache", "keysToDelete", "value", "key", "timestamp", "delete", "reportPerformanceIfNeeded", "cacheHitRate", "applyTouchEffect", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "hitItem", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,a,OAAAA,a;;AACjDC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;wCAEaY,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CX,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CY,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,EAbU;AAaN;AAE5C;AAfkD,eAgB1CC,YAhB0C,GAiB9C,IAAIC,GAAJ,EAjB8C;AAAA,eAkB1CC,mBAlB0C,GAkBZ,GAlBY;AAkBP;AAlBO,eAmB1CC,mBAnB0C,GAmBM;AAAEN,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAnBN;AAAA,eAoB1CM,wBApB0C,GAoBP,CApBO;AAoBJ;AAE9C;AAtBkD,eAuB1CC,0BAvB0C,GAuBJ,IAvBI;AAuBE;AAvBF,eAwB1CC,eAxB0C,GAwBkB,QAxBlB;AAwB4B;AAE9E;AA1BkD,eA2B1CC,aA3B0C,GA2BlB,EA3BkB;AA2Bd;AA3Bc,eA4B1CC,oBA5B0C,GA4BX,GA5BW;AA4BN;AAE5C;AA9BkD,eA+B1CC,uBA/B0C,GA+BR,EA/BQ;AA+BJ;AA/BI,eAgC1CC,yBAhC0C,GAgCL,IAhCK;AAgCC;AAEnD;AAlCkD,eAmC1CC,eAnC0C,GAmCE,IAnCF;AAmCQ;AAnCR,eAoC1CC,eApC0C,GAoChB,GApCgB;AAoCX;AApCW,eAqC1CC,kBArC0C,GAqCb,CArCa;AAAA,eAsC1CC,cAtC0C,GAsC0B,EAtC1B;AAsC8B;AAtC9B,eAuC1CC,gBAvC0C,GAuCf,CAvCe;AAuCZ;AAEtC;AAzCkD,eA0C1CC,iBA1C0C,GA0Cd,CA1Cc;AA0CX;AA1CW,eA2C1CC,gBA3C0C,GA2Cd,KA3Cc;AAAA,eA4C1CC,iBA5C0C,GA4Cd,CA5Cc;AAAA,eA6C1CC,eA7C0C,GA6ChB,IA7CgB;AA6CV;AAExC;AA/CkD,eAgD1CC,YAhD0C,GAgDnB,CAhDmB;AAAA,eAiD1CC,aAjD0C,GAiDlB,CAjDkB;AAAA,eAkD1CC,qBAlD0C,GAkDV,CAlDU;AAAA;;AAoDlDC,QAAAA,MAAM,GAAG;AACL7C,UAAAA,KAAK,CAAC8C,EAAN,CAAS7C,KAAK,CAAC8C,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACAjD,UAAAA,KAAK,CAAC8C,EAAN,CAAS7C,KAAK,CAAC8C,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACAnD,UAAAA,KAAK,CAAC8C,EAAN,CAAS7C,KAAK,CAAC8C,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAKjD,UAAL,GAAkB8C,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,CACK,0CAAyC,KAAK5C,UAAL,GAAkB,IAAlB,GAAyB,IAAK,EAD5E;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAK2C,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR9D,UAAAA,KAAK,CAAC+D,GAAN,CAAU9D,KAAK,CAAC8C,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACAjD,UAAAA,KAAK,CAAC+D,GAAN,CAAU9D,KAAK,CAAC8C,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACAnD,UAAAA,KAAK,CAAC+D,GAAN,CAAU9D,KAAK,CAAC8C,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD,EAHQ,CAKR;;AACA,eAAK/B,YAAL,CAAkB0C,KAAlB;AACA;AAAA;AAAA,4BAAKV,GAAL,CAASC,WAAT,CAAqB,uCAArB;AACH;AAED;AACJ;AACA;;;AACYU,QAAAA,eAAe,GAAY;AAC/B,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAKrD,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYsD,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAK3D,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgB4D,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,gBAAMC,QAAQ,GAAGJ,IAAI,CAACZ,IAAtB;AACA,gBAAMa,kBAAkB,GAAG,KAAK5D,UAAL,CAAgB4D,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,kBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,gBAAMC,YAAY,GAAG,KAAKpE,UAAL,CAAgBqE,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAK3E,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH;;AAED,gBAAM4E,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,gBAAME,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAXsE,CAatE;;AACA,gBAAMC,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKhE,mBAAL,CAAyBN,CAA7C,CAAf;AACA,gBAAM6E,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKlE,mBAAL,CAAyBL,CAA7C,CAAf;AACA,gBAAM6E,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB,CAhBsE,CAkBtE;;AACA,gBAAMG,QAAQ,GAAG,KAAKC,8BAAL,CAAoCX,QAApC,EAA8CE,QAA9C,CAAjB,CAnBsE,CAqBtE;;AACA,eAAKlE,mBAAL,CAAyBN,CAAzB,GAA6BsE,QAA7B;AACA,eAAKhE,mBAAL,CAAyBL,CAAzB,GAA6BuE,QAA7B,CAvBsE,CAyBtE;;AACA,cAAIQ,QAAQ,CAACE,MAAT,KAAoB,CAAxB,EAA2B;AACvB,iBAAKC,gBAAL;AACA,mBAAO,IAAP;AACH,WA7BqE,CA+BtE;;;AACA,cAAIH,QAAQ,CAACE,MAAT,KAAoB,CAAxB,EAA2B;AACvB,iBAAKC,gBAAL;AACA,mBAAOH,QAAQ,CAAC,CAAD,CAAf;AACH,WAnCqE,CAqCtE;;;AACA,iBAAO,KAAKI,0BAAL,CAAgCJ,QAAhC,EAA0CF,QAA1C,CAAP;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,8BAA8B,CAACjF,CAAD,EAAYC,CAAZ,EAA4C;AAC9E,gBAAMmE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,gBAAMiB,GAAG,GAAGjB,MAAM,CAACkB,gBAAP,CAAwBtF,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAMsF,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,gBAAMV,QAA6B,GAAG,EAAtC;AACA,gBAAMW,SAAS,GAAG,CAAlB,CAN8E,CAMzD;;AACrB,gBAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AACA;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,SAA5B,EAAuCE,KAAK,EAA5C,EAAgD;AAC5C,kBAAI9G,aAAa,CAAC+G,QAAd,CAAuBC,cAAvB,CAAsCV,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,sBAAMS,MAAM,GAAGjH,aAAa,CAAC+G,QAAd,CAAuBG,oBAAtC;AACA,sBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB5D,IAAhC;AACA,sBAAM6D,QAAQ,GAAGF,OAAO,CAAC1D,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAI4D,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACrB,QAAQ,CAACsB,IAAT,CAAcnD,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAc2D,OAApC,CAAL,EAAmD;AAC/ClB,oBAAAA,QAAQ,CAACuB,IAAT,CAAcH,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,wBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACK,OAAb,EAAsB;AAClBL,oBAAAA,QAAQ,CAACK,OAAT,GAAmB,KAAnB;AACAZ,oBAAAA,iBAAiB,CAACW,IAAlB,CAAuBJ,QAAvB;AACH;AACJ,iBAZD,MAYO;AACH,wBADG,CACI;AACV;AACJ,eApBD,MAoBO;AACH,sBADG,CACI;AACV;AACJ;AACJ,WA3BD,SA2BU;AACN;AACAP,YAAAA,iBAAiB,CAACa,OAAlB,CAA0BN,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACO,OAAzB,EAAkC;AAC9BP,gBAAAA,QAAQ,CAACK,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH;;AAED,iBAAOxB,QAAP;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,0BAA0B,CAC9BuB,KAD8B,EAE9BC,YAF8B,EAGN;AACxB,cAAID,KAAK,CAACzB,MAAN,IAAgB,CAApB,EAAuB;AACnB,mBAAOyB,KAAK,CAAC,CAAD,CAAL,IAAY,IAAnB;AACH;;AAED,gBAAM1D,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,gBAAM4D,UAAU,GAAGD,YAAY,GAAG,KAAKzF,iBAAvC,CANwB,CAQxB;;AACA,cAAI,CAAC0F,UAAL,EAAiB;AAAA;;AACb,iBAAK1B,gBAAL;AACA9B,YAAAA,OAAO,CAAClB,GAAR,CAAa,mBAAkB,kBAAAwE,KAAK,CAAC,CAAD,CAAL,CAASpE,IAAT,mCAAeuE,IAAf,KAAuB,IAAK,EAA3D;AACA,mBAAOH,KAAK,CAAC,CAAD,CAAZ;AACH,WAbuB,CAexB;;;AACA,cAAI,CAAC,KAAKvF,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyB4B,GAAzB;AACA,iBAAK/B,gBAAL,GAAwB,CAAxB;AACA,iBAAKD,cAAL,GAAsB0F,KAAK,CAACI,GAAN,CAAU5D,IAAI,KAAK;AAAEA,cAAAA,IAAF;AAAQ6D,cAAAA,KAAK,EAAE;AAAf,aAAL,CAAd,CAAtB;AACA3D,YAAAA,OAAO,CAAClB,GAAR,CAAa,cAAawE,KAAK,CAACzB,MAAO,cAAvC;AACH,WAtBuB,CAwBxB;;;AACA,cAAIjC,GAAG,GAAG,KAAK5B,iBAAX,GAA+B,KAAKC,eAAxC,EAAyD;AACrD,iBAAK6D,gBAAL;AACA,mBAAOwB,KAAK,CAAC,CAAD,CAAZ,CAFqD,CAEpC;AACpB,WA5BuB,CA8BxB;;;AACA,cAAI1D,GAAG,GAAG,KAAKjC,kBAAX,GAAgC,KAAKD,eAAzC,EAA0D;AAAA;;AACtD,iBAAKG,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8ByF,KAAK,CAACzB,MAA5D;AACA,iBAAKlE,kBAAL,GAA0BiC,GAA1B;AAEAI,YAAAA,OAAO,CAAClB,GAAR,CACK,UAAS,KAAKjB,gBAAL,GAAwB,CAAE,MAAK,0BAAAyF,KAAK,CAAC,KAAKzF,gBAAN,CAAL,CAA6BqB,IAA7B,2CAAmCuE,IAAnC,KAA2C,IAAK,EAD7F;AAGH;;AAED,iBAAOH,KAAK,CAAC,KAAKzF,gBAAN,CAAL,IAAgCyF,KAAK,CAAC,CAAD,CAA5C;AACH;AAED;AACJ;AACA;;;AACYM,QAAAA,sBAAsB,CAACjH,CAAD,EAAYC,CAAZ,EAAiD;AAC3E,gBAAMiH,SAAS,GAAGC,WAAW,CAAClE,GAAZ,EAAlB,CAD2E,CAG3E;;AACA,gBAAMmE,YAAY,GAAG,KAAKC,sBAAL,CAA4BrH,CAA5B,EAA+BC,CAA/B,CAArB,CAJ2E,CAM3E;;AACA,gBAAMqH,mBAAmB,GAAG,KAAKC,8BAAL,CAAoCvH,CAApC,EAAuCC,CAAvC,CAA5B,CAP2E,CAS3E;;AACA,gBAAMuH,aAAa,GAAG,KAAKC,kBAAL,CAAwBL,YAAxB,EAAsCE,mBAAtC,EAA2DtH,CAA3D,EAA8DC,CAA9D,CAAtB,CAV2E,CAY3E;;AACA,gBAAMyH,QAAQ,GAAG,KAAKC,cAAL,CAAoBH,aAApB,CAAjB,CAb2E,CAe3E;;AACA,gBAAMI,OAAO,GAAGT,WAAW,CAAClE,GAAZ,EAAhB;AACA,gBAAM4E,QAAQ,GAAGD,OAAO,GAAGV,SAA3B;AACA,eAAK3F,YAAL;;AAEA,cAAIsG,QAAQ,GAAG,CAAf,EAAkB;AACdxE,YAAAA,OAAO,CAACC,IAAR,CAAc,cAAauE,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CAAoB,IAA/C;AACH;;AAED,iBAAOJ,QAAP;AACH;AAED;AACJ;AACA;;;AACYL,QAAAA,sBAAsB,CAACrH,CAAD,EAAYC,CAAZ,EAA4C;AACtE,gBAAMmE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,gBAAMmB,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AACA,gBAAMiB,KAA0B,GAAG,EAAnC;AACA,gBAAMoB,UAAU,GAAG,IAAIC,GAAJ,EAAnB,CAJsE,CAMtE;;AACA,gBAAMC,MAAM,GAAG,KAAKvH,aAApB;AACA,gBAAMwH,MAAM,GAAG,CACX;AAAElI,YAAAA,CAAF;AAAKC,YAAAA;AAAL,WADW,EACD;AACV;AAAED,YAAAA,CAAC,EAAEA,CAAC,GAAGiI,MAAT;AAAiBhI,YAAAA;AAAjB,WAFW,EAEW;AACtB;AAAED,YAAAA,CAAC,EAAEA,CAAC,GAAGiI,MAAT;AAAiBhI,YAAAA;AAAjB,WAHW,EAGW;AACtB;AAAED,YAAAA,CAAF;AAAKC,YAAAA,CAAC,EAAEA,CAAC,GAAGgI;AAAZ,WAJW,EAIW;AACtB;AAAEjI,YAAAA,CAAF;AAAKC,YAAAA,CAAC,EAAEA,CAAC,GAAGgI;AAAZ,WALW,CAKW;AALX,WAAf;;AAQA,eAAK,MAAME,KAAX,IAAoBD,MAApB,EAA4B;AACxB,kBAAM7C,GAAG,GAAGjB,MAAM,CAACkB,gBAAP,CAAwB6C,KAAK,CAACnI,CAA9B,EAAiCmI,KAAK,CAAClI,CAAvC,CAAZ;;AAEA,gBAAIlB,aAAa,CAAC+G,QAAd,CAAuBC,cAAvB,CAAsCV,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,oBAAMS,MAAM,GAAGjH,aAAa,CAAC+G,QAAd,CAAuBG,oBAAtC;AACA,oBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB5D,IAAhC;AACA,oBAAM6D,QAAQ,GAAGF,OAAO,CAAC1D,YAAR;AAAA;AAAA,yDAAjB;;AAEA,kBAAI4D,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAAC0B,UAAU,CAAChE,GAAX,CAAemC,OAAO,CAACkC,IAAvB,CAAvC,EAAqE;AACjEzB,gBAAAA,KAAK,CAACJ,IAAN,CAAWH,QAAX;AACA2B,gBAAAA,UAAU,CAACM,GAAX,CAAenC,OAAO,CAACkC,IAAvB;AACH;AACJ;AACJ;;AAED,iBAAOzB,KAAP;AACH;AAED;AACJ;AACA;;;AACYY,QAAAA,8BAA8B,CAACe,MAAD,EAAiBC,MAAjB,EAAsD;AACxF,gBAAMnE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,gBAAMuC,KAA0B,GAAG,EAAnC,CAFwF,CAIxF;;AACA,gBAAM3B,QAAQ,GAAG,KAAKwD,gBAAL,EAAjB;;AAEA,eAAK,MAAMrF,IAAX,IAAmB6B,QAAnB,EAA6B;AACzB,gBAAI,CAAC7B,IAAI,CAACZ,IAAN,IAAc,CAACY,IAAI,CAACkD,SAAxB,EAAmC,SADV,CAGzB;;AACA,kBAAMoC,QAAQ,GAAGtF,IAAI,CAACZ,IAAL,CAAUmG,aAA3B;AACA,kBAAMC,SAAS,GAAGvE,MAAM,CAACwE,aAAP,CAAqBH,QAArB,CAAlB,CALyB,CAOzB;;AACA,kBAAM3D,QAAQ,GAAGH,IAAI,CAACI,IAAL,CACbJ,IAAI,CAACkE,GAAL,CAASF,SAAS,CAAC3I,CAAV,GAAcsI,MAAvB,EAA+B,CAA/B,IAAoC3D,IAAI,CAACkE,GAAL,CAASF,SAAS,CAAC1I,CAAV,GAAcsI,MAAvB,EAA+B,CAA/B,CADvB,CAAjB,CARyB,CAYzB;;AACA,gBAAIzD,QAAQ,IAAI,KAAKlE,uBAArB,EAA8C;AAC1C+F,cAAAA,KAAK,CAACJ,IAAN,CAAWpD,IAAX;AACH;AACJ;;AAED,iBAAOwD,KAAP;AACH;AAED;AACJ;AACA;;;AACY6B,QAAAA,gBAAgB,GAAwB;AAC5C,gBAAM7B,KAA0B,GAAG,EAAnC;;AAEA,cAAI,CAAC,KAAKnH,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgBqE,SAAzC,EAAoD;AAChD,mBAAO8C,KAAP;AACH,WAL2C,CAO5C;;;AACA,gBAAM7C,cAAc,GAAG,KAAKtE,UAAL,CAAgBqE,SAAhB,CAA0BC,cAAjD;;AACA,cAAIA,cAAc,IAAIA,cAAc,CAACgF,IAAf,GAAsB,CAA5C,EAA+C;AAC3ChF,YAAAA,cAAc,CAAC2C,OAAf,CAAuBlD,QAAQ,IAAI;AAC/B,kBAAIA,QAAQ,IAAIA,QAAQ,CAACmD,OAAzB,EAAkC;AAC9B,sBAAMN,QAAQ,GAAG7C,QAAQ,CAACf,YAAT;AAAA;AAAA,2DAAjB;;AACA,oBAAI4D,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChCM,kBAAAA,KAAK,CAACJ,IAAN,CAAWH,QAAX;AACH;AACJ;AACJ,aAPD;AAQH;;AAED,iBAAOO,KAAP;AACH;AAED;AACJ;AACA;;;AACYc,QAAAA,kBAAkB,CACtBL,YADsB,EAEtB2B,WAFsB,EAGtBT,MAHsB,EAItBC,MAJsB,EAK2B;AACjD,gBAAMS,UAAU,GAAG,IAAI5I,GAAJ,EAAnB;AACA,gBAAMgE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC,CAFiD,CAIjD;;AACA,eAAK,MAAMjB,IAAX,IAAmBiE,YAAnB,EAAiC;AAC7B,kBAAMqB,QAAQ,GAAGtF,IAAI,CAACZ,IAAL,CAAUmG,aAA3B;AACA,kBAAMC,SAAS,GAAGvE,MAAM,CAACwE,aAAP,CAAqBH,QAArB,CAAlB;AACA,kBAAM3D,QAAQ,GAAGH,IAAI,CAACI,IAAL,CACbJ,IAAI,CAACkE,GAAL,CAASF,SAAS,CAAC3I,CAAV,GAAcsI,MAAvB,EAA+B,CAA/B,IAAoC3D,IAAI,CAACkE,GAAL,CAASF,SAAS,CAAC1I,CAAV,GAAcsI,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;AAIAS,YAAAA,UAAU,CAACC,GAAX,CAAe9F,IAAI,CAACZ,IAAL,CAAU6F,IAAzB,EAA+B;AAC3BjF,cAAAA,IAD2B;AAE3B6D,cAAAA,KAAK,EAAE,MAAMlC,QAAQ,GAAG,GAFG,CAEE;;AAFF,aAA/B;AAIH,WAhBgD,CAkBjD;;;AACA,eAAK,MAAM3B,IAAX,IAAmB4F,WAAnB,EAAgC;AAC5B,gBAAI,CAACC,UAAU,CAACjF,GAAX,CAAeZ,IAAI,CAACZ,IAAL,CAAU6F,IAAzB,CAAL,EAAqC;AACjC,oBAAMK,QAAQ,GAAGtF,IAAI,CAACZ,IAAL,CAAUmG,aAA3B;AACA,oBAAMC,SAAS,GAAGvE,MAAM,CAACwE,aAAP,CAAqBH,QAArB,CAAlB;AACA,oBAAM3D,QAAQ,GAAGH,IAAI,CAACI,IAAL,CACbJ,IAAI,CAACkE,GAAL,CAASF,SAAS,CAAC3I,CAAV,GAAcsI,MAAvB,EAA+B,CAA/B,IAAoC3D,IAAI,CAACkE,GAAL,CAASF,SAAS,CAAC1I,CAAV,GAAcsI,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;AAIAS,cAAAA,UAAU,CAACC,GAAX,CAAe9F,IAAI,CAACZ,IAAL,CAAU6F,IAAzB,EAA+B;AAC3BjF,gBAAAA,IAD2B;AAE3B6D,gBAAAA,KAAK,EAAE,KAAKlC,QAAQ,GAAG,GAFI,CAEC;;AAFD,eAA/B;AAIH;AACJ;;AAED,iBAAOoE,KAAK,CAACC,IAAN,CAAWH,UAAU,CAACI,MAAX,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AACYzB,QAAAA,cAAc,CAClBqB,UADkB,EAEM;AACxB,cAAIA,UAAU,CAAC9D,MAAX,KAAsB,CAA1B,EAA6B;AACzB,mBAAO,IAAP;AACH,WAHuB,CAKxB;;;AACA8D,UAAAA,UAAU,CAACK,IAAX,CAAgB,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAACvC,KAAF,GAAUsC,CAAC,CAACtC,KAAtC;AAEA,gBAAMwC,aAAa,GAAGR,UAAU,CAAC,CAAD,CAAhC,CARwB,CAUxB;;AACA,cAAIA,UAAU,CAAC9D,MAAX,GAAoB,CAAxB,EAA2B;AACvB,mBAAO,KAAKuE,wBAAL,CAA8BT,UAA9B,CAAP;AACH;;AAED,iBAAOQ,aAAa,CAACrG,IAArB;AACH;AAED;AACJ;AACA;;;AACYsG,QAAAA,wBAAwB,CAC5BT,UAD4B,EAEJ;AAAA;;AACxB,gBAAM/F,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CADwB,CAGxB;;AACA,cAAI,CAAC,KAAK7B,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyB4B,GAAzB;AACA,iBAAKhC,cAAL,GAAsB+H,UAAtB;AACA,iBAAK9H,gBAAL,GAAwB,CAAxB;AACAmC,YAAAA,OAAO,CAAClB,GAAR,CAAa,SAAQ6G,UAAU,CAAC9D,MAAO,gBAAvC;AACH,WAVuB,CAYxB;;;AACA,cAAIjC,GAAG,GAAG,KAAK5B,iBAAX,GAA+B,KAAKC,eAAxC,EAAyD;AACrD,iBAAK6D,gBAAL;AACA,mBAAO6D,UAAU,CAAC,CAAD,CAAV,CAAc7F,IAArB,CAFqD,CAE1B;AAC9B,WAhBuB,CAkBxB;;;AACA,cAAIF,GAAG,GAAG,KAAKjC,kBAAX,GAAgC,KAAKD,eAAzC,EAA0D;AAAA;;AACtD,iBAAKG,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8B8H,UAAU,CAAC9D,MAAjE;AACA,iBAAKlE,kBAAL,GAA0BiC,GAA1B;AAEA,kBAAMyG,WAAW,GAAGV,UAAU,CAAC,KAAK9H,gBAAN,CAA9B;AACAmC,YAAAA,OAAO,CAAClB,GAAR,CACK,eAAc,0BAAAuH,WAAW,CAACvG,IAAZ,CAAiBZ,IAAjB,2CAAuBuE,IAAvB,KAA+B,IAAK,KAAI,KAAK5F,gBAAL,GAAwB,CAAE,IAAG8H,UAAU,CAAC9D,MAAO,GAD1G;AAGH;;AAED,iBAAO,0BAAA8D,UAAU,CAAC,KAAK9H,gBAAN,CAAV,2CAAmCiC,IAAnC,KAA2C6F,UAAU,CAAC,CAAD,CAAV,CAAc7F,IAAhE;AACH;AAED;AACJ;AACA;;;AACYwG,QAAAA,wBAAwB,CAAC3J,CAAD,EAAYC,CAAZ,EAA4C;AACxE,gBAAMiH,SAAS,GAAGC,WAAW,CAAClE,GAAZ,EAAlB;AACA,gBAAMmB,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,gBAAMiB,GAAG,GAAGjB,MAAM,CAACkB,gBAAP,CAAwBtF,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,gBAAMsF,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,gBAAMV,QAA6B,GAAG,EAAtC;AACA,gBAAMW,SAAS,GAAG,CAAlB,CAPwE,CAOnD;;AACrB,gBAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AACA;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,SAA5B,EAAuCE,KAAK,EAA5C,EAAgD;AAC5C,kBAAI9G,aAAa,CAAC+G,QAAd,CAAuBC,cAAvB,CAAsCV,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,sBAAMS,MAAM,GAAGjH,aAAa,CAAC+G,QAAd,CAAuBG,oBAAtC;AACA,sBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB5D,IAAhC;AACA,sBAAM6D,QAAQ,GAAGF,OAAO,CAAC1D,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAI4D,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACrB,QAAQ,CAACsB,IAAT,CAAcnD,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAc2D,OAApC,CAAL,EAAmD;AAC/ClB,oBAAAA,QAAQ,CAACuB,IAAT,CAAcH,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,wBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACK,OAAb,EAAsB;AAClBL,oBAAAA,QAAQ,CAACK,OAAT,GAAmB,KAAnB;AACAZ,oBAAAA,iBAAiB,CAACW,IAAlB,CAAuBJ,QAAvB;AACH;AACJ,iBAZD,MAYO;AACH,wBADG,CACI;AACV;AACJ,eApBD,MAoBO;AACH,sBADG,CACI;AACV;AACJ;AACJ,WA3BD,SA2BU;AACN;AACAP,YAAAA,iBAAiB,CAACa,OAAlB,CAA0BN,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACO,OAAzB,EAAkC;AAC9BP,gBAAAA,QAAQ,CAACK,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH,WA5CuE,CA8CxE;;;AACA,gBAAMoB,OAAO,GAAGT,WAAW,CAAClE,GAAZ,EAAhB;AACA,gBAAM4E,QAAQ,GAAGD,OAAO,GAAGV,SAA3B;;AAEA,cAAIW,QAAQ,GAAG,CAAf,EAAkB;AACd;AACAxE,YAAAA,OAAO,CAACC,IAAR,CACK,gBAAeuE,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CAAoB,SAAQ9C,QAAQ,CAACE,MAAO,KADhE;AAGH;;AAED,iBAAOF,QAAP;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,gBAAgB,GAAS;AAC7B,cAAI,KAAK/D,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,GAAwB,KAAxB;AACA,iBAAKF,gBAAL,GAAwB,CAAxB;AACA,iBAAKD,cAAL,GAAsB,EAAtB;AACAoC,YAAAA,OAAO,CAAClB,GAAR,CAAY,kBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYyH,QAAAA,mBAAmB,GAAS;AAChC,eAAKzE,gBAAL;AACA,eAAKnE,kBAAL,GAA0BgC,IAAI,CAACC,GAAL,KAAa,IAAvC,CAFgC,CAEa;;AAC7CI,UAAAA,OAAO,CAAClB,GAAR,CAAa,wBAAb;AACH;AAED;AACJ;AACA;;;AACY0H,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAK1J,YAAL,CAAkB2I,IAAlB,GAAyB,EAA7B,EAAiC;AAC7B,kBAAM7F,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,kBAAM6G,YAAsB,GAAG,EAA/B;AAEA,iBAAK3J,YAAL,CAAkBsG,OAAlB,CAA0B,CAACsD,KAAD,EAAQC,GAAR,KAAgB;AACtC,kBAAI/G,GAAG,GAAG8G,KAAK,CAACE,SAAZ,GAAwB,KAAK5J,mBAAL,GAA2B,CAAvD,EAA0D;AACtDyJ,gBAAAA,YAAY,CAACvD,IAAb,CAAkByD,GAAlB;AACH;AACJ,aAJD;AAMAF,YAAAA,YAAY,CAACrD,OAAb,CAAqBuD,GAAG,IAAI,KAAK7J,YAAL,CAAkB+J,MAAlB,CAAyBF,GAAzB,CAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACYG,QAAAA,yBAAyB,GAAS;AACtC,gBAAMlH,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;;AACA,cAAIA,GAAG,GAAG,KAAKxB,qBAAX,GAAmC,KAAvC,EAA8C;AAC1C;AACA,kBAAM2I,YAAY,GACd,KAAK7I,YAAL,GAAoB,CAApB,GAAyB,KAAKC,aAAL,GAAqB,KAAKD,YAA3B,GAA2C,GAAnE,GAAyE,CAD7E;;AAGA,gBAAI,KAAKA,YAAL,GAAoB,CAAxB,EAA2B;AACvB8B,cAAAA,OAAO,CAAClB,GAAR,CACK,oBAAmB,KAAKZ,YAAa,WAAU6I,YAAY,CAACtC,OAAb,CAAqB,CAArB,CAAwB,GAD5E;;AAIA,kBAAIsC,YAAY,GAAG,EAAnB,EAAuB;AACnB/G,gBAAAA,OAAO,CAACC,IAAR,CACK,eAAc8G,YAAY,CAACtC,OAAb,CAAqB,CAArB,CAAwB,YAD3C;AAGH;AACJ;;AAED,iBAAKrG,qBAAL,GAA6BwB,GAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACYoH,QAAAA,gBAAgB,CAACjE,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAACD,QAAQ,CAACC,SAAT,CAAmBiE,QAA1D,EAAoE;AAChElE,YAAAA,QAAQ,CAACC,SAAT,CAAmBiE,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAOlE,QAAQ,CAACmE,OAAhB,KAA4B,UAAhC,EAA4C;AACxCnE,cAAAA,QAAQ,CAACmE,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAACpE,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkCD,QAAQ,CAACC,SAAT,CAAmBiE,QAAzD,EAAmE;AAC/DlE,YAAAA,QAAQ,CAACC,SAAT,CAAmBiE,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAOlE,QAAQ,CAACqE,aAAhB,KAAkC,UAAtC,EAAkD;AAC9CrE,cAAAA,QAAQ,CAACqE,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACY3I,QAAAA,YAAY,CAACqC,KAAD,EAAoB;AACpC;AACA,eAAKpE,gBAAL,CAAsBC,CAAtB,GAA0BmE,KAAK,CAACI,YAAN,EAA1B;AACA,eAAKxE,gBAAL,CAAsBE,CAAtB,GAA0BkE,KAAK,CAACM,YAAN,EAA1B;AACA,eAAK5E,YAAL,GAAoBmD,IAAI,CAACC,GAAL,EAApB,CAJoC,CAMpC;;AACA,eAAKkC,gBAAL,GAPoC,CASpC;AACA;;AAEA,gBAAMuF,OAAO,GAAG,KAAKxG,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAIuG,OAAJ,EAAa;AACT,iBAAKjL,kBAAL,GAA0BiL,OAA1B;AACA,iBAAKL,gBAAL,CAAsBK,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACY1I,QAAAA,WAAW,CAACmC,KAAD,EAAoB;AACnC,gBAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,gBAAMqB,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAI1B,WAAW,GAAG,KAAKlD,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,gBAAM4E,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKvE,gBAAL,CAAsBC,CAA1C,CAAf;AACA,gBAAM6E,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKzE,gBAAL,CAAsBE,CAA1C,CAAf;AACA,gBAAM6E,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAIC,QAAQ,GAAG,KAAK5E,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,cAAI,KAAKT,kBAAL,IAA2BqF,QAAQ,GAAG,KAAK5E,qBAAL,GAA6B,CAAvE,EAA0E;AACtE;AACA,iBAAKL,YAAL,GAAoBkD,WAApB;AACA,iBAAKhD,gBAAL,CAAsBC,CAAtB,GAA0BsE,QAA1B;AACA,iBAAKvE,gBAAL,CAAsBE,CAAtB,GAA0BuE,QAA1B;AACA;AACH,WA1BkC,CA4BnC;;;AACA,eAAK3E,YAAL,GAAoBkD,WAApB;AACA,eAAKhD,gBAAL,CAAsBC,CAAtB,GAA0BsE,QAA1B;AACA,eAAKvE,gBAAL,CAAsBE,CAAtB,GAA0BuE,QAA1B,CA/BmC,CAiCnC;;AACA,gBAAMkG,OAAO,GAAG,KAAKxG,oBAAL,CAA0BC,KAA1B,CAAhB,CAlCmC,CAoCnC;;AACA,cAAI,KAAK1E,kBAAL,KAA4BiL,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAKjL,kBAAT,EAA6B;AACzB,mBAAK+K,iBAAL,CAAuB,KAAK/K,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAIiL,OAAJ,EAAa;AACT,mBAAKL,gBAAL,CAAsBK,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAKjL,kBAAL,GAA0BiL,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACYxI,QAAAA,UAAU,CAACiC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAKrD,kBAAT,EAA6B;AACzB,mBAAK+K,iBAAL,CAAuB,KAAK/K,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,gBAAMiL,OAAO,GAAG,KAAKxG,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAK1E,kBAAL,IAA2BiL,OAAO,KAAK,KAAKjL,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAK+K,iBAAL,CAAuB,KAAK/K,kBAA5B;AAEA;AAAA;AAAA,8BAAK0C,GAAL,CAASC,WAAT,CACK,cAAa,+BAAK3C,kBAAL,CAAwB8C,IAAxB,2CAA8BuE,IAA9B,KAAsC,MAAO,EAD/D,EAJgE,CAQhE;;AACA,gBAAI,KAAK5D,wBAAL,CAA8B,KAAKzD,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqBsD,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAKzD,UAAL,IAAmB,KAAKA,UAAL,CAAgB4D,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAK5D,UAAL,CAAgB4D,kBAAhB,CAAmCuH,UAAnC,CAA8C,KAAKlL,kBAAL,CAAwB8C,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,CACK,gBAAe,gCAAK3C,kBAAL,CAAwB8C,IAAxB,4CAA8BuE,IAA9B,KAAsC,MAAO,EADjE,EAFA,CAMA;;AACA,uBAAK8C,mBAAL;AACH,iBARD,CAQE,OAAOgB,KAAP,EAAc;AACZvH,kBAAAA,OAAO,CAACuH,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAlBuD,CAoBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKjL,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aAxBD,MAwBO,CACN;AACJ,WAnCD,MAmCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAK+K,iBAAL,CAAuB,KAAK/K,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WAxDiC,CA0DlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AAtzBiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\n\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流 - 增强版\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测\n\n    // 🎯 射线检测缓存优化\n    private raycastCache: Map<string, { items: ItemSceneViewComp[]; timestamp: number }> =\n        new Map();\n    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算\n    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private raycastPositionThreshold: number = 8; // 射线检测位置阈值\n\n    // 🎯 连续检测系统 - 解决慢速移动问题\n    private continuousDetectionEnabled: boolean = true; // 启用连续检测\n    private detectionMethod: 'raycast' | 'screen_distance' | 'hybrid' = 'hybrid'; // 检测方法\n\n    // 🎯 射线检测配置\n    private raycastRadius: number = 20; // 射线检测扩展半径\n    private maxDetectionDistance: number = 100; // 最大检测距离（屏幕像素）\n\n    // 🎯 屏幕距离检测配置\n    private screenDistanceThreshold: number = 80; // 屏幕距离阈值（像素）\n    private useScreenDistanceFallback: boolean = true; // 使用屏幕距离作为备选\n\n    // 🎯 物品切换控制\n    private currentBestItem: ItemSceneViewComp | null = null; // 当前最佳物品\n    private itemSwitchDelay: number = 400; // 物品切换延迟400ms\n    private lastItemSwitchTime: number = 0;\n    private candidateItems: Array<{ item: ItemSceneViewComp; score: number }> = []; // 候选物品及评分\n    private currentItemIndex: number = 0; // 当前物品索引\n\n    // 🎯 慢速移动优化\n    private slowMoveThreshold: number = 3; // 降低慢速移动阈值到3px\n    private isInSlowMoveMode: boolean = false;\n    private slowMoveStartTime: number = 0;\n    private maxSlowMoveTime: number = 5000; // 增加到5秒\n\n    // 🎯 简单性能监控\n    private raycastCount: number = 0;\n    private cacheHitCount: number = 0;\n    private lastPerformanceReport: number = 0;\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n\n        // 🧹 清理射线检测缓存\n        this.raycastCache.clear();\n        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🚀 简化射线检测 - 专注解决慢速移动问题\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 计算移动距离\n        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);\n        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        // 🎯 总是执行射线检测，获取当前位置的所有物品\n        const allItems = this.performSimpleMultiLayerRaycast(currentX, currentY);\n\n        // 更新位置记录\n        this.lastRaycastPosition.x = currentX;\n        this.lastRaycastPosition.y = currentY;\n\n        // 🎯 如果没有检测到物品，返回null\n        if (allItems.length === 0) {\n            this.exitSlowMoveMode();\n            return null;\n        }\n\n        // 🎯 如果只有一个物品，直接返回\n        if (allItems.length === 1) {\n            this.exitSlowMoveMode();\n            return allItems[0];\n        }\n\n        // 🎯 多个物品时，处理层级切换\n        return this.handleSimpleLayerSwitching(allItems, distance);\n    }\n\n    /**\n     * 🎯 简化多层射线检测 - 只使用单点射线，逐层检测\n     */\n    private performSimpleMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 简化层级切换 - 基于移动距离判断\n     */\n    private handleSimpleLayerSwitching(\n        items: ItemSceneViewComp[],\n        moveDistance: number\n    ): ItemSceneViewComp | null {\n        if (items.length <= 1) {\n            return items[0] || null;\n        }\n\n        const now = Date.now();\n        const isSlowMove = moveDistance < this.slowMoveThreshold;\n\n        // 🎯 快速移动时，总是返回第一个（顶层）物品\n        if (!isSlowMove) {\n            this.exitSlowMoveMode();\n            console.log(`🚀 快速移动，选择顶层物品: ${items[0].node?.name || '未知'}`);\n            return items[0];\n        }\n\n        // 🎯 慢速移动时，进入层级切换模式\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.currentItemIndex = 0;\n            this.candidateItems = items.map(item => ({ item, score: 100 }));\n            console.log(`🎯 慢速移动，检测到${items.length}个堆叠物品，开始层级切换`);\n        }\n\n        // 🎯 检查是否超时\n        if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return items[0]; // 返回顶层物品\n        }\n\n        // 🎯 检查是否应该切换到下一层\n        if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n            this.currentItemIndex = (this.currentItemIndex + 1) % items.length;\n            this.lastItemSwitchTime = now;\n\n            console.log(\n                `🔄 切换到第${this.currentItemIndex + 1}层: ${items[this.currentItemIndex].node?.name || '未知'}`\n            );\n        }\n\n        return items[this.currentItemIndex] || items[0];\n    }\n\n    /**\n     * 🎯 混合检测方法 - 射线检测 + 屏幕距离检测\n     */\n    private performHybridDetection(x: number, y: number): ItemSceneViewComp | null {\n        const startTime = performance.now();\n\n        // 方法1：扩展射线检测\n        const raycastItems = this.performExpandedRaycast(x, y);\n\n        // 方法2：屏幕距离检测（作为补充）\n        const screenDistanceItems = this.performScreenDistanceDetection(x, y);\n\n        // 合并结果并评分\n        const allCandidates = this.mergeAndScoreItems(raycastItems, screenDistanceItems, x, y);\n\n        // 选择最佳物品\n        const bestItem = this.selectBestItem(allCandidates);\n\n        // 性能监控\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        this.raycastCount++;\n\n        if (duration > 3) {\n            console.warn(`⚠️ 混合检测耗时: ${duration.toFixed(2)}ms`);\n        }\n\n        return bestItem;\n    }\n\n    /**\n     * 🎯 扩展射线检测 - 使用多个检测点\n     */\n    private performExpandedRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n        const items: ItemSceneViewComp[] = [];\n        const foundItems = new Set<string>();\n\n        // 检测点：中心 + 四个方向\n        const radius = this.raycastRadius;\n        const points = [\n            { x, y }, // 中心\n            { x: x - radius, y }, // 左\n            { x: x + radius, y }, // 右\n            { x, y: y - radius }, // 上\n            { x, y: y + radius }, // 下\n        ];\n\n        for (const point of points) {\n            const ray = camera.screenPointToRay(point.x, point.y);\n\n            if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                const result = PhysicsSystem.instance.raycastClosestResult;\n                const hitNode = result.collider.node;\n                const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                if (itemComp && itemComp.ItemModel && !foundItems.has(hitNode.uuid)) {\n                    items.push(itemComp);\n                    foundItems.add(hitNode.uuid);\n                }\n            }\n        }\n\n        return items;\n    }\n\n    /**\n     * 🎯 屏幕距离检测 - 基于屏幕坐标距离\n     */\n    private performScreenDistanceDetection(touchX: number, touchY: number): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        const items: ItemSceneViewComp[] = [];\n\n        // 获取场景中所有物品\n        const allItems = this.getAllSceneItems();\n\n        for (const item of allItems) {\n            if (!item.node || !item.ItemModel) continue;\n\n            // 将物品世界坐标转换为屏幕坐标\n            const worldPos = item.node.worldPosition;\n            const screenPos = camera.worldToScreen(worldPos);\n\n            // 计算屏幕距离\n            const distance = Math.sqrt(\n                Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n            );\n\n            // 如果在阈值范围内，添加到候选列表\n            if (distance <= this.screenDistanceThreshold) {\n                items.push(item);\n            }\n        }\n\n        return items;\n    }\n\n    /**\n     * 🎯 获取场景中所有物品\n     */\n    private getAllSceneItems(): ItemSceneViewComp[] {\n        const items: ItemSceneViewComp[] = [];\n\n        if (!this.gameEntity || !this.gameEntity.GameModel) {\n            return items;\n        }\n\n        // 从游戏模型中获取所有场景物品\n        const allItemsToPick = this.gameEntity.GameModel.allItemsToPick;\n        if (allItemsToPick && allItemsToPick.size > 0) {\n            allItemsToPick.forEach(itemNode => {\n                if (itemNode && itemNode.isValid) {\n                    const itemComp = itemNode.getComponent(ItemSceneViewComp);\n                    if (itemComp && itemComp.ItemModel) {\n                        items.push(itemComp);\n                    }\n                }\n            });\n        }\n\n        return items;\n    }\n\n    /**\n     * 🎯 合并和评分物品\n     */\n    private mergeAndScoreItems(\n        raycastItems: ItemSceneViewComp[],\n        screenItems: ItemSceneViewComp[],\n        touchX: number,\n        touchY: number\n    ): Array<{ item: ItemSceneViewComp; score: number }> {\n        const candidates = new Map<string, { item: ItemSceneViewComp; score: number }>();\n        const camera = smc.camera.CameraModel.camera;\n\n        // 处理射线检测结果（高优先级）\n        for (const item of raycastItems) {\n            const worldPos = item.node.worldPosition;\n            const screenPos = camera.worldToScreen(worldPos);\n            const distance = Math.sqrt(\n                Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n            );\n\n            candidates.set(item.node.uuid, {\n                item,\n                score: 100 - distance * 0.5, // 射线检测基础分100，距离越近分数越高\n            });\n        }\n\n        // 处理屏幕距离检测结果（低优先级）\n        for (const item of screenItems) {\n            if (!candidates.has(item.node.uuid)) {\n                const worldPos = item.node.worldPosition;\n                const screenPos = camera.worldToScreen(worldPos);\n                const distance = Math.sqrt(\n                    Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n                );\n\n                candidates.set(item.node.uuid, {\n                    item,\n                    score: 50 - distance * 0.3, // 屏幕距离基础分50\n                });\n            }\n        }\n\n        return Array.from(candidates.values());\n    }\n\n    /**\n     * 🎯 选择最佳物品\n     */\n    private selectBestItem(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        if (candidates.length === 0) {\n            return null;\n        }\n\n        // 按分数排序，选择最高分的物品\n        candidates.sort((a, b) => b.score - a.score);\n\n        const bestCandidate = candidates[0];\n\n        // 如果有多个候选物品，可以在这里实现慢速移动时的切换逻辑\n        if (candidates.length > 1) {\n            return this.handleMultipleCandidates(candidates);\n        }\n\n        return bestCandidate.item;\n    }\n\n    /**\n     * 🎯 处理多个候选物品\n     */\n    private handleMultipleCandidates(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        const now = Date.now();\n\n        // 检查是否为慢速移动模式\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.candidateItems = candidates;\n            this.currentItemIndex = 0;\n            console.log(`🎯 检测到${candidates.length}个候选物品，进入慢速选择模式`);\n        }\n\n        // 检查是否超时\n        if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return candidates[0].item; // 返回最高分物品\n        }\n\n        // 检查是否应该切换\n        if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n            this.currentItemIndex = (this.currentItemIndex + 1) % candidates.length;\n            this.lastItemSwitchTime = now;\n\n            const currentItem = candidates[this.currentItemIndex];\n            console.log(\n                `🔄 切换到候选物品: ${currentItem.item.node?.name || '未知'} (${this.currentItemIndex + 1}/${candidates.length})`\n            );\n        }\n\n        return candidates[this.currentItemIndex]?.item || candidates[0].item;\n    }\n\n    /**\n     * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品\n     */\n    private performMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const startTime = performance.now();\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层，平衡性能和功能\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        // 🎯 记录性能数据\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n\n        if (duration > 3) {\n            // 多层检测阈值稍高\n            console.warn(\n                `⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 退出慢速移动模式\n     */\n    private exitSlowMoveMode(): void {\n        if (this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = false;\n            this.currentItemIndex = 0;\n            this.candidateItems = [];\n            console.log('🏁 退出慢速移动模式，回到顶层');\n        }\n    }\n\n    /**\n     * 🎯 暂停物品切换 - 选择物品后暂停一段时间\n     */\n    private pauseLayerSwitching(): void {\n        this.exitSlowMoveMode();\n        this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒\n        console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);\n    }\n\n    /**\n     * 🎯 清理过期缓存\n     */\n    private cleanupCache(): void {\n        if (this.raycastCache.size > 50) {\n            const now = Date.now();\n            const keysToDelete: string[] = [];\n\n            this.raycastCache.forEach((value, key) => {\n                if (now - value.timestamp > this.raycastCacheTimeout * 2) {\n                    keysToDelete.push(key);\n                }\n            });\n\n            keysToDelete.forEach(key => this.raycastCache.delete(key));\n        }\n    }\n\n    /**\n     * 🎯 性能报告（如果需要）\n     */\n    private reportPerformanceIfNeeded(): void {\n        const now = Date.now();\n        if (now - this.lastPerformanceReport > 10000) {\n            // 每10秒报告一次\n            const cacheHitRate =\n                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;\n\n            if (this.raycastCount > 0) {\n                console.log(\n                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`\n                );\n\n                if (cacheHitRate < 30) {\n                    console.warn(\n                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`\n                    );\n                }\n            }\n\n            this.lastPerformanceReport = now;\n        }\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件\n     */\n    private onTouchStart(event: EventTouch) {\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = Date.now();\n\n        // 🎯 触摸开始时重置慢速移动模式\n        this.exitSlowMoveMode();\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到30FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率\n        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {\n            // 更新位置但不执行射线检测，减少性能消耗\n            this.lastMoveTime = currentTime;\n            this.lastMovePosition.x = currentX;\n            this.lastMovePosition.y = currentY;\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n\n                        // 🎯 选择物品后暂停层级切换\n                        this.pauseLayerSwitching();\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}