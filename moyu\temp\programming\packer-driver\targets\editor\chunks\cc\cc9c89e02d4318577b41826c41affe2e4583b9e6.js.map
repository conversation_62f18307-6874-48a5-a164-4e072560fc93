{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "PhysicsSystem", "oops", "PHY_GROUP", "smc", "ItemSceneViewComp", "GameSceneViewComp", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "raycastCache", "Map", "raycastCacheTimeout", "lastRaycastPosition", "raycastPositionThreshold", "raycastCount", "cacheHitCount", "lastPerformanceReport", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "clear", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "camera", "CameraModel", "currentX", "getLocationX", "currentY", "getLocationY", "deltaX", "Math", "abs", "deltaY", "cache<PERSON>ey", "floor", "cached", "get", "timestamp", "reportPerformanceIfNeeded", "startTime", "performance", "ray", "screenPointToRay", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "result", "instance", "raycastClosest", "raycastResult", "raycastClosestResult", "hitNode", "collider", "itemSceneViewComp", "ItemModel", "endTime", "duration", "toFixed", "set", "size", "keysToDelete", "for<PERSON>ach", "value", "key", "push", "delete", "cacheHitRate", "applyTouchEffect", "itemComp", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "hitItem", "distance", "sqrt", "name", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,a,OAAAA,a;;AACjDC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;wCAEaY,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CX,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CY,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,EAbU;AAaN;AAE5C;AAfkD,eAgB1CC,YAhB0C,GAiB9C,IAAIC,GAAJ,EAjB8C;AAAA,eAkB1CC,mBAlB0C,GAkBZ,GAlBY;AAkBP;AAlBO,eAmB1CC,mBAnB0C,GAmBM;AAAEN,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAnBN;AAAA,eAoB1CM,wBApB0C,GAoBP,CApBO;AAoBJ;AAE9C;AAtBkD,eAuB1CC,YAvB0C,GAuBnB,CAvBmB;AAAA,eAwB1CC,aAxB0C,GAwBlB,CAxBkB;AAAA,eAyB1CC,qBAzB0C,GAyBV,CAzBU;AAAA;;AA2BlDC,QAAAA,MAAM,GAAG;AACL9B,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACAlC,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACApC,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAKlC,UAAL,GAAkB+B,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,CACK,0CAAyC,KAAK7B,UAAL,GAAkB,IAAlB,GAAyB,IAAK,EAD5E;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAK4B,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR/C,UAAAA,KAAK,CAACgD,GAAN,CAAU/C,KAAK,CAAC+B,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACAlC,UAAAA,KAAK,CAACgD,GAAN,CAAU/C,KAAK,CAAC+B,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACApC,UAAAA,KAAK,CAACgD,GAAN,CAAU/C,KAAK,CAAC+B,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD,EAHQ,CAKR;;AACA,eAAKhB,YAAL,CAAkB2B,KAAlB;AACA;AAAA;AAAA,4BAAKV,GAAL,CAASC,WAAT,CAAqB,uCAArB;AACH;AAED;AACJ;AACA;;;AACYU,QAAAA,eAAe,GAAY;AAC/B,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAKtC,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYuC,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAK5C,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgB6C,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,gBAAMC,QAAQ,GAAGJ,IAAI,CAACZ,IAAtB;AACA,gBAAMa,kBAAkB,GAAG,KAAK7C,UAAL,CAAgB6C,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,kBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,gBAAMC,YAAY,GAAG,KAAKrD,UAAL,CAAgBsD,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAK5D,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH;;AAED,gBAAM6D,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,gBAAME,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAXsE,CAatE;;AACA,gBAAMC,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKjD,mBAAL,CAAyBN,CAA7C,CAAf;AACA,gBAAM8D,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKnD,mBAAL,CAAyBL,CAA7C,CAAf;;AAEA,cAAI0D,MAAM,GAAG,KAAKpD,wBAAd,IAA0CuD,MAAM,GAAG,KAAKvD,wBAA5D,EAAsF;AAClF;AACA,kBAAMwD,QAAQ,GAAI,GAAEH,IAAI,CAACI,KAAL,CAAWT,QAAQ,GAAG,KAAKhD,wBAA3B,CAAqD,IAAGqD,IAAI,CAACI,KAAL,CAAWP,QAAQ,GAAG,KAAKlD,wBAA3B,CAAqD,EAAjI;AACA,kBAAM0D,MAAM,GAAG,KAAK9D,YAAL,CAAkB+D,GAAlB,CAAsBH,QAAtB,CAAf;;AAEA,gBAAIE,MAAM,IAAIhC,IAAI,CAACC,GAAL,KAAa+B,MAAM,CAACE,SAApB,GAAgC,KAAK9D,mBAAnD,EAAwE;AACpE,mBAAKI,aAAL;AACA,mBAAK2D,yBAAL;AACA,qBAAOH,MAAM,CAAC7B,IAAd;AACH;AACJ,WA3BqE,CA6BtE;;;AACA,gBAAMiC,SAAS,GAAGC,WAAW,CAACpC,GAAZ,EAAlB;AACA,gBAAMqC,GAAG,GAAGlB,MAAM,CAACmB,gBAAP,CAAwBjB,QAAxB,EAAkCE,QAAlC,CAAZ;AACA,gBAAMgB,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,cAAIC,MAAgC,GAAG,IAAvC;;AAEA,cAAI9F,aAAa,CAAC+F,QAAd,CAAuBC,cAAvB,CAAsCR,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,kBAAMO,aAAa,GAAGjG,aAAa,CAAC+F,QAAd,CAAuBG,oBAA7C;AACA,kBAAMC,OAAO,GAAGF,aAAa,CAACG,QAAd,CAAuB3D,IAAvC;AACA,kBAAM4D,iBAAiB,GAAGF,OAAO,CAACzD,YAAR;AAAA;AAAA,uDAA1B;;AAEA,gBAAI2D,iBAAiB,IAAIA,iBAAiB,CAACC,SAA3C,EAAsD;AAClDR,cAAAA,MAAM,GAAGO,iBAAT;AACH;AACJ,WA5CqE,CA8CtE;;;AACA,gBAAME,OAAO,GAAGhB,WAAW,CAACpC,GAAZ,EAAhB;AACA,gBAAMqD,QAAQ,GAAGD,OAAO,GAAGjB,SAA3B;AACA,eAAK7D,YAAL;;AAEA,cAAI+E,QAAQ,GAAG,CAAf,EAAkB;AACd;AACAjD,YAAAA,OAAO,CAACC,IAAR,CAAc,gBAAegD,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CAAoB,IAAjD;AACH,WAtDqE,CAwDtE;;;AACA,gBAAMzB,QAAQ,GAAI,GAAEH,IAAI,CAACI,KAAL,CAAWT,QAAQ,GAAG,KAAKhD,wBAA3B,CAAqD,IAAGqD,IAAI,CAACI,KAAL,CAAWP,QAAQ,GAAG,KAAKlD,wBAA3B,CAAqD,EAAjI;AACA,eAAKJ,YAAL,CAAkBsF,GAAlB,CAAsB1B,QAAtB,EAAgC;AAAE3B,YAAAA,IAAI,EAAEyC,MAAR;AAAgBV,YAAAA,SAAS,EAAElC,IAAI,CAACC,GAAL;AAA3B,WAAhC,EA1DsE,CA4DtE;;AACA,cAAI,KAAK/B,YAAL,CAAkBuF,IAAlB,GAAyB,EAA7B,EAAiC;AAC7B,kBAAMxD,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,kBAAMyD,YAAsB,GAAG,EAA/B;AAEA,iBAAKxF,YAAL,CAAkByF,OAAlB,CAA0B,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACtC,kBAAI5D,GAAG,GAAG2D,KAAK,CAAC1B,SAAZ,GAAwB,KAAK9D,mBAAL,GAA2B,CAAvD,EAA0D;AACtDsF,gBAAAA,YAAY,CAACI,IAAb,CAAkBD,GAAlB;AACH;AACJ,aAJD;AAMAH,YAAAA,YAAY,CAACC,OAAb,CAAqBE,GAAG,IAAI,KAAK3F,YAAL,CAAkB6F,MAAlB,CAAyBF,GAAzB,CAA5B;AACH,WAxEqE,CA0EtE;;;AACA,eAAKxF,mBAAL,CAAyBN,CAAzB,GAA6BuD,QAA7B;AACA,eAAKjD,mBAAL,CAAyBL,CAAzB,GAA6BwD,QAA7B;AAEA,iBAAOoB,MAAP;AACH;AAED;AACJ;AACA;;;AACYT,QAAAA,yBAAyB,GAAS;AACtC,gBAAMlC,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;;AACA,cAAIA,GAAG,GAAG,KAAKxB,qBAAX,GAAmC,KAAvC,EAA8C;AAC1C;AACA,kBAAMuF,YAAY,GACd,KAAKzF,YAAL,GAAoB,CAApB,GAAyB,KAAKC,aAAL,GAAqB,KAAKD,YAA3B,GAA2C,GAAnE,GAAyE,CAD7E;;AAGA,gBAAI,KAAKA,YAAL,GAAoB,CAAxB,EAA2B;AACvB8B,cAAAA,OAAO,CAAClB,GAAR,CACK,oBAAmB,KAAKZ,YAAa,WAAUyF,YAAY,CAACT,OAAb,CAAqB,CAArB,CAAwB,GAD5E;;AAIA,kBAAIS,YAAY,GAAG,EAAnB,EAAuB;AACnB3D,gBAAAA,OAAO,CAACC,IAAR,CACK,eAAc0D,YAAY,CAACT,OAAb,CAAqB,CAArB,CAAwB,YAD3C;AAGH;AACJ;;AAED,iBAAK9E,qBAAL,GAA6BwB,GAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACYgE,QAAAA,gBAAgB,CAACC,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACd,SAArB,IAAkC,CAACc,QAAQ,CAACd,SAAT,CAAmBe,QAA1D,EAAoE;AAChED,YAAAA,QAAQ,CAACd,SAAT,CAAmBe,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAOD,QAAQ,CAACE,OAAhB,KAA4B,UAAhC,EAA4C;AACxCF,cAAAA,QAAQ,CAACE,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAACH,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACd,SAArB,IAAkCc,QAAQ,CAACd,SAAT,CAAmBe,QAAzD,EAAmE;AAC/DD,YAAAA,QAAQ,CAACd,SAAT,CAAmBe,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAOD,QAAQ,CAACI,aAAhB,KAAkC,UAAtC,EAAkD;AAC9CJ,cAAAA,QAAQ,CAACI,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYxF,QAAAA,YAAY,CAACqC,KAAD,EAAoB;AACpC;AACA,eAAKrD,gBAAL,CAAsBC,CAAtB,GAA0BoD,KAAK,CAACI,YAAN,EAA1B;AACA,eAAKzD,gBAAL,CAAsBE,CAAtB,GAA0BmD,KAAK,CAACM,YAAN,EAA1B;AACA,eAAK7D,YAAL,GAAoBoC,IAAI,CAACC,GAAL,EAApB,CAJoC,CAMpC;AACA;;AAEA,gBAAMsE,OAAO,GAAG,KAAKrD,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAIoD,OAAJ,EAAa;AACT,iBAAK/G,kBAAL,GAA0B+G,OAA1B;AACA,iBAAKN,gBAAL,CAAsBM,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACYvF,QAAAA,WAAW,CAACmC,KAAD,EAAoB;AACnC,gBAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,gBAAMqB,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAI1B,WAAW,GAAG,KAAKnC,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,gBAAM6D,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKxD,gBAAL,CAAsBC,CAA1C,CAAf;AACA,gBAAM8D,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAK1D,gBAAL,CAAsBE,CAA1C,CAAf;AACA,gBAAMwG,QAAQ,GAAG7C,IAAI,CAAC8C,IAAL,CAAU/C,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAI2C,QAAQ,GAAG,KAAKvG,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,cAAI,KAAKT,kBAAL,IAA2BgH,QAAQ,GAAG,KAAKvG,qBAAL,GAA6B,CAAvE,EAA0E;AACtE;AACA,iBAAKL,YAAL,GAAoBmC,WAApB;AACA,iBAAKjC,gBAAL,CAAsBC,CAAtB,GAA0BuD,QAA1B;AACA,iBAAKxD,gBAAL,CAAsBE,CAAtB,GAA0BwD,QAA1B;AACA;AACH,WA1BkC,CA4BnC;;;AACA,eAAK5D,YAAL,GAAoBmC,WAApB;AACA,eAAKjC,gBAAL,CAAsBC,CAAtB,GAA0BuD,QAA1B;AACA,eAAKxD,gBAAL,CAAsBE,CAAtB,GAA0BwD,QAA1B,CA/BmC,CAiCnC;;AACA,gBAAM+C,OAAO,GAAG,KAAKrD,oBAAL,CAA0BC,KAA1B,CAAhB,CAlCmC,CAoCnC;;AACA,cAAI,KAAK3D,kBAAL,KAA4B+G,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAK/G,kBAAT,EAA6B;AACzB,mBAAK6G,iBAAL,CAAuB,KAAK7G,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAI+G,OAAJ,EAAa;AACT,mBAAKN,gBAAL,CAAsBM,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAK/G,kBAAL,GAA0B+G,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACYrF,QAAAA,UAAU,CAACiC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAKtC,kBAAT,EAA6B;AACzB,mBAAK6G,iBAAL,CAAuB,KAAK7G,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,gBAAM+G,OAAO,GAAG,KAAKrD,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAK3D,kBAAL,IAA2B+G,OAAO,KAAK,KAAK/G,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAK6G,iBAAL,CAAuB,KAAK7G,kBAA5B;AAEA;AAAA;AAAA,8BAAK2B,GAAL,CAASC,WAAT,CACK,cAAa,+BAAK5B,kBAAL,CAAwB+B,IAAxB,2CAA8BmF,IAA9B,KAAsC,MAAO,EAD/D,EAJgE,CAQhE;;AACA,gBAAI,KAAKxE,wBAAL,CAA8B,KAAK1C,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqBuC,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAK1C,UAAL,IAAmB,KAAKA,UAAL,CAAgB6C,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAK7C,UAAL,CAAgB6C,kBAAhB,CAAmCuE,UAAnC,CAA8C,KAAKnH,kBAAL,CAAwB+B,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,CACK,gBAAe,gCAAK5B,kBAAL,CAAwB+B,IAAxB,4CAA8BmF,IAA9B,KAAsC,MAAO,EADjE;AAGH,iBALD,CAKE,OAAOE,KAAP,EAAc;AACZvE,kBAAAA,OAAO,CAACuE,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAfuD,CAiBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKlH,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aArBD,MAqBO,CACN;AACJ,WAhCD,MAgCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAK6G,iBAAL,CAAuB,KAAK7G,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WArDiC,CAuDlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AAhZiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\n\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流 - 增强版\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测\n\n    // 🎯 射线检测缓存优化\n    private raycastCache: Map<string, { item: ItemSceneViewComp | null; timestamp: number }> =\n        new Map();\n    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算\n    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private raycastPositionThreshold: number = 8; // 射线检测位置阈值\n\n    // 🎯 简单性能监控\n    private raycastCount: number = 0;\n    private cacheHitCount: number = 0;\n    private lastPerformanceReport: number = 0;\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n\n        // 🧹 清理射线检测缓存\n        this.raycastCache.clear();\n        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🚀 优化版射线检测 - 带缓存和智能跳过\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 检查位置是否变化足够大，避免无意义的重复检测\n        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);\n        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);\n\n        if (deltaX < this.raycastPositionThreshold && deltaY < this.raycastPositionThreshold) {\n            // 位置变化很小，使用缓存结果\n            const cacheKey = `${Math.floor(currentX / this.raycastPositionThreshold)}_${Math.floor(currentY / this.raycastPositionThreshold)}`;\n            const cached = this.raycastCache.get(cacheKey);\n\n            if (cached && Date.now() - cached.timestamp < this.raycastCacheTimeout) {\n                this.cacheHitCount++;\n                this.reportPerformanceIfNeeded();\n                return cached.item;\n            }\n        }\n\n        // 🎯 执行射线检测（带性能监控）\n        const startTime = performance.now();\n        const ray = camera.screenPointToRay(currentX, currentY);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        let result: ItemSceneViewComp | null = null;\n\n        if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n            const raycastResult = PhysicsSystem.instance.raycastClosestResult;\n            const hitNode = raycastResult.collider.node;\n            const itemSceneViewComp = hitNode.getComponent(ItemSceneViewComp);\n\n            if (itemSceneViewComp && itemSceneViewComp.ItemModel) {\n                result = itemSceneViewComp;\n            }\n        }\n\n        // 🎯 记录性能数据\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        this.raycastCount++;\n\n        if (duration > 2) {\n            // 超过2ms记录警告\n            console.warn(`⚠️ 射线检测耗时过长: ${duration.toFixed(2)}ms`);\n        }\n\n        // 🎯 更新缓存\n        const cacheKey = `${Math.floor(currentX / this.raycastPositionThreshold)}_${Math.floor(currentY / this.raycastPositionThreshold)}`;\n        this.raycastCache.set(cacheKey, { item: result, timestamp: Date.now() });\n\n        // 🎯 清理过期缓存（防止内存泄漏）\n        if (this.raycastCache.size > 50) {\n            const now = Date.now();\n            const keysToDelete: string[] = [];\n\n            this.raycastCache.forEach((value, key) => {\n                if (now - value.timestamp > this.raycastCacheTimeout * 2) {\n                    keysToDelete.push(key);\n                }\n            });\n\n            keysToDelete.forEach(key => this.raycastCache.delete(key));\n        }\n\n        // 更新最后检测位置\n        this.lastRaycastPosition.x = currentX;\n        this.lastRaycastPosition.y = currentY;\n\n        return result;\n    }\n\n    /**\n     * 🎯 性能报告（如果需要）\n     */\n    private reportPerformanceIfNeeded(): void {\n        const now = Date.now();\n        if (now - this.lastPerformanceReport > 10000) {\n            // 每10秒报告一次\n            const cacheHitRate =\n                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;\n\n            if (this.raycastCount > 0) {\n                console.log(\n                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`\n                );\n\n                if (cacheHitRate < 30) {\n                    console.warn(\n                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`\n                    );\n                }\n            }\n\n            this.lastPerformanceReport = now;\n        }\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件\n     */\n    private onTouchStart(event: EventTouch) {\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = Date.now();\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到30FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率\n        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {\n            // 更新位置但不执行射线检测，减少性能消耗\n            this.lastMoveTime = currentTime;\n            this.lastMovePosition.x = currentX;\n            this.lastMovePosition.y = currentY;\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}