System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Enum, ClientConst, MusicConf, _crd, PickBoxCommonIndexLenth, PickBoxCommonExtraIndexLenth, PHY_GROUP, LAYER_GROUP;

  _export({
    ClientConst: void 0,
    MusicConf: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Enum = _cc.Enum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "884a3KNT1hDbbEqlqb6Jt4t", "ClientConst", undefined);

      __checkObsolete__(['Enum']);

      _export("ClientConst", ClientConst = class ClientConst {});

      //重力相关
      ClientConst.Gravity = 30;
      //  数值越大。下落越快
      ClientConst.LinearDamping = 0.1;
      //控制物体线性速度衰减的程度。值越大，物体的速度衰减越快 如果希望物品下落过程中减速不那么明显，可以减小该值
      ClientConst.AngularDamping = 0.5;
      //如果希望物品在下落过程中旋转得更稳定或者减少旋转速度的衰减，可以减小该值
      ClientConst.pickBoxScale = 8;
      //盒子大小
      ClientConst.eazyModeItemKinds = 3;
      // 简单模式下的道具种类
      ClientConst.eazyModeItemNums = 15;
      // 简单模式下的道具数量
      ClientConst.eazyModeItemArr = [6, 6, 3];
      ClientConst.eazyModeItemArrNew = [3, 3, 3];
      ClientConst.initialRadius = 4;
      // 圆锥的底部半径 [3-6] 为佳
      ClientConst.initConeAngle = 80;
      // 圆锥的底部角度(锥形) [60~80] 为佳
      ClientConst.wallCellNum = 3;
      // wallCell的个数。个数越多。底部越近似一个圆 [3-5]为佳。
      ClientConst.hallGroundBgSize = 1.6;
      // wallCell的大小。越大。越接近一个圆 [16-20]为佳。
      ClientConst.hallGroundDistance = 16;
      // wallCell的距离。
      ClientConst.hallGroundNum = 4;
      //初始地面的个数。
      ClientConst.hallMaxRank = 50;
      // 排行榜最大排名限制
      ClientConst.increaseVertexCount = 5000;
      // 原固定顶点数（保留，若不需要可删除）
      ClientConst.increaseVertexCountPct = 0.02;
      // 新增：百分比权重（例如0.1表示10%）
      ClientConst.commonPrefabs = 'prefabs/commonPrefabs/';
      ClientConst.itemPrefabPaths = 'prefabs/game/';
      // 默认物品预制体路径
      ClientConst.defaultBgPath = 'img/bg/bg_1';
      // 默认背景图片路径
      ClientConst.dissoveCreatedDuration = 1.5;
      // 消融生成动画时长
      ClientConst.alwaysNewPlayerTest = false;

      _export("MusicConf", MusicConf = class MusicConf {});

      MusicConf.btnclick = 'boot/audios/btnclick';
      // 按钮点击音效
      MusicConf.freeze = 'common/audios/freeze';
      //  消除音效
      MusicConf.pass = 'common/audios/pass';
      //  过关音效
      MusicConf.softFail = 'common/audios/softFail';
      //  失败音效
      MusicConf.tap = 'common/audios/tap';
      //  轻触音效
      MusicConf.gameMusic1 = 'common/audios/game';
      MusicConf.gameMusic2 = 'common/audios/game_2';
      MusicConf.hallmusic = 'common/audios/hall_1';
      MusicConf.commonPickEffect = 'common/audios/pick';
      // 闲置音效
      MusicConf.commonClearEffect = 'common/audios/clear';
      // 删除音效
      MusicConf.gameFailEffect = 'common/audios/fail_1';

      _export("PickBoxCommonIndexLenth", PickBoxCommonIndexLenth = [0, 6]); // 普通格子索引


      _export("PickBoxCommonExtraIndexLenth", PickBoxCommonExtraIndexLenth = [7, 9]); // 额外格子索引
      //在PhysicsSystem 中设置 用来控制碰撞组


      _export("PHY_GROUP", PHY_GROUP = {
        DEFAULT: 1 << 0,
        WALL: 1 << 1,
        // 墙
        ITEM: 1 << 2,
        // 道具
        ITEM_BOX: 1 << 3,
        // 普通格子中的道具
        ITEM_BOX_EXTRA: 1 << 4,
        // 额外格子中的道具
        GROUND: 1 << 5 // 地面

      }); //在Layers 中设置  用来控制摄像机可见的层级


      _export("LAYER_GROUP", LAYER_GROUP = {
        DEFAULT: 1 << 30,
        RimLight: 1 << 18,
        //
        UI_3D: 1 << 23,
        //
        WALL: 1 << 19,
        //
        GROUND: 1 << 17 // 地面

      });

      Enum(PHY_GROUP);
      Enum(LAYER_GROUP);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d0df48df9a43f97a283f9cdf7c5fb2c78f915002.js.map