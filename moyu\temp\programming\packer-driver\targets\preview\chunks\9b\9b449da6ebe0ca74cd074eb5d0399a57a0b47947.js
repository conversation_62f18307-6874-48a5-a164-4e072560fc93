System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, LayerSwitchDebugger;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f1c83DKj+VEG6WD3lm9AG5x", "LayerSwitchDebugger", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 🎯 层级切换调试器
       * 用于调试和优化多层射线检测的层级切换功能
       */

      _export("LayerSwitchDebugger", LayerSwitchDebugger = (_dec = ccclass('LayerSwitchDebugger'), _dec2 = property(Label), _dec3 = property(Label), _dec(_class = (_class2 = class LayerSwitchDebugger extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "statusLabel", _descriptor, this);

          _initializerDefineProperty(this, "configLabel", _descriptor2, this);

          // 调试配置
          this.debugConfig = {
            layerSwitchDelay: 800,
            slowMoveThreshold: 5,
            maxSlowMoveTime: 3000,
            pauseAfterSelection: 1000,
            maxLayers: 3
          };
        }

        start() {
          this.updateConfigDisplay();
          this.scheduleStatusUpdate(); // 暴露调试方法到全局

          window.LayerDebugger = {
            setDelay: delay => this.setLayerSwitchDelay(delay),
            setThreshold: threshold => this.setSlowMoveThreshold(threshold),
            setMaxTime: time => this.setMaxSlowMoveTime(time),
            setPause: pause => this.setPauseAfterSelection(pause),
            setMaxLayers: layers => this.setMaxLayers(layers),
            getConfig: () => this.debugConfig,
            resetToDefault: () => this.resetToDefault(),
            showHelp: () => this.showHelp()
          };
          console.log('🎯 层级切换调试器已启动，使用 LayerDebugger 进行调试');
          this.showHelp();
        }
        /**
         * 🎯 定期更新状态显示
         */


        scheduleStatusUpdate() {
          this.schedule(() => {
            this.updateStatusDisplay();
          }, 1); // 每秒更新一次
        }
        /**
         * 🎯 更新状态显示
         */


        updateStatusDisplay() {
          if (!this.statusLabel) return;
          var status = "\u5C42\u7EA7\u5207\u6362\u72B6\u6001:\n\u5F53\u524D\u5EF6\u8FDF: " + this.debugConfig.layerSwitchDelay + "ms\n\u6162\u901F\u9608\u503C: " + this.debugConfig.slowMoveThreshold + "px\n\u6700\u5927\u65F6\u95F4: " + this.debugConfig.maxSlowMoveTime + "ms\n\u9009\u62E9\u6682\u505C: " + this.debugConfig.pauseAfterSelection + "ms\n\u6700\u5927\u5C42\u6570: " + this.debugConfig.maxLayers + "\u5C42\n\n\u4F7F\u7528\u8BF4\u660E:\n- \u6162\u6162\u79FB\u52A8\u624B\u6307 (<" + this.debugConfig.slowMoveThreshold + "px) \u89E6\u53D1\u5C42\u7EA7\u5207\u6362\n- \u6BCF " + this.debugConfig.layerSwitchDelay + "ms \u5207\u6362\u4E00\u6B21\n- \u9009\u62E9\u7269\u54C1\u540E\u6682\u505C " + this.debugConfig.pauseAfterSelection + "ms\n- \u5FEB\u901F\u79FB\u52A8\u6216\u8D85\u65F6\u81EA\u52A8\u9000\u51FA";
          this.statusLabel.string = status;
        }
        /**
         * 🎯 更新配置显示
         */


        updateConfigDisplay() {
          if (!this.configLabel) return;
          this.configLabel.string = "\u8C03\u8BD5\u547D\u4EE4:\nLayerDebugger.setDelay(800)     // \u8BBE\u7F6E\u5207\u6362\u5EF6\u8FDF\nLayerDebugger.setThreshold(5)   // \u8BBE\u7F6E\u6162\u901F\u9608\u503C\nLayerDebugger.setMaxTime(3000)  // \u8BBE\u7F6E\u6700\u5927\u65F6\u95F4\nLayerDebugger.setPause(1000)    // \u8BBE\u7F6E\u9009\u62E9\u6682\u505C\nLayerDebugger.setMaxLayers(3)   // \u8BBE\u7F6E\u6700\u5927\u5C42\u6570\nLayerDebugger.resetToDefault()  // \u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C";
        }
        /**
         * 🎯 设置层级切换延迟
         */


        setLayerSwitchDelay(delay) {
          if (delay < 100 || delay > 5000) {
            console.warn('⚠️ 延迟应在100-5000ms之间');
            return;
          }

          this.debugConfig.layerSwitchDelay = delay;
          console.log("\u2705 \u5C42\u7EA7\u5207\u6362\u5EF6\u8FDF\u5DF2\u8BBE\u7F6E\u4E3A: " + delay + "ms");
          this.updateStatusDisplay(); // 通知ItemInteractionManager更新配置

          this.notifyConfigChange();
        }
        /**
         * 🎯 设置慢速移动阈值
         */


        setSlowMoveThreshold(threshold) {
          if (threshold < 1 || threshold > 20) {
            console.warn('⚠️ 阈值应在1-20px之间');
            return;
          }

          this.debugConfig.slowMoveThreshold = threshold;
          console.log("\u2705 \u6162\u901F\u79FB\u52A8\u9608\u503C\u5DF2\u8BBE\u7F6E\u4E3A: " + threshold + "px");
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 设置最大慢速移动时间
         */


        setMaxSlowMoveTime(time) {
          if (time < 1000 || time > 10000) {
            console.warn('⚠️ 时间应在1000-10000ms之间');
            return;
          }

          this.debugConfig.maxSlowMoveTime = time;
          console.log("\u2705 \u6700\u5927\u6162\u901F\u79FB\u52A8\u65F6\u95F4\u5DF2\u8BBE\u7F6E\u4E3A: " + time + "ms");
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 设置选择后暂停时间
         */


        setPauseAfterSelection(pause) {
          if (pause < 0 || pause > 5000) {
            console.warn('⚠️ 暂停时间应在0-5000ms之间');
            return;
          }

          this.debugConfig.pauseAfterSelection = pause;
          console.log("\u2705 \u9009\u62E9\u540E\u6682\u505C\u65F6\u95F4\u5DF2\u8BBE\u7F6E\u4E3A: " + pause + "ms");
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 设置最大检测层数
         */


        setMaxLayers(layers) {
          if (layers < 1 || layers > 10) {
            console.warn('⚠️ 层数应在1-10之间');
            return;
          }

          this.debugConfig.maxLayers = layers;
          console.log("\u2705 \u6700\u5927\u68C0\u6D4B\u5C42\u6570\u5DF2\u8BBE\u7F6E\u4E3A: " + layers + "\u5C42");
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 重置为默认配置
         */


        resetToDefault() {
          this.debugConfig = {
            layerSwitchDelay: 800,
            slowMoveThreshold: 5,
            maxSlowMoveTime: 3000,
            pauseAfterSelection: 1000,
            maxLayers: 3
          };
          console.log('✅ 已重置为默认配置');
          this.updateStatusDisplay();
          this.notifyConfigChange();
        }
        /**
         * 🎯 通知配置变更
         */


        notifyConfigChange() {
          // 这里可以通知ItemInteractionManager更新配置
          // 暂时通过事件或全局变量的方式
          window.LayerSwitchConfig = this.debugConfig;
        }
        /**
         * 🎯 显示帮助信息
         */


        showHelp() {
          var help = "\n\uD83C\uDFAF \u5C42\u7EA7\u5207\u6362\u8C03\u8BD5\u5668\u4F7F\u7528\u6307\u5357:\n\n\u95EE\u9898\u8BCA\u65AD:\n1. \u5207\u6362\u592A\u5FEB \u2192 LayerDebugger.setDelay(1200) // \u589E\u52A0\u5EF6\u8FDF\n2. \u5207\u6362\u592A\u6162 \u2192 LayerDebugger.setDelay(500)  // \u51CF\u5C11\u5EF6\u8FDF\n3. \u96BE\u4EE5\u89E6\u53D1 \u2192 LayerDebugger.setThreshold(3) // \u964D\u4F4E\u9608\u503C\n4. \u5BB9\u6613\u89E6\u53D1 \u2192 LayerDebugger.setThreshold(8) // \u63D0\u9AD8\u9608\u503C\n5. \u5207\u6362\u592A\u4E45 \u2192 LayerDebugger.setMaxTime(2000) // \u51CF\u5C11\u6700\u5927\u65F6\u95F4\n\n\u63A8\u8350\u914D\u7F6E:\n- \u7CBE\u786E\u64CD\u4F5C: delay=1000, threshold=3, maxTime=2000\n- \u5FEB\u901F\u64CD\u4F5C: delay=600, threshold=7, maxTime=1500\n- \u5E73\u8861\u6A21\u5F0F: delay=800, threshold=5, maxTime=3000 (\u9ED8\u8BA4)\n\n\u5B9E\u65F6\u8C03\u8BD5:\n- \u6253\u5F00\u63A7\u5236\u53F0\u67E5\u770B\u5207\u6362\u65E5\u5FD7\n- \u89C2\u5BDF \"\uD83D\uDD04 \u5207\u6362\u5230\u7B2CX\u5C42\u7269\u54C1\" \u6D88\u606F\n- \u6CE8\u610F \"\uD83C\uDFC1 \u9000\u51FA\u6162\u901F\u79FB\u52A8\u6A21\u5F0F\" \u65F6\u673A\n";
          console.log(help);
        }
        /**
         * 🎯 获取当前性能统计
         */


        getPerformanceStats() {
          return {
            config: this.debugConfig,
            recommendations: this.getRecommendations(),
            troubleshooting: this.getTroubleshootingTips()
          };
        }
        /**
         * 🎯 获取推荐配置
         */


        getRecommendations() {
          return {
            precise: {
              delay: 1000,
              threshold: 3,
              maxTime: 2000
            },
            fast: {
              delay: 600,
              threshold: 7,
              maxTime: 1500
            },
            balanced: {
              delay: 800,
              threshold: 5,
              maxTime: 3000
            }
          };
        }
        /**
         * 🎯 获取故障排除提示
         */


        getTroubleshootingTips() {
          return ['如果切换太频繁，增加 layerSwitchDelay', '如果难以触发切换，降低 slowMoveThreshold', '如果切换时间太长，减少 maxSlowMoveTime', '如果选择后仍在切换，增加 pauseAfterSelection', '如果检测不到下层，增加 maxLayers'];
        }

        onDestroy() {
          this.unscheduleAllCallbacks();
          delete window.LayerDebugger;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "statusLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "configLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class)); // 暴露到全局，便于调试


      window.LayerSwitchDebugger = LayerSwitchDebugger;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9b449da6ebe0ca74cd074eb5d0399a57a0b47947.js.map