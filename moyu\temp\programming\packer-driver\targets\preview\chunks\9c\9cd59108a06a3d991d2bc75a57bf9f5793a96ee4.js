System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15", "__unresolved_16", "__unresolved_17", "__unresolved_18", "__unresolved_19", "__unresolved_20", "__unresolved_21", "__unresolved_22", "__unresolved_23", "__unresolved_24", "__unresolved_25", "__unresolved_26"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Collider, instantiate, MeshRenderer, Node, Prefab, RigidBody, Vec3, Vec4, oops, ecs, PropType, SceneType, smc, EventMessage, ClientConst, SceneItemType, GameEvent, simpleLoader, ItemEntity, ItemSceneViewComp, WallSceneViewComp, GameSceneViewComp, BaseSceneEntity, GameModelComp, GameUIViewComp, configManager, GameState, UnifiedGameManager, ModuleUtil, UIID, gameAudioManager, levelProgressManager, CollectionSlotManager, GameResultManager, ItemInteractionManager, ItemInteractionManagerComp, EcsSceneSystem, _dec, _class, _crd, GameEntity;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropType(extras) {
    _reporterNs.report("PropType", "../../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneType(extras) {
    _reporterNs.report("SceneType", "../../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUsePropArgs(extras) {
    _reporterNs.report("UsePropArgs", "../../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMessage(extras) {
    _reporterNs.report("EventMessage", "../../../../../extensions/oops-plugin-framework/assets/core/common/event/EventMessage", _context.meta, extras);
  }

  function _reportPossibleCrUseOfClientConst(extras) {
    _reporterNs.report("ClientConst", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneItemType(extras) {
    _reporterNs.report("SceneItemType", "../../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../../common/Enum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsimpleLoader(extras) {
    _reporterNs.report("simpleLoader", "../../common/loader/SimpleLoadingManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemEntity(extras) {
    _reporterNs.report("ItemEntity", "../../item/ItemEntity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemSceneViewComp(extras) {
    _reporterNs.report("ItemSceneViewComp", "../../item/view/ItemSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWallSceneViewComp(extras) {
    _reporterNs.report("WallSceneViewComp", "../../prefab/WallSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameSceneViewComp(extras) {
    _reporterNs.report("GameSceneViewComp", "../../sceneMgr/vIew/GameSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseSceneEntity(extras) {
    _reporterNs.report("BaseSceneEntity", "../BaseSceneEntity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameModelComp(extras) {
    _reporterNs.report("GameModelComp", "./model/GameModelComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameUIViewComp(extras) {
    _reporterNs.report("GameUIViewComp", "./view/GameUIViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfconfigManager(extras) {
    _reporterNs.report("configManager", "../../managers/ConfigManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameState(extras) {
    _reporterNs.report("GameState", "../../managers/UnifiedGameManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUnifiedGameManager(extras) {
    _reporterNs.report("UnifiedGameManager", "../../managers/UnifiedGameManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfModuleUtil(extras) {
    _reporterNs.report("ModuleUtil", "../../../../../extensions/oops-plugin-framework/assets/module/common/ModuleUtil", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIID(extras) {
    _reporterNs.report("UIID", "../../common/config/GameUIConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfgameAudioManager(extras) {
    _reporterNs.report("gameAudioManager", "../../managers/AudioManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOflevelProgressManager(extras) {
    _reporterNs.report("levelProgressManager", "../../managers/LevelProgressManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCollectionSlotManager(extras) {
    _reporterNs.report("CollectionSlotManager", "../../managers/CollectionSlotManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResultManager(extras) {
    _reporterNs.report("GameResultManager", "../../managers/GameResultManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemInteractionManager(extras) {
    _reporterNs.report("ItemInteractionManager", "../../managers/ItemInteractionManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemInteractionManagerComp(extras) {
    _reporterNs.report("ItemInteractionManagerComp", "./ItemInteractionManager", _context.meta, extras);
  }

  _export("EcsSceneSystem", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Collider = _cc.Collider;
      instantiate = _cc.instantiate;
      MeshRenderer = _cc.MeshRenderer;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      RigidBody = _cc.RigidBody;
      Vec3 = _cc.Vec3;
      Vec4 = _cc.Vec4;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      ecs = _unresolved_3.ecs;
    }, function (_unresolved_4) {
      PropType = _unresolved_4.PropType;
      SceneType = _unresolved_4.SceneType;
    }, function (_unresolved_5) {
      smc = _unresolved_5.smc;
    }, function (_unresolved_6) {
      EventMessage = _unresolved_6.EventMessage;
    }, function (_unresolved_7) {
      ClientConst = _unresolved_7.ClientConst;
    }, function (_unresolved_8) {
      SceneItemType = _unresolved_8.SceneItemType;
    }, function (_unresolved_9) {
      GameEvent = _unresolved_9.GameEvent;
    }, function (_unresolved_10) {
      simpleLoader = _unresolved_10.simpleLoader;
    }, function (_unresolved_11) {
      ItemEntity = _unresolved_11.ItemEntity;
    }, function (_unresolved_12) {
      ItemSceneViewComp = _unresolved_12.ItemSceneViewComp;
    }, function (_unresolved_13) {
      WallSceneViewComp = _unresolved_13.WallSceneViewComp;
    }, function (_unresolved_14) {
      GameSceneViewComp = _unresolved_14.GameSceneViewComp;
    }, function (_unresolved_15) {
      BaseSceneEntity = _unresolved_15.BaseSceneEntity;
    }, function (_unresolved_16) {
      GameModelComp = _unresolved_16.GameModelComp;
    }, function (_unresolved_17) {
      GameUIViewComp = _unresolved_17.GameUIViewComp;
    }, function (_unresolved_18) {
      configManager = _unresolved_18.configManager;
    }, function (_unresolved_19) {
      GameState = _unresolved_19.GameState;
      UnifiedGameManager = _unresolved_19.UnifiedGameManager;
    }, function (_unresolved_20) {
      ModuleUtil = _unresolved_20.ModuleUtil;
    }, function (_unresolved_21) {
      UIID = _unresolved_21.UIID;
    }, function (_unresolved_22) {
      gameAudioManager = _unresolved_22.gameAudioManager;
    }, function (_unresolved_23) {
      levelProgressManager = _unresolved_23.levelProgressManager;
    }, function (_unresolved_24) {
      CollectionSlotManager = _unresolved_24.CollectionSlotManager;
    }, function (_unresolved_25) {
      GameResultManager = _unresolved_25.GameResultManager;
    }, function (_unresolved_26) {
      ItemInteractionManager = _unresolved_26.ItemInteractionManager;
    }, function (_unresolved_27) {
      ItemInteractionManagerComp = _unresolved_27.ItemInteractionManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c40cd/4hk9JqYTnU/wUxuql", "GameEntity", undefined);

      __checkObsolete__(['Collider', 'instantiate', 'MeshRenderer', 'Node', 'Prefab', 'RigidBody', 'Vec3', 'Vec4']); // 🏗️ 统一管理器导入
      // 🎮 业务逻辑管理器导入
      // 🎯 导入触摸交互管理器


      /**
       * 游戏实体 - 简化版
       * <AUTHOR>
       * @version 3.0.0 - 统一管理器架构 + 消融生成效果 + 完整触摸系统
       */
      _export("GameEntity", GameEntity = (_dec = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register("GameEntity"), _dec(_class = class GameEntity extends (_crd && BaseSceneEntity === void 0 ? (_reportPossibleCrUseOfBaseSceneEntity({
        error: Error()
      }), BaseSceneEntity) : BaseSceneEntity) {
        constructor() {
          super(...arguments);
          // 🎯 核心组件
          this.GameModel = void 0;
          this.GameUIView = void 0;
          this.GameSceneView = void 0;
          this.WallSceneView = void 0;
          // 🏗️ 统一管理器
          this.gameManager = void 0;
          // 🎯 触摸交互管理器
          this.interactionManager = void 0;
          // 🎯 游戏结果管理器
          this.gameResultManager = void 0;
          // 🎭 简化版消融系统 - 只用一个标志位
          this.isDissolveAnimating = false;
          // 🎯 预加载状态管理
          this.hardModeResourcesLoaded = false;
        }

        /**
         * 🚀 实体初始化
         */
        init() {
          this.add(_crd && GameModelComp === void 0 ? (_reportPossibleCrUseOfGameModelComp({
            error: Error()
          }), GameModelComp) : GameModelComp);
          this.listenEvent(); // 延迟初始化gameManager，确保异步完成

          this.initializeGameManager(); // 🎯 初始化触摸交互管理器

          this.interactionManager = new (_crd && ItemInteractionManager === void 0 ? (_reportPossibleCrUseOfItemInteractionManager({
            error: Error()
          }), ItemInteractionManager) : ItemInteractionManager)(this); // 🎯 初始化游戏结果管理器

          this.gameResultManager = new (_crd && GameResultManager === void 0 ? (_reportPossibleCrUseOfGameResultManager({
            error: Error()
          }), GameResultManager) : GameResultManager)(this);
        }
        /**
         * 🎮 初始化统一游戏管理器
         */


        initializeGameManager() {
          var _this = this;

          return _asyncToGenerator(function* () {
            try {
              // 确保ConfigManager已初始化
              yield (_crd && configManager === void 0 ? (_reportPossibleCrUseOfconfigManager({
                error: Error()
              }), configManager) : configManager).initialize(); // 创建统一管理器

              _this.gameManager = new (_crd && UnifiedGameManager === void 0 ? (_reportPossibleCrUseOfUnifiedGameManager({
                error: Error()
              }), UnifiedGameManager) : UnifiedGameManager)(_this); // 设置状态回调

              _this.setupStateCallbacks();

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎮 统一游戏管理器初始化完成');
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 游戏管理器初始化失败:', error);
            }
          })();
        }
        /**
         * 🎯 设置状态回调
         */


        setupStateCallbacks() {
          this.gameManager.onStateChange((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
            error: Error()
          }), GameState) : GameState).SimpleMode, () => {
            this.setupSimpleMode();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
          });
          this.gameManager.onStateChange((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
            error: Error()
          }), GameState) : GameState).HardMode, () => {
            this.setupHardMode();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
          });
          this.gameManager.onStateChange((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
            error: Error()
          }), GameState) : GameState).Win, () => {
            this.handleGameWin();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
          });
          this.gameManager.onStateChange((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
            error: Error()
          }), GameState) : GameState).GameOver, () => {
            this.handleGameFail();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
          });
          this.gameManager.onStateChange((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
            error: Error()
          }), GameState) : GameState).Paused, () => {
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
          });
          this.gameManager.onStateChange((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
            error: Error()
          }), GameState) : GameState).Loading, () => {
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
          });
        }
        /**
         * 🎯 更新ViewModel中的游戏状态数据
         * 供VMState组件使用
         */


        updateGameStateInViewModel() {
          var currentState = this.gameManager.getCurrentState(); // 更新vmdata中的游戏状态

          this.GameModel.vmdata.gameState = currentState;
          this.GameModel.vmdata.isSimpleMode = this.gameManager.isSimpleMode() ? 1 : 0;
          this.GameModel.vmdata.isHardMode = this.gameManager.isHardMode() ? 1 : 0;
          this.GameModel.vmdata.isGameOver = this.gameManager.isGameOver() ? 1 : 0;
          this.GameModel.vmdata.isWin = this.gameManager.isWin() ? 1 : 0;
          this.GameModel.vmdata.isPaused = this.gameManager.isPaused() ? 1 : 0;
          this.GameModel.vmdata.isPlaying = this.gameManager.isSimpleMode() || this.gameManager.isHardMode() ? 1 : 0;
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83C\uDFAF ViewModel\u6E38\u620F\u72B6\u6001\u5DF2\u66F4\u65B0: " + currentState);
        }
        /**
         * ⚡ 设置简单模式
         */


        setupSimpleMode() {
          var levelConfig = this.gameManager.getCurrentLevelConfig();

          if (levelConfig) {
            this.gameManager.setRefillStrategy({
              adjustByPerformance: false,
              adjustByTime: false,
              minInterval: levelConfig.spawnInterval || 3000,
              maxInterval: (levelConfig.spawnInterval || 3000) * 1.5,
              difficultyMultiplier: 1.0
            });
          }

          (_crd && gameAudioManager === void 0 ? (_reportPossibleCrUseOfgameAudioManager({
            error: Error()
          }), gameAudioManager) : gameAudioManager).playSimpleGameMusic(); // 🎯 简单模式开始时，后台预加载困难模式资源

          this.startPreloadingHardModeInBackground();
        }
        /**
         * 🔥 设置困难模式
         */


        setupHardMode() {
          var levelConfig = this.gameManager.getCurrentLevelConfig();

          if (levelConfig) {
            this.gameManager.setRefillStrategy({
              adjustByPerformance: true,
              adjustByTime: true,
              minInterval: levelConfig.spawnInterval || 2000,
              maxInterval: (levelConfig.spawnInterval || 2000) * 2,
              difficultyMultiplier: (_crd && levelProgressManager === void 0 ? (_reportPossibleCrUseOflevelProgressManager({
                error: Error()
              }), levelProgressManager) : levelProgressManager).getDifficultyMultiplier((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).role.getPassIndex())
            });
          }

          (_crd && gameAudioManager === void 0 ? (_reportPossibleCrUseOfgameAudioManager({
            error: Error()
          }), gameAudioManager) : gameAudioManager).playHardGameMusic(); // 🎯 进入困难模式时完成新手引导（统一新手状态管理）

          if ((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role.isNewPlayer()) {
            var _role$RoleModel;

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎓 进入困难模式，检测到新手玩家，开始完成新手引导...');
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83D\uDD0D \u5F15\u5BFC\u524D\u72B6\u6001: isNewPlayer = " + ((_role$RoleModel = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).role.RoleModel) == null || (_role$RoleModel = _role$RoleModel.userGameData) == null ? void 0 : _role$RoleModel.isNewPlayer));
            (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).role.completeNewPlayerGuide().then(success => {
              if (success) {
                var _role$RoleModel2;

                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ 新手引导完成成功');
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\uD83D\uDD0D \u5F15\u5BFC\u540E\u72B6\u6001: isNewPlayer = " + ((_role$RoleModel2 = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                  error: Error()
                }), smc) : smc).role.RoleModel) == null || (_role$RoleModel2 = _role$RoleModel2.userGameData) == null ? void 0 : _role$RoleModel2.isNewPlayer));
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logError('❌ 新手引导完成失败');
              }
            }).catch(error => {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 完成新手引导异常:', error);
            });
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('ℹ️ 进入困难模式，玩家已完成新手引导，跳过新手状态更新');
          } // 💰 困难模式开始时扣除挑战次数并记录挑战数据


          this.costChallengeAttempt().then(success => {
            if (success) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 困难模式挑战次数扣除成功');
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('❌ 困难模式挑战次数扣除失败');
            }
          }).catch(error => {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 困难模式挑战次数扣除异常:', error);
          }); // 🎯 困难模式开始后开始预加载后续需要的资源

          this.startPreloadingEndGameResources();
        }
        /**
         * 🏆 处理游戏胜利
         */


        handleGameWin() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🏆 游戏胜利处理开始'); // 🎯 委托给GameResultManager统一处理

            yield _this2.gameResultManager.handleGameWin();
          })();
        }
        /**
         * 💥 处理游戏失败
         */


        handleGameFail() {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('💥 游戏失败处理开始'); // 🎯 委托给GameResultManager统一处理

            yield _this3.gameResultManager.handleGameFail();
          })();
        }
        /**
         * 🎯 简单模式开始时后台预加载困难模式资源
         */


        startPreloadingHardModeInBackground() {
          // 🎯 异步后台预加载，不阻塞游戏进行，不显示Loading UI
          this.ensureHardModeResourcesPreloaded(false).catch(error => {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 困难模式资源后台预加载失败:', error); // 预加载失败时不设置标记，让胜利时再次尝试
          });
        }
        /**
         * 🎯 统一的困难模式资源预加载方法
         * @param showLoading 是否显示Loading界面
         */


        ensureHardModeResourcesPreloaded(showLoading) {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            if (showLoading === void 0) {
              showLoading = false;
            }

            var levelConfig = _this4.gameManager.getCurrentLevelConfig();

            if (!(levelConfig != null && levelConfig.hardModeItems) || levelConfig.hardModeItems.length === 0) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎯 无困难模式道具需要预加载');
              _this4.hardModeResourcesLoaded = true;
              return;
            }

            if (_this4.hardModeResourcesLoaded) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎯 困难模式资源已预加载，跳过');
              return;
            }

            if (showLoading) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.open((_crd && UIID === void 0 ? (_reportPossibleCrUseOfUIID({
                error: Error()
              }), UIID) : UIID).Loading);
            }

            try {
              var uniqueItemNames = Array.from(new Set(levelConfig.hardModeItems.map(item => item.name))); // 🎯 使用配置化的物品路径

              var itemPrefabPath = _this4.getItemPrefabPath();

              var prefabPaths = uniqueItemNames.map(name => "" + itemPrefabPath + name);
              yield (_crd && simpleLoader === void 0 ? (_reportPossibleCrUseOfsimpleLoader({
                error: Error()
              }), simpleLoader) : simpleLoader).loadPrefabs(prefabPaths);
              _this4.hardModeResourcesLoaded = true;
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\u2705 \u56F0\u96BE\u6A21\u5F0F\u9053\u5177\u9884\u52A0\u8F7D\u5B8C\u6210: " + uniqueItemNames.length + " \u79CD\u9053\u5177");
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 困难模式资源预加载失败:', error);
              throw error;
            } finally {
              if (showLoading) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).gui.remove((_crd && UIID === void 0 ? (_reportPossibleCrUseOfUIID({
                  error: Error()
                }), UIID) : UIID).Loading);
              }
            }
          })();
        }
        /**
         * 🎯 开始预加载游戏结束相关资源
         * 在困难模式开始时调用，后台预加载GameResult界面和Hall资源
         */


        startPreloadingEndGameResources() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 困难模式开始，后台预加载结束游戏资源'); // 预加载GameResult界面资源

          this.preloadGameResultResources(); // 预加载Hall大厅资源

          this.preloadHallResources();
        }
        /**
         * 🎮 预加载GameResult界面资源
         */


        preloadGameResultResources() {
          return _asyncToGenerator(function* () {
            try {
              // 预加载GameResult预制体
              yield (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).res.loadAsync('prefabs/commonPrefabs/GameResult', Prefab);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ GameResult界面资源预加载完成');
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ GameResult界面资源预加载失败:', error);
            }
          })();
        }
        /**
         * 🏛️ 预加载Hall大厅资源
         */


        preloadHallResources() {
          return _asyncToGenerator(function* () {
            try {
              // 使用SimpleSceneManager的预加载方法
              var success = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).sceneMgr.preloadScene((_crd && SceneType === void 0 ? (_reportPossibleCrUseOfSceneType({
                error: Error()
              }), SceneType) : SceneType).Hall);

              if (success) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ Hall大厅资源预加载完成');
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn('⚠️ Hall大厅资源预加载失败');
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ Hall大厅资源预加载失败:', error);
            }
          })();
        }
        /**
         * 🙈 游戏隐藏事件处理
         */


        onHide() {
          var _this$gameManager;

          (_this$gameManager = this.gameManager) == null || _this$gameManager.setInputEnabled(false);
        }
        /**
         * 👂 注册事件监听
         */


        listenEvent() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).message.on((_crd && EventMessage === void 0 ? (_reportPossibleCrUseOfEventMessage({
            error: Error()
          }), EventMessage) : EventMessage).GAME_HIDE, this.onHide, this);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).message.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).UseProp, this.onUseProp, this);
        }
        /**
         * 🔇 注销事件监听
         */


        unListenEvent() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).message.off((_crd && EventMessage === void 0 ? (_reportPossibleCrUseOfEventMessage({
            error: Error()
          }), EventMessage) : EventMessage).GAME_HIDE, this.onHide, this);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).message.off((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).UseProp, this.onUseProp, this);
        }
        /**
         * 👆 选择物品 - 恢复完整逻辑
         */


        chooseItem(item) {
          var inputEnabled = this.gameManager.isInputEnabled();
          var dissolveAnimating = this.isDissolveAnimating; // 🎯 详细状态检查日志

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83C\uDFAF \u9009\u62E9\u7269\u54C1 " + item.name + ": \u8F93\u5165\u542F\u7528=" + inputEnabled + ", \u6D88\u878D\u52A8\u753B=" + dissolveAnimating);

          if (!inputEnabled) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚫 输入被禁用，无法选择物品');
            return;
          } // 检查消融动画是否完成


          if (dissolveAnimating) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎭 消融动画进行中，无法选择物品');
            return;
          } // 委托给触摸交互管理器处理


          this.interactionManager.chooseItem(item);
        }
        /**
         * 🎯 选择额外区物品
         */


        chooseExtraItem(item) {
          if (!this.gameManager.isInputEnabled()) return;
          this.interactionManager.chooseExtraItem(item);
        }
        /**
         * 💥 销毁实体
         */


        destroy() {
          var _this$gameManager2;

          console.log('🗑️ GameEntity销毁开始'); // 🧹 清理收集槽和所有物品（防止场景切换时物品残留）

          if (this.GameModel) {
            console.log('🧹 场景切换时清理收集槽和物品');
            this.GameModel.clear();
          } // 🎯 清理交互管理器


          if (this.interactionManager) {
            this.interactionManager.destroy();
          } // 🎮 清理游戏管理器


          (_this$gameManager2 = this.gameManager) == null || _this$gameManager2.destroy(); // 🎯 清理游戏结果管理器

          if (this.gameResultManager) {
            this.gameResultManager.destroy();
          } // 🔇 取消事件监听


          this.unListenEvent(); // 🧹 调用父类销毁

          super.destroy();
          console.log('✅ GameEntity销毁完成');
        }
        /**
         * 🏗️ 加载UI和场景
         */


        loadUIAndScene(index, onEssentialLoaded) {
          var _this5 = this;

          return _asyncToGenerator(function* () {
            var level = index || 1; // 默认关卡1

            try {
              var _role;

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83C\uDFAE \u5F00\u59CB\u52A0\u8F7D\u6E38\u620F\u573A\u666F\uFF0C\u5173\u5361: " + level); // 🔧 等待gameManager初始化完成

              yield _this5.waitForGameManagerReady(); // 🎓 如果是新手玩家，引导

              if ((_role = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).role) != null && _role.isNewPlayer()) {
                try {
                  yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                    error: Error()
                  }), smc) : smc).guide.load();
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('✅ 新手引导系统加载完成');
                } catch (error) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logError('❌ 引导系统加载失败:', error); // 即使引导系统加载失败，也继续游戏流程
                }
              }

              yield _this5.gameManager.loadLevel(level, onEssentialLoaded);
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError("\u274C \u5173\u5361 " + level + " \u52A0\u8F7D\u5931\u8D25:", error);
              throw error;
            }
          })();
        }
        /**
         * 🔧 等待gameManager准备就绪
         */


        waitForGameManagerReady() {
          var _this6 = this;

          return _asyncToGenerator(function* () {
            var maxWaitTime = 5000; // 最多等待5秒

            var checkInterval = 50; // 每50ms检查一次

            var waitedTime = 0;

            while (!_this6.gameManager && waitedTime < maxWaitTime) {
              yield new Promise(resolve => setTimeout(resolve, checkInterval));
              waitedTime += checkInterval;
            }

            if (!_this6.gameManager) {
              throw new Error('GameManager初始化超时');
            }
          })();
        }
        /**
         * 🚨 紧急创建地板（当WallSceneView未加载时的备用方案）
         */


        createEmergencyFloor() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logWarn('🚨 启动紧急地板创建程序...'); // 创建一个简单的地板节点

          var floorNode = new Node('EmergencyFloor');
          floorNode.parent = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).game.root;
          floorNode.setPosition(0, -10, 0);
          floorNode.setScale(100, 1, 100); // 添加刚体组件

          var rigidBody = floorNode.addComponent(RigidBody);
          rigidBody.type = RigidBody.Type.STATIC;
          rigidBody.enabled = true; // 添加碰撞体组件

          var collider = floorNode.addComponent(Collider);
          collider.enabled = true;
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('✅ 紧急地板创建完成: 位置Y=-10, 尺寸100x100');
        }
        /**
         * 🏗️ 异步加载游戏场景（wallSceneView已包含在gameSceneView中）
         */


        loadWallAndScene() {
          var _this7 = this;

          return _asyncToGenerator(function* () {
            try {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🏗️ 开始加载游戏场景组件...'); // 加载游戏场景视图（现在包含了wallSceneView子节点）

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83D\uDCE6 \u52A0\u8F7DGameSceneView: " + _this7.GameModel.gameSceneViewPrefab);
              yield (_crd && ModuleUtil === void 0 ? (_reportPossibleCrUseOfModuleUtil({
                error: Error()
              }), ModuleUtil) : ModuleUtil).addViewAsync(_this7, _crd && GameSceneViewComp === void 0 ? (_reportPossibleCrUseOfGameSceneViewComp({
                error: Error()
              }), GameSceneViewComp) : GameSceneViewComp, (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).game.root, _this7.GameModel.gameSceneViewPrefab);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ GameSceneView加载完成'); // 🔧 确保GameSceneView的ent属性正确设置

              if (_this7.GameSceneView) {
                _this7.GameSceneView.ent = _this7;
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ GameSceneView.ent属性已设置'); // 🎯 在GameSceneView节点上添加触摸交互管理器组件

                if (!_this7.GameSceneView.node.getComponent(_crd && ItemInteractionManagerComp === void 0 ? (_reportPossibleCrUseOfItemInteractionManagerComp({
                  error: Error()
                }), ItemInteractionManagerComp) : ItemInteractionManagerComp)) {
                  _this7.GameSceneView.node.addComponent(_crd && ItemInteractionManagerComp === void 0 ? (_reportPossibleCrUseOfItemInteractionManagerComp({
                    error: Error()
                  }), ItemInteractionManagerComp) : ItemInteractionManagerComp);

                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('✅ ItemInteractionManager组件已添加到GameSceneView');
                }
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logError('❌ GameSceneView组件未正确加载');
              } // 🔧 优化：从GameSceneView中获取WallSceneView子组件


              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔍 从GameSceneView中查找WallSceneView组件...');

              var wallSceneNode = _this7.GameSceneView.node.getChildByName('wallSceneView');

              if (wallSceneNode) {
                var wallSceneComp = wallSceneNode.getComponent(_crd && WallSceneViewComp === void 0 ? (_reportPossibleCrUseOfWallSceneViewComp({
                  error: Error()
                }), WallSceneViewComp) : WallSceneViewComp);

                if (wallSceneComp) {
                  // 手动注册WallSceneView组件到实体
                  _this7.WallSceneView = wallSceneComp;
                  _this7.WallSceneView.ent = _this7;
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('✅ WallSceneView组件已从GameSceneView中获取');
                } else {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logWarn('⚠️ wallSceneView节点中未找到WallSceneViewComp组件');
                }
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn('⚠️ GameSceneView中未找到wallSceneView子节点');
              }

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎉 所有游戏场景组件加载完成');
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 游戏场景组件加载失败:', error);
              throw error;
            }
          })();
        }
        /**
         * 🎬 加载场景
         */


        loadScene() {
          this.loadWallAndScene().then(() => {
            setTimeout(() => {
              this.startGame();
            }, 300);
          });
        }
        /**
         * 🗑️ 销毁物品
         */


        destroyItem(itemEntity) {
          // 🎯 安全检查：确保itemEntity和ItemModel存在
          if (!itemEntity || !itemEntity.ItemModel) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn("\u26A0\uFE0F destroyItem: itemEntity\u6216ItemModel\u4E3A\u7A7A");
            return;
          } // 🎯 在销毁前释放PickBox（双重保险）


          if (itemEntity.ItemModel.pickBox) {
            itemEntity.ItemModel.pickBox.freeItem();
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83D\uDCE4 destroyItem\u65F6\u91CA\u653EPickBox: " + itemEntity.ItemModel.itemId);
          }

          var itemId = itemEntity.ItemModel.itemId;

          if (itemId !== undefined && itemId !== null) {
            this.GameModel.allItemEntitys.delete(itemId);
          }

          itemEntity.destroy();
        }
        /**
         * 🔍 检查阈值触发
         */


        checkThreshold() {
          var _this8 = this;

          return _asyncToGenerator(function* () {
            _this8.gameManager.checkAndRefillItems();
          })();
        }
        /**
         * 🎯 在指定位置创建物品 - 简化版，适配重新设计的预制体
         */


        createItemOnPos(bornPos, itemName, index, randomRotation) {
          if (randomRotation === void 0) {
            randomRotation = true;
          }

          var levelConfig = this.gameManager.getCurrentLevelConfig();

          if (!levelConfig) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError("\u5173\u5361\u914D\u7F6E\u672A\u627E\u5230\uFF0C\u65E0\u6CD5\u521B\u5EFA\u7269\u54C1: " + itemName);
            return;
          }

          var itemConfigs = this.gameManager.isSimpleMode() ? levelConfig.easyModeItems : levelConfig.hardModeItems;
          var foundItem = itemConfigs.find(item => item.name === itemName);

          if (!foundItem) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError("\u7269\u54C1\u914D\u7F6E\u672A\u627E\u5230\uFF0C\u65E0\u6CD5\u521B\u5EFA\u7269\u54C1: " + itemName);
            return;
          } // 创建ECS实体并预先初始化


          var itemEntity = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
            error: Error()
          }), ecs) : ecs).getEntity(_crd && ItemEntity === void 0 ? (_reportPossibleCrUseOfItemEntity({
            error: Error()
          }), ItemEntity) : ItemEntity);
          itemEntity.ItemModel.itemId = index;
          itemEntity.ItemModel.itemType = (_crd && SceneItemType === void 0 ? (_reportPossibleCrUseOfSceneItemType({
            error: Error()
          }), SceneItemType) : SceneItemType).Foods;
          itemEntity.ItemModel.touching = false; // 🎯 设置缩放参数

          itemEntity.ItemModel.startScale = new Vec3(foundItem.startScale, foundItem.startScale, foundItem.startScale);
          itemEntity.ItemModel.pickScale = new Vec3(foundItem.pickScale, foundItem.pickScale, foundItem.pickScale); // 创建物品节点 - 支持从配置获取路径

          var itemPath = this.getItemPrefabPathFromConfig() + itemName;
          var prefab = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).res.get(itemPath, Prefab);

          if (prefab) {
            var itemNode = instantiate(prefab);
            itemNode.setPosition(bornPos); // 🎯 保存预制体的原始旋转方向

            itemEntity.ItemModel.startRotation = itemNode.rotation.clone(); // 然后才设置随机旋转（用于掉落效果）

            if (randomRotation) {
              var randomY = Math.random() * 360;
              itemNode.setRotationFromEuler(0, randomY, 0);
            } // 🎯 应用初始缩放到节点


            if (itemEntity.ItemModel.startScale) {
              itemNode.setScale(itemEntity.ItemModel.startScale);
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).game.root.addChild(itemNode); // 获取或添加ItemSceneViewComp组件

            var itemSceneViewComp = itemNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
              error: Error()
            }), ItemSceneViewComp) : ItemSceneViewComp);

            if (!itemSceneViewComp) {
              itemSceneViewComp = itemNode.addComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                error: Error()
              }), ItemSceneViewComp) : ItemSceneViewComp);
            } // 建立ECS关联


            itemSceneViewComp.ent = itemEntity;
            itemSceneViewComp.ItemModel = itemEntity.ItemModel; // 设置渲染器

            var itemMeshRenderer = itemNode.getComponent(MeshRenderer);

            if (itemMeshRenderer) {
              itemEntity.ItemModel.meshRenderer = itemMeshRenderer;
            } // 初始化组件


            if (typeof itemSceneViewComp.initializeComponent === 'function') {
              itemSceneViewComp.initializeComponent();
            } // 检查消融效果


            var meshRenderer = itemNode.getComponent(MeshRenderer);

            if (this.interactionManager && !this.interactionManager.sharedMaterial) {
              this.interactionManager.sharedMaterial = meshRenderer == null ? void 0 : meshRenderer.sharedMaterial;
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 消融材质已设置，等待统一启动');
            } // 添加到管理器


            this.GameModel.allItemEntitys.set(index, itemEntity);
            this.GameModel.allItemsToPick.set(index, itemNode);
          }
        }
        /**
         * 🎨 执行消融动画
         */


        runDissolveAnimation(duration) {
          var updateInterval = 1000 / 60; // 60fps

          var elapsedTime = 0;
          var updateTimer = setInterval(() => {
            elapsedTime += updateInterval / 1000;

            if (elapsedTime >= duration) {
              // 动画完成，设置最终状态
              this.interactionManager.sharedMaterial.setProperty('dissolveParams', new Vec4(1.0, 0.1, 0.0, 0.0)); // 清理定时器并完成动画

              clearInterval(updateTimer);
              this.completeDissolveAndEnableInput();
              return;
            } // 计算当前进度并应用缓动


            var progress = elapsedTime / duration;
            var easedProgress = this.easeInOutCubic(progress); // 更新共享材质的dissolveParams

            this.interactionManager.sharedMaterial.setProperty('dissolveParams', new Vec4(easedProgress, 0.1, 0.0, 0.0));
          }, updateInterval);
        }
        /**
         * 🎨 三次方缓动函数
         */


        easeInOutCubic(t) {
          return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }
        /**
         * 🎉 完成消融动画并启用输入 - 统一入口
         */


        completeDissolveAndEnableInput() {
          this.isDissolveAnimating = false;
          this.gameManager.setInputEnabled(true);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎉 消融动画完成！游戏正式开始，可以触摸选择物品了！');
        }
        /**
         * 🎯 统一处理输入启用 - 检查是否需要消融动画
         */


        handleInputEnabling() {
          var _this$interactionMana;

          // 🔍 检查是否有共享材质（通过材质属性判断是否需要消融动画）
          if ((_this$interactionMana = this.interactionManager) != null && _this$interactionMana.sharedMaterial) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎬 检测到消融材质，启动消融动画');
            this.startDissolveAnimation();
          } else {
            // 🎯 没有消融动画，延迟一下直接启用输入（
            setTimeout(() => {
              this.completeDissolveAndEnableInput();
            }, 0.5 * 1000);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎯 无消融材质，将延迟启用输入');
          }
        }
        /**
         * 🎬 启动消融动画 - 统一入口
         */


        startDissolveAnimation() {
          var dissolveStartDelay = 0.2; // 延迟0.2秒开始消融

          var dissolveDuration = (_crd && ClientConst === void 0 ? (_reportPossibleCrUseOfClientConst({
            error: Error()
          }), ClientConst) : ClientConst).dissoveCreatedDuration || 1.5; // 消融总时长
          // 设置标志位

          this.isDissolveAnimating = true; // 设置初始消融参数：完全透明

          this.interactionManager.sharedMaterial.setProperty('dissolveParams', new Vec4(0, 0.1, 0.0, 0.0)); // 延迟启动消融动画

          setTimeout(() => {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83C\uDFAC \u5F00\u59CB\u6D88\u878D\u52A8\u753B\uFF0C\u6301\u7EED\u65F6\u95F4: " + dissolveDuration + "\u79D2");
            this.runDissolveAnimation(dissolveDuration);
          }, dissolveStartDelay * 1000);
        }
        /**
         * 🎮 加载游戏UI
         */


        loadGameUI() {
          var uic = {
            onAdded: (node, params) => {
              // 🔗 绑定UI组件到实体
              var comp = node.getComponent(_crd && GameUIViewComp === void 0 ? (_reportPossibleCrUseOfGameUIViewComp({
                error: Error()
              }), GameUIViewComp) : GameUIViewComp);
              this.add(comp);
            }
          };
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.open(this.GameModel.gameUIID, null, uic);
        }
        /**
         * 🎮 加载游戏UI并执行回调
         */


        loadGameUIWithCallback(onUIReady) {
          var uic = {
            onAdded: (node, params) => {
              // 🔗 绑定UI组件到实体
              var comp = node.getComponent(_crd && GameUIViewComp === void 0 ? (_reportPossibleCrUseOfGameUIViewComp({
                error: Error()
              }), GameUIViewComp) : GameUIViewComp);
              this.add(comp); // 执行回调

              onUIReady();
            }
          };
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.open(this.GameModel.gameUIID, null, uic);
        } // =================== 公共API ===================


        getCurrentGameState() {
          return this.gameManager.getCurrentState();
        }

        isSimpleMode() {
          return this.gameManager.isSimpleMode();
        }

        isHardMode() {
          return this.gameManager.isHardMode();
        }
        /**
         * 🎯 判断之前是否为困难模式（用于Win/GameOver状态时的模式判断）
         */


        wasPreviouslyHardMode() {
          return this.gameManager.wasPreviouslyHardMode();
        }
        /**
         * 🎯 判断之前是否为简单模式（用于Win/GameOver状态时的模式判断）
         */


        wasPreviouslySimpleMode() {
          return this.gameManager.wasPreviouslySimpleMode();
        }
        /**
         * 🎯 获取当前关卡索引（支持测试关卡）
         */


        getCurrentLevelIndex() {
          var _this$gameManager3;

          return ((_this$gameManager3 = this.gameManager) == null ? void 0 : _this$gameManager3.getCurrentLevelIndex()) || 1;
        }
        /**
         * 🎯 获取当前关卡的物品预制体路径（公共方法）
         */


        getItemPrefabPath() {
          return this.getItemPrefabPathFromConfig();
        }
        /**
         * 🎯 获取当前关卡的物品预制体路径
         */


        getItemPrefabPathFromConfig() {
          var levelConfig = this.gameManager.getCurrentLevelConfig();

          if (!levelConfig) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn("\u26A0\uFE0F \u5173\u5361\u914D\u7F6E\u4E0D\u5B58\u5728\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u7269\u54C1\u8DEF\u5F84");
            return (_crd && ClientConst === void 0 ? (_reportPossibleCrUseOfClientConst({
              error: Error()
            }), ClientConst) : ClientConst).itemPrefabPaths; // 兜底默认路径
          }

          var configuredPath = levelConfig.itemPrefabPaths; // 确保路径以斜杠结尾

          var path = configuredPath.endsWith('/') ? configuredPath : configuredPath + '/';
          return path;
        }

        checkAndRefillItems(touchedItem) {
          this.gameManager.checkAndRefillItems(touchedItem);
        }
        /**
         * 🎯 检查输入是否启用（供引导系统使用）
         */


        isInputEnabled() {
          var _this$gameManager$isI, _this$gameManager4;

          return (_this$gameManager$isI = (_this$gameManager4 = this.gameManager) == null ? void 0 : _this$gameManager4.isInputEnabled()) != null ? _this$gameManager$isI : false;
        }

        doGameFail() {
          var _this9 = this;

          return _asyncToGenerator(function* () {
            _this9.gameManager.changeState((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
              error: Error()
            }), GameState) : GameState).GameOver);
          })();
        }

        doGameWin() {
          var _this10 = this;

          return _asyncToGenerator(function* () {
            _this10.gameManager.changeState((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
              error: Error()
            }), GameState) : GameState).Win);
          })();
        }

        beforeStartGame() {
          var _this$WallSceneView, _this$GameSceneView, _this$GameUIView;

          // 🔧 调试：检查所有组件的加载状态
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔍 检查游戏组件加载状态:');
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("- WallSceneView: " + (this.WallSceneView ? '✅ 已加载' : '❌ 未加载'));
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("- GameSceneView: " + (this.GameSceneView ? '✅ 已加载' : '❌ 未加载'));
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("- GameUIView: " + (this.GameUIView ? '✅ 已加载' : '❌ 未加载')); // 🔧 强制调用墙体创建，即使组件未正确加载

          if ((_this$WallSceneView = this.WallSceneView) != null && _this$WallSceneView.beforeStartGame) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83C\uDFD7\uFE0F \u8C03\u7528WallSceneView.beforeStartGame()");
            this.WallSceneView.beforeStartGame();
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ WallSceneView组件未加载或beforeStartGame方法不存在'); // 🔧 尝试手动创建基础地板

            this.createEmergencyFloor();
          }

          if ((_this$GameSceneView = this.GameSceneView) != null && _this$GameSceneView.beforeStartGame) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎮 调用GameSceneView.beforeStartGame()');
            this.GameSceneView.beforeStartGame(); // 🎯 直接初始化收集槽管理器

            if (this.interactionManager) {
              this.interactionManager.slotManager = new (_crd && CollectionSlotManager === void 0 ? (_reportPossibleCrUseOfCollectionSlotManager({
                error: Error()
              }), CollectionSlotManager) : CollectionSlotManager)(this.GameSceneView);
            }
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ GameSceneView组件未加载');
          }

          if ((_this$GameUIView = this.GameUIView) != null && _this$GameUIView.beforeStartGame) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🖥️ 调用GameUIView.beforeStartGame()');
            this.GameUIView.beforeStartGame();
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ GameUIView组件未加载');
          }

          this.restartFromSimpleMode();
        }
        /**
         * 🔄 从简单模式重新开始游戏
         */


        restartFromSimpleMode() {
          // 🎯 强制设置为简单模式（状态回调会自动调用setupSimpleMode）
          this.gameManager.changeState((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
            error: Error()
          }), GameState) : GameState).SimpleMode); // 🎮 开始游戏

          this.startGame();
        }

        startGame() {
          var _this11 = this;

          return _asyncToGenerator(function* () {
            // 🧹 统一清理游戏状态（避免重复清理）
            _this11.GameModel.clear(); // 🧹 清理其他状态


            if (_this11.interactionManager) {
              _this11.interactionManager.sharedMaterial = null;
            }

            _this11.isDissolveAnimating = false;
            _this11.hardModeResourcesLoaded = false; // 🎯 根据当前游戏状态初始化对应的道具（状态由外部设置，不在这里改变）

            if (_this11.isSimpleMode()) {
              _this11.gameManager.initializeEasyModeItems();
            } else if (_this11.isHardMode()) {
              _this11.gameManager.initializeHardModeItems();
            } // 🎯 统一创建道具（UnifiedGameManager会根据模式和新手状态选择策略）


            _this11.gameManager.createInitialItemsInScene(); // 🎯 初始时禁用输入，然后统一处理输入启用


            _this11.gameManager.setInputEnabled(false); // 🎯 统一处理输入启用：检查是否需要消融动画


            _this11.handleInputEnabling();
          })();
        }
        /**
         * 🎯 检查游戏结果
         */


        checkResult() {
          // 🔍 输出当前状态调试信息
          var collectItemsCount = this.GameModel.collectItems.length;
          var allItemsToPickCount = this.GameModel.allItemsToPick.size;
          var gameMode = this.isSimpleMode() ? '简单模式' : '困难模式';
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83D\uDD0D \u68C0\u67E5\u6E38\u620F\u7ED3\u679C [" + gameMode + "]: \u6536\u96C6\u69FD=" + collectItemsCount + ", \u573A\u666F\u7269\u54C1=" + allItemsToPickCount); // 检查是否所有物品都被收集

          if (collectItemsCount === 0 && allItemsToPickCount === 0) {
            // 游戏胜利
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83C\uDF89 " + gameMode + "\u80DC\u5229\uFF01\u6240\u6709\u7269\u54C1\u5DF2\u6536\u96C6");
            this.gameManager.changeState((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
              error: Error()
            }), GameState) : GameState).Win);
            return;
          } // 检查是否收集区已满 - 但要考虑三消可能性


          if (collectItemsCount >= 7) {
            // 🔍 检查是否有三消的可能性
            if (this.hasPossibleMerge()) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎯 收集区已满，但检测到可能的三消，等待三消完成...'); // 有三消可能性，不立即结束游戏，等待三消逻辑处理

              return;
            } // 没有三消可能性，游戏失败


            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('💥 游戏失败！收集区已满且无法三消');
            this.gameManager.changeState((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
              error: Error()
            }), GameState) : GameState).GameOver);
          }
        }
        /**
         * 🔍 检查是否有可能的三消
         */


        hasPossibleMerge() {
          var _this$interactionMana2;

          if (!((_this$interactionMana2 = this.interactionManager) != null && _this$interactionMana2.slotManager)) {
            return false;
          } // 统计每种物品的数量


          var itemCounts = {};

          for (var item of this.GameModel.collectItems) {
            var name = item.name;
            itemCounts[name] = (itemCounts[name] || 0) + 1;
          } // 检查是否有任何物品数量>=3


          for (var count of Object.values(itemCounts)) {
            if (count >= 3) {
              return true;
            }
          }

          return false;
        }

        onUseProp(_, args) {
          console.log('使用道具:', args);
        }

        handleUseProp(_, args) {
          console.log('使用道具:', args);
        }

        tryUseProp(usePropArgs, toastMsg) {
          if (toastMsg === void 0) {
            toastMsg = true;
          }

          return true;
        }
        /**
         * 🔄 处理简单模式到困难模式的切换
         * 由GameResultManager调用，处理模式切换的完整流程
         */


        handleSimpleModeToHardMode() {
          var _this12 = this;

          return _asyncToGenerator(function* () {
            // 🎯 禁用输入，防止玩家操作
            _this12.gameManager.setInputEnabled(false); // 🧹 确保Guide系统被完全清理


            _this12.ensureGuideSystemCleanup(); // 🎬 确保困难模式资源已预加载（显示Loading UI如果需要）


            yield _this12.ensureHardModeResourcesPreloaded(true); // 🧹 统一清理游戏状态（避免重复清理）

            _this12.GameModel.clear(); // 🧹 清理其他状态


            if (_this12.interactionManager) {
              _this12.interactionManager.sharedMaterial = null;
            }

            _this12.isDissolveAnimating = false; // 🔄 切换到困难模式（状态回调会自动调用setupHardMode）

            _this12.gameManager.changeState((_crd && GameState === void 0 ? (_reportPossibleCrUseOfGameState({
              error: Error()
            }), GameState) : GameState).HardMode); // 🎯 初始化困难模式道具数组


            _this12.gameManager.initializeHardModeItems(); // 🎯 创建困难模式道具


            _this12.gameManager.createInitialItemsInScene(); // 🎯 处理输入启用


            _this12.handleInputEnabling();

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 困难模式切换完成，重复游戏流程开始');
          })();
        }
        /**
         * 🧹 确保Guide系统被完全清理
         */


        ensureGuideSystemCleanup() {
          try {
            // 🎯 清理Guide系统
            if ((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).guide && (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).guide.GuideModel) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🧹 清理Guide系统');
              (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).guide.GuideModel.reset();
            } // 🎯 补充清理GuideView3DItemComp（Guide系统没有清理的部分）


            if (this.GameSceneView && this.GameSceneView.node) {
              var guideItemComponents = this.GameSceneView.node.getComponentsInChildren('GuideView3DItem');

              for (var comp of guideItemComponents) {
                if (comp && comp.node && comp.node.isValid) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness("\uD83E\uDDF9 \u6E05\u74063D\u5F15\u5BFC\u7EC4\u4EF6: " + comp.node.name);
                  comp.node.destroy();
                }
              }
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ Guide系统清理完成');
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 清理Guide系统时出错:', error);
          }
        }
        /**
         * 💰 扣除挑战次数并记录挑战数据
         * @returns Promise<boolean> 是否成功扣除
         */


        costChallengeAttempt() {
          return _asyncToGenerator(function* () {
            try {
              // 🔄 使用Role的updateProp方法扣除挑战次数（会自动调用服务端API记录数据）
              var success = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).role.updateProp((_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
                error: Error()
              }), PropType) : PropType).PropsDayLeftCount, -1, 'start_hard_mode');

              if (success) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ 困难模式挑战次数已扣除，挑战数据已记录');
                return true;
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('❌ 挑战次数扣除失败');
                return false;
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 扣除挑战次数异常:', error);
              return false;
            }
          })();
        }

      }) || _class));

      _export("EcsSceneSystem", EcsSceneSystem = class EcsSceneSystem extends (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).System {
        constructor() {
          super();
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9cd59108a06a3d991d2bc75a57bf9f5793a96ee4.js.map