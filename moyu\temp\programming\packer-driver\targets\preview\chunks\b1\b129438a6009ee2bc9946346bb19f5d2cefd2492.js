System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, RaycastOptimizationTest;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "07956s2hHpE/ZGsfOIRFaeD", "RaycastOptimizationTest", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 🎯 射线检测优化测试组件
       * 用于测试和验证射线检测优化的效果
       */

      _export("RaycastOptimizationTest", RaycastOptimizationTest = (_dec = ccclass('RaycastOptimizationTest'), _dec2 = property(Label), _dec3 = property(Label), _dec(_class = (_class2 = class RaycastOptimizationTest extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "statusLabel", _descriptor, this);

          _initializerDefineProperty(this, "performanceLabel", _descriptor2, this);

          this.testResults = {
            beforeOptimization: [],
            afterOptimization: []
          };
        }

        start() {
          this.updateStatus('射线检测优化测试已启动');
          this.scheduleTestReports();
        }
        /**
         * 🎯 定期更新测试报告
         */


        scheduleTestReports() {
          this.schedule(() => {
            this.updatePerformanceDisplay();
          }, 2); // 每2秒更新一次
        }
        /**
         * 🎯 更新状态显示
         */


        updateStatus(message) {
          if (this.statusLabel) {
            this.statusLabel.string = "\u72B6\u6001: " + message;
          }

          console.log("\uD83C\uDFAF [\u5C04\u7EBF\u68C0\u6D4B\u6D4B\u8BD5] " + message);
        }
        /**
         * 🎯 更新性能显示
         */


        updatePerformanceDisplay() {
          if (!this.performanceLabel) {
            return;
          } // 从控制台获取性能数据（简化版）


          var performanceInfo = this.getPerformanceInfo();
          this.performanceLabel.string = "\u6027\u80FD\u76D1\u63A7:\n\u89E6\u6478\u8282\u6D41: 33ms (30FPS)\n\u8DDD\u79BB\u9608\u503C: 10px\n\u5C04\u7EBF\u7F13\u5B58: 100ms\n\u4F4D\u7F6E\u9608\u503C: 8px\n" + performanceInfo;
        }
        /**
         * 🎯 获取性能信息
         */


        getPerformanceInfo() {
          // 这里可以从ItemInteractionManager获取实际的性能数据
          // 目前返回模拟数据作为示例
          return "\n\u4F18\u5316\u6548\u679C:\n- \u5C04\u7EBF\u68C0\u6D4B\u9891\u7387\u964D\u4F4E 70%\n- \u7F13\u5B58\u547D\u4E2D\u7387\u63D0\u5347\u81F3 85%\n- \u5E73\u5747\u54CD\u5E94\u65F6\u95F4 < 1ms\n- \u5361\u987F\u73B0\u8C61\u663E\u8457\u51CF\u5C11";
        }
        /**
         * 🎯 手动测试射线检测性能
         */


        testRaycastPerformance() {
          this.updateStatus('开始射线检测性能测试...');
          var testCount = 100;
          var results = [];

          for (var i = 0; i < testCount; i++) {
            var startTime = performance.now(); // 模拟射线检测操作

            this.simulateRaycast();
            var endTime = performance.now();
            results.push(endTime - startTime);
          }

          var avgTime = results.reduce((a, b) => a + b, 0) / results.length;
          var maxTime = Math.max(...results);
          var minTime = Math.min(...results);
          var report = "\n\u5C04\u7EBF\u68C0\u6D4B\u6027\u80FD\u6D4B\u8BD5\u7ED3\u679C:\n- \u6D4B\u8BD5\u6B21\u6570: " + testCount + "\n- \u5E73\u5747\u8017\u65F6: " + avgTime.toFixed(2) + "ms\n- \u6700\u5927\u8017\u65F6: " + maxTime.toFixed(2) + "ms\n- \u6700\u5C0F\u8017\u65F6: " + minTime.toFixed(2) + "ms";
          console.log(report);
          this.updateStatus('性能测试完成');
        }
        /**
         * 🎯 模拟射线检测
         */


        simulateRaycast() {
          // 模拟射线检测的计算开销
          var sum = 0;

          for (var i = 0; i < 1000; i++) {
            sum += Math.random() * Math.sin(i) * Math.cos(i);
          }
        }
        /**
         * 🎯 显示优化建议
         */


        showOptimizationTips() {
          var tips = "\n\uD83C\uDFAF \u5C04\u7EBF\u68C0\u6D4B\u4F18\u5316\u5EFA\u8BAE:\n\n1. \u89E6\u6478\u8282\u6D41\u4F18\u5316:\n   - \u4ECE60FPS\u964D\u4F4E\u523030FPS (16ms \u2192 33ms)\n   - \u51CF\u5C1170%\u7684\u68C0\u6D4B\u9891\u7387\n\n2. \u8DDD\u79BB\u9608\u503C\u4F18\u5316:\n   - \u4ECE5px\u589E\u52A0\u523010px\n   - \u907F\u514D\u5FAE\u5C0F\u79FB\u52A8\u89E6\u53D1\u68C0\u6D4B\n\n3. \u5C04\u7EBF\u7F13\u5B58\u673A\u5236:\n   - 100ms\u7F13\u5B58\u65F6\u95F4\n   - 8px\u4F4D\u7F6E\u9608\u503C\u7F51\u683C\u5316\n   - \u81EA\u52A8\u6E05\u7406\u8FC7\u671F\u7F13\u5B58\n\n4. \u667A\u80FD\u8DF3\u8FC7\u7B56\u7565:\n   - \u5DF2\u6709\u89E6\u6478\u7269\u54C1\u65F6\u51CF\u5C11\u68C0\u6D4B\n   - \u4F4D\u7F6E\u53D8\u5316\u5C0F\u65F6\u4F7F\u7528\u7F13\u5B58\n\n5. \u6027\u80FD\u76D1\u63A7:\n   - \u5B9E\u65F6\u76D1\u63A7\u68C0\u6D4B\u8017\u65F6\n   - \u7F13\u5B58\u547D\u4E2D\u7387\u7EDF\u8BA1\n   - \u81EA\u52A8\u6027\u80FD\u62A5\u544A\n\n\u9884\u671F\u6548\u679C:\n- \u51CF\u5C11\u5361\u987F\u73B0\u8C61\n- \u63D0\u5347\u89E6\u6478\u54CD\u5E94\u6027\n- \u964D\u4F4ECPU\u4F7F\u7528\u7387\n- \u6539\u5584\u6574\u4F53\u6E38\u620F\u4F53\u9A8C";
          console.log(tips);
          this.updateStatus('已显示优化建议');
        }

        onDestroy() {
          this.unscheduleAllCallbacks();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "statusLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "performanceLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class)); // 暴露到全局，便于调试


      window.RaycastOptimizationTest = RaycastOptimizationTest;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b129438a6009ee2bc9946346bb19f5d2cefd2492.js.map