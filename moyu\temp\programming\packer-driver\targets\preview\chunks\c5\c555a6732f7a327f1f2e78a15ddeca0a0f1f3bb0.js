System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, Component, instantiate, Material, MeshRenderer, Prefab, Vec4, _dec, _class, _crd, ccclass, property, testComp;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Material = _cc.Material;
      MeshRenderer = _cc.MeshRenderer;
      Prefab = _cc.Prefab;
      Vec4 = _cc.Vec4;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c9fa7SRcolAo45K/fmQMjla", "testComp", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'Component', 'instantiate', 'Material', 'MeshRenderer', 'Node', 'Prefab', 'Vec4']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("testComp", testComp = (_dec = ccclass('testComp'), _dec(_class = class testComp extends Component {
        constructor() {
          super(...arguments);
          // 共享材质
          this.sharedMaterial = null;
          // 呼吸消融
          this.dissolveEnable = false;
          // 发光材质
          this.rimLightXrayPbrMaterial = null;
          // 时间累计
          this.time = 0;
          // 实例化的节点数组
          this.instantiatedNodes = [];
          // 当前发光的节点
          this.currentGlowNode = null;
          // 当前发光强度
          this.currentRimStrength = 0;
        }

        onLoad() {
          assetManager.loadBundle('bundleOne', (err, bundle) => {
            if (err) {
              console.error('❌ 加载BundleOne bundle失败:', err);
              return;
            }

            bundle.load('common/materials/rimLightXrayPbrMaterial', Material, (err, material) => {
              if (err) {
                console.error('❌ 加载发光材质失败:', err);
                return;
              }

              console.log('✅ 发光材质加载完成:', material);
              this.rimLightXrayPbrMaterial = material;
            });
            var prefabs = ['prefabs/game/半个茄子', 'prefabs/game/南瓜', 'prefabs/game/土豆', 'prefabs/game/大蒜', 'prefabs/game/洋葱半个', 'prefabs/game/玉米', 'prefabs/game/甜菜', 'prefabs/game/生姜', 'prefabs/game/番茄', 'prefabs/game/红卷心菜', 'prefabs/game/红椒', 'prefabs/game/红薯', 'prefabs/game/绿卷心菜', 'prefabs/game/胡桃南瓜', 'prefabs/game/胡萝卜', 'prefabs/game/芦笋', 'prefabs/game/花椰菜', 'prefabs/game/青椒', 'prefabs/game/韭葱', 'prefabs/game/黄椒', 'prefabs/game/黄瓜', 'prefabs/game/黄西葫芦'];
            prefabs.forEach(name => {
              bundle.load(name, Prefab, (err, prefab) => {
                if (err) {
                  console.error('❌ 加载预制体失败:', err);
                  return;
                }

                console.log('✅ 预制体加载完成:', name);

                for (var i = 1; i <= 6; i++) {
                  var node = instantiate(prefab);
                  node.setParent(this.node); // 保存实例化的节点

                  this.instantiatedNodes.push(node);
                  var meshRenderer = node.getComponent(MeshRenderer);

                  if (!this.sharedMaterial) {
                    this.sharedMaterial = meshRenderer.sharedMaterial;

                    if (this.sharedMaterial && this.sharedMaterial.effectAsset.name.includes('dissolve')) {
                      this.dissolveEnable = true;
                    }
                  }
                }
              });
            });
          });
        }

        start() {}

        update(deltaTime) {
          if (this.dissolveEnable && this.sharedMaterial && this.instantiatedNodes.length > 0) {
            this.time += deltaTime; // 9秒循环：0~2秒生成，2~7秒发光，7~9秒消失

            var cycleTime = this.time % 9;
            var dissolveValue;

            if (cycleTime <= 2) {
              // 0~2秒：消融生成 (从不可见到可见，dissolveValue: 0→1)
              dissolveValue = cycleTime / 2; // 确保没有发光节点

              if (this.currentGlowNode) {
                this.switchMaterial(this.currentGlowNode, false);
                this.currentGlowNode = null;
              }
            } else if (cycleTime <= 7) {
              // 2~7秒：完全显示状态，随机一个模型发光（5秒发光时间）
              dissolveValue = 1; // 发光时间计算（从第2秒开始的时间）

              var glowTime = cycleTime - 2; // 0~5秒
              // 在2秒时刻随机选择一个节点发光

              if (!this.currentGlowNode) {
                var randomIndex = Math.floor(Math.random() * this.instantiatedNodes.length); // const randomIndex = 1; // 选择第2个节点进行测试

                this.currentGlowNode = this.instantiatedNodes[randomIndex];
                console.log("\u2728 \u9009\u62E9\u7B2C" + randomIndex + "\u4E2A\u6A21\u578B\u53D1\u5149\uFF0C\u5F00\u59CB\u547C\u5438\u706F\u6548\u679C");
                this.switchMaterial(this.currentGlowNode, true);
              }
            } else {
              // 7~9秒：切换回原材质，消融消失 (从可见到不可见，dissolveValue: 1→0)
              dissolveValue = 1 - (cycleTime - 7) / 2; // 切换回原材质

              if (this.currentGlowNode) {
                this.switchMaterial(this.currentGlowNode, false);
                this.currentGlowNode = null;
              }
            } // 设置共享材质的消融参数（影响所有使用该材质的模型）


            this.sharedMaterial.setProperty('dissolveParams', new Vec4(dissolveValue, 0.1, 0.0, 0.0));
          }
        } // 🎨 安全的材质属性复制方法 - 确保不破坏GPU Instancing合批


        copyMaterialProperties(fromMaterial, toMaterial) {
          try {
            // ✅ 复制shader支持的纹理属性
            var normalMap = fromMaterial.getProperty('normalMap');
            var albedoMap = fromMaterial.getProperty('albedoMap');
            var pbrMap = fromMaterial.getProperty('pbrMap'); // 🎯 设置纹理属性

            if (normalMap) toMaterial.setProperty('normalMap', normalMap);
            if (albedoMap) toMaterial.setProperty('albedoMap', albedoMap);
            if (pbrMap) toMaterial.setProperty('pbrMap', pbrMap);
          } catch (error) {
            console.warn('⚠️ 材质属性复制失败:', error);
          }
        }

        switchMaterial(node, toRimLight) {
          var meshRenderer = node.getComponent(MeshRenderer);

          if (toRimLight) {
            if (this.sharedMaterial && this.rimLightXrayPbrMaterial) {
              this.copyMaterialProperties(this.sharedMaterial, this.rimLightXrayPbrMaterial);
              meshRenderer.setSharedMaterial(this.rimLightXrayPbrMaterial, 0); // 🌟 设置基础rim light强度（会被呼吸效果调制）

              meshRenderer.setInstancedAttribute('a_instanced_rimStrength', [2.0]);
              console.log("\uD83C\uDF1F \u5207\u6362\u5230\u53D1\u5149\u6750\u8D28\uFF0C\u5F00\u542F\u547C\u5438\u706F\u6548\u679C");
              console.log("\uD83C\uDF1F \u5F53\u524D\u6750\u8D28\uFF1A", meshRenderer.sharedMaterial.name);
              console.log("\uD83C\uDF1F \u5F53\u524D\u6750\u8D28effect\uFF1A", meshRenderer.sharedMaterial.effectAsset.name);
            }
          } else {
            if (this.sharedMaterial) {
              meshRenderer.setSharedMaterial(this.sharedMaterial, 0);
              console.log("\uD83D\uDD04 \u5207\u6362\u56DE\u539F\u59CB\u6750\u8D28");
            }
          }
        } // 更新发光强度 - 已不需要，呼吸灯效果由shader自动处理
        // private updateRimStrength(node: Node) {
        //     // 功能已迁移到shader中的呼吸灯计算
        // }


      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c555a6732f7a327f1f2e78a15ddeca0a0f1f3bb0.js.map