System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, input, Input, PhysicsSystem, oops, PHY_GROUP, smc, ItemSceneViewComp, GameSceneViewComp, _dec, _class, _crd, ccclass, property, ItemInteractionManager;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPHY_GROUP(extras) {
    _reporterNs.report("PHY_GROUP", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemSceneViewComp(extras) {
    _reporterNs.report("ItemSceneViewComp", "../../item/view/ItemSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameSceneViewComp(extras) {
    _reporterNs.report("GameSceneViewComp", "../../sceneMgr/vIew/GameSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEntity(extras) {
    _reporterNs.report("GameEntity", "./GameEntity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      input = _cc.input;
      Input = _cc.Input;
      PhysicsSystem = _cc.PhysicsSystem;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      PHY_GROUP = _unresolved_3.PHY_GROUP;
    }, function (_unresolved_4) {
      smc = _unresolved_4.smc;
    }, function (_unresolved_5) {
      ItemSceneViewComp = _unresolved_5.ItemSceneViewComp;
    }, function (_unresolved_6) {
      GameSceneViewComp = _unresolved_6.GameSceneViewComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "15b11JywPdI6Z9ridQfpm7x", "ItemInteractionManager", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'input', 'Input', 'PhysicsSystem']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 物品交互管理器 - 智能触摸组件版本
       * 负责触摸事件的捕获和处理，与类版本协同工作
       */

      _export("ItemInteractionManager", ItemInteractionManager = (_dec = ccclass('ItemInteractionManager'), _dec(_class = class ItemInteractionManager extends Component {
        constructor() {
          super(...arguments);
          this.gameEntity = null;
          this.currentTouchedItem = null;
          // 🎯 防抖和状态管理
          this.lastClickTime = 0;
          this.clickDelay = 100;
          // 100ms防抖延迟
          this.isProcessingClick = false;
          // 🚀 性能优化：触摸移动节流 - 增强版
          this.lastMoveTime = 0;
          this.moveThrottleDelay = 33;
          // 30FPS限制，约33ms (降低检测频率)
          this.lastMovePosition = {
            x: 0,
            y: 0
          };
          this.moveDistanceThreshold = 10;
          // 增加像素阈值到10px，减少不必要的检测
          // 🎯 射线检测缓存优化
          this.raycastCache = new Map();
          this.raycastCacheTimeout = 100;
          // 缓存100ms，减少重复计算
          this.lastRaycastPosition = {
            x: -1,
            y: -1
          };
          this.raycastPositionThreshold = 8;
          // 射线检测位置阈值
          // 🎯 简单性能监控
          this.raycastCount = 0;
          this.cacheHitCount = 0;
          this.lastPerformanceReport = 0;
        }

        onLoad() {
          input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');
        }

        start() {
          // 在start中获取GameEntity，确保ent属性已经设置
          var gameSceneViewComp = this.node.getComponent(_crd && GameSceneViewComp === void 0 ? (_reportPossibleCrUseOfGameSceneViewComp({
            error: Error()
          }), GameSceneViewComp) : GameSceneViewComp);

          if (gameSceneViewComp && gameSceneViewComp.ent) {
            this.gameEntity = gameSceneViewComp.ent;
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\u2705 ItemInteractionManager\u83B7\u53D6\u5230GameEntity: " + (this.gameEntity ? '成功' : '失败'));
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');
          }
        }

        onDestroy() {
          input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this); // 🧹 清理射线检测缓存

          this.raycastCache.clear();
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');
        }
        /**
         * 🔍 检查是否可以处理点击
         */


        canProcessClick() {
          var currentTime = Date.now(); // 防抖检查

          if (currentTime - this.lastClickTime < this.clickDelay) {
            // console.log('🛡️ 点击过快，触发防抖保护');
            return false;
          } // 处理状态检查


          if (this.isProcessingClick) {
            // console.log('🛡️ 正在处理点击，跳过重复处理');
            return false;
          }

          return true;
        }
        /**
         * 🎯 智能物品选择逻辑
         */


        intelligentItemSelection(item) {
          if (!this.gameEntity || !this.gameEntity.interactionManager) {
            console.warn('⚠️ GameEntity或InteractionManager未准备好');
            return false;
          }

          var itemNode = item.node;
          var interactionManager = this.gameEntity.interactionManager; // 🔍 检查物品是否在收集槽管理器中

          if (interactionManager.slotManager) {
            var slot = interactionManager.slotManager.getSlotByItem(itemNode);

            if (slot) {
              // 物品在收集槽中
              if (slot.index >= 7) {
                // 在额外槽位(7-9)，可以选中移动到主槽位
                // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);
                return true;
              } else {
                // 在主槽位(0-6)，不能选中
                // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);
                return false;
              }
            }
          } // 🔍 检查物品是否在场景中可选择


          var isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());

          if (!isSelectable) {
            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);
            return false;
          } // 🔍 检查收集槽是否已满


          if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {
            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);
            return false;
          }

          return true;
        }
        /**
         * 🚀 优化版射线检测 - 带缓存和智能跳过
         */


        detectItemAtPosition(event) {
          if (!this.gameEntity) {
            return null;
          }

          var camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;

          if (!camera) {
            return null;
          }

          var currentX = event.getLocationX();
          var currentY = event.getLocationY(); // 🎯 检查位置是否变化足够大，避免无意义的重复检测

          var deltaX = Math.abs(currentX - this.lastRaycastPosition.x);
          var deltaY = Math.abs(currentY - this.lastRaycastPosition.y);

          if (deltaX < this.raycastPositionThreshold && deltaY < this.raycastPositionThreshold) {
            // 位置变化很小，使用缓存结果
            var _cacheKey = Math.floor(currentX / this.raycastPositionThreshold) + "_" + Math.floor(currentY / this.raycastPositionThreshold);

            var cached = this.raycastCache.get(_cacheKey);

            if (cached && Date.now() - cached.timestamp < this.raycastCacheTimeout) {
              this.cacheHitCount++;
              this.reportPerformanceIfNeeded();
              return cached.item;
            }
          } // 🎯 执行射线检测（带性能监控）


          var startTime = performance.now();
          var ray = camera.screenPointToRay(currentX, currentY);
          var raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA;
          var result = null;

          if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
            var raycastResult = PhysicsSystem.instance.raycastClosestResult;
            var hitNode = raycastResult.collider.node;
            var itemSceneViewComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
              error: Error()
            }), ItemSceneViewComp) : ItemSceneViewComp);

            if (itemSceneViewComp && itemSceneViewComp.ItemModel) {
              result = itemSceneViewComp;
            }
          } // 🎯 记录性能数据


          var endTime = performance.now();
          var duration = endTime - startTime;
          this.raycastCount++;

          if (duration > 2) {
            // 超过2ms记录警告
            console.warn("\u26A0\uFE0F \u5C04\u7EBF\u68C0\u6D4B\u8017\u65F6\u8FC7\u957F: " + duration.toFixed(2) + "ms");
          } // 🎯 更新缓存


          var cacheKey = Math.floor(currentX / this.raycastPositionThreshold) + "_" + Math.floor(currentY / this.raycastPositionThreshold);
          this.raycastCache.set(cacheKey, {
            item: result,
            timestamp: Date.now()
          }); // 🎯 清理过期缓存（防止内存泄漏）

          if (this.raycastCache.size > 50) {
            var now = Date.now();
            var keysToDelete = [];
            this.raycastCache.forEach((value, key) => {
              if (now - value.timestamp > this.raycastCacheTimeout * 2) {
                keysToDelete.push(key);
              }
            });
            keysToDelete.forEach(key => this.raycastCache.delete(key));
          } // 更新最后检测位置


          this.lastRaycastPosition.x = currentX;
          this.lastRaycastPosition.y = currentY;
          return result;
        }
        /**
         * 🎯 性能报告（如果需要）
         */


        reportPerformanceIfNeeded() {
          var now = Date.now();

          if (now - this.lastPerformanceReport > 10000) {
            // 每10秒报告一次
            var cacheHitRate = this.raycastCount > 0 ? this.cacheHitCount / this.raycastCount * 100 : 0;

            if (this.raycastCount > 0) {
              console.log("\uD83C\uDFAF \u5C04\u7EBF\u68C0\u6D4B\u6027\u80FD\u62A5\u544A: \u603B\u6B21\u6570=" + this.raycastCount + ", \u7F13\u5B58\u547D\u4E2D\u7387=" + cacheHitRate.toFixed(1) + "%");

              if (cacheHitRate < 30) {
                console.warn("\u26A0\uFE0F \u7F13\u5B58\u547D\u4E2D\u7387\u8F83\u4F4E: " + cacheHitRate.toFixed(1) + "%\uFF0C\u5EFA\u8BAE\u4F18\u5316\u7F13\u5B58\u7B56\u7565");
              }
            }

            this.lastPerformanceReport = now;
          }
        }
        /**
         * 应用触摸效果到物品
         */


        applyTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = true;

            if (typeof itemComp.onTouch === 'function') {
              itemComp.onTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);

          }
        }
        /**
         * 取消物品的触摸效果
         */


        cancelTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = false;

            if (typeof itemComp.onCancelTouch === 'function') {
              itemComp.onCancelTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);

          }
        }
        /**
         * 处理触摸开始事件
         */


        onTouchStart(event) {
          // 🚀 初始化移动位置缓存
          this.lastMovePosition.x = event.getLocationX();
          this.lastMovePosition.y = event.getLocationY();
          this.lastMoveTime = Date.now(); // 高频交互日志注释掉，减少日志噪音
          // oops.log.logTrace('🎯 触摸开始');

          var hitItem = this.detectItemAtPosition(event);

          if (hitItem) {
            this.currentTouchedItem = hitItem;
            this.applyTouchEffect(hitItem);
          } else {// oops.log.logTrace('🎯 触摸开始：未击中任何物品');
          }
        }
        /**
         * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测
         */


        onTouchMove(event) {
          var currentTime = Date.now();
          var currentX = event.getLocationX();
          var currentY = event.getLocationY(); // 🎯 时间节流：限制检测频率到30FPS

          if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {
            return;
          } // 🎯 距离阈值：只有移动足够距离才触发检测


          var deltaX = Math.abs(currentX - this.lastMovePosition.x);
          var deltaY = Math.abs(currentY - this.lastMovePosition.y);
          var distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

          if (distance < this.moveDistanceThreshold) {
            return;
          } // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率


          if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {
            // 更新位置但不执行射线检测，减少性能消耗
            this.lastMoveTime = currentTime;
            this.lastMovePosition.x = currentX;
            this.lastMovePosition.y = currentY;
            return;
          } // 更新缓存


          this.lastMoveTime = currentTime;
          this.lastMovePosition.x = currentX;
          this.lastMovePosition.y = currentY; // 🎯 执行射线检测（现在频率大大降低）

          var hitItem = this.detectItemAtPosition(event); // 如果当前触摸的物品和检测到的物品不同，需要切换

          if (this.currentTouchedItem !== hitItem) {
            // 取消之前物品的触摸效果
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } // 应用新物品的触摸效果


            if (hitItem) {
              this.applyTouchEffect(hitItem); // 高频交互日志注释掉，减少日志噪音
              // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);
            }

            this.currentTouchedItem = hitItem;
          }
        }
        /**
         * 处理触摸结束事件 - 智能版本
         */


        onTouchEnd(event) {
          // 🛡️ 防抖和状态检查
          if (!this.canProcessClick()) {
            // 清除触摸状态但不处理选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            }

            this.currentTouchedItem = null;
            return;
          } // 只有在物品上松手才算选中


          var hitItem = this.detectItemAtPosition(event);

          if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {
            var _this$currentTouchedI;

            // 在同一个物品上松手，检查是否可以选中
            this.cancelTouchEffect(this.currentTouchedItem);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\u2705 \u68C0\u6D4B\u5230\u7269\u54C1\u9009\u62E9: " + (((_this$currentTouchedI = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI.name) || '未知物品')); // 🎯 智能选择检查

            if (this.intelligentItemSelection(this.currentTouchedItem)) {
              // 设置处理状态
              this.isProcessingClick = true;
              this.lastClickTime = Date.now(); // 触发选择逻辑

              if (this.gameEntity && this.gameEntity.interactionManager) {
                try {
                  var _this$currentTouchedI2;

                  this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness("\uD83C\uDFAF \u6210\u529F\u89E6\u53D1\u7269\u54C1\u9009\u62E9: " + (((_this$currentTouchedI2 = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI2.name) || '未知物品'));
                } catch (error) {
                  console.error('❌ 物品选择过程中发生错误:', error);
                }
              } // 延迟重置处理状态


              setTimeout(() => {
                this.isProcessingClick = false;
              }, this.clickDelay);
            } else {}
          } else {
            // 不在原物品上松手，取消选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } else {// oops.log.logTrace('❌ 在空白区域松手，无选择');
            }
          } // 清除当前触摸状态


          this.currentTouchedItem = null;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cc9c89e02d4318577b41826c41affe2e4583b9e6.js.map