System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, input, Input, PhysicsSystem, oops, PHY_GROUP, smc, ItemSceneViewComp, GameSceneViewComp, _dec, _class, _crd, ccclass, property, ItemInteractionManager;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPHY_GROUP(extras) {
    _reporterNs.report("PHY_GROUP", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfItemSceneViewComp(extras) {
    _reporterNs.report("ItemSceneViewComp", "../../item/view/ItemSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameSceneViewComp(extras) {
    _reporterNs.report("GameSceneViewComp", "../../sceneMgr/vIew/GameSceneViewComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEntity(extras) {
    _reporterNs.report("GameEntity", "./GameEntity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      input = _cc.input;
      Input = _cc.Input;
      PhysicsSystem = _cc.PhysicsSystem;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      PHY_GROUP = _unresolved_3.PHY_GROUP;
    }, function (_unresolved_4) {
      smc = _unresolved_4.smc;
    }, function (_unresolved_5) {
      ItemSceneViewComp = _unresolved_5.ItemSceneViewComp;
    }, function (_unresolved_6) {
      GameSceneViewComp = _unresolved_6.GameSceneViewComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "15b11JywPdI6Z9ridQfpm7x", "ItemInteractionManager", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'input', 'Input', 'PhysicsSystem']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 物品交互管理器 - 智能触摸组件版本
       * 负责触摸事件的捕获和处理，与类版本协同工作
       */

      _export("ItemInteractionManager", ItemInteractionManager = (_dec = ccclass('ItemInteractionManager'), _dec(_class = class ItemInteractionManager extends Component {
        constructor() {
          super(...arguments);
          this.gameEntity = null;
          this.currentTouchedItem = null;
          // 🎯 防抖和状态管理
          this.lastClickTime = 0;
          this.clickDelay = 100;
          // 100ms防抖延迟
          this.isProcessingClick = false;
          // 🚀 性能优化：触摸移动节流 - 增强版
          this.lastMoveTime = 0;
          this.moveThrottleDelay = 33;
          // 30FPS限制，约33ms (降低检测频率)
          this.lastMovePosition = {
            x: 0,
            y: 0
          };
          this.moveDistanceThreshold = 10;
          // 增加像素阈值到10px，减少不必要的检测
          // 🎯 性能优化配置
          this.lastRaycastPosition = {
            x: -1,
            y: -1
          };
          this.lastRaycastTime = 0;
          this.raycastThrottleDelay = 16;
          // 60FPS节流，约16ms
          this.moveDistanceThreshold = 5;
          // 移动5px以上才重新检测
          this.lastDetectedItem = null;
          // 🎯 长按优化
          this.isLongPressing = false;
          this.longPressStartTime = 0;
          this.longPressThreshold = 500;
          // 500ms算长按
          this.longPressRaycastDelay = 33;
        }

        // 长按时30FPS检测
        onLoad() {
          input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');
        }

        start() {
          // 在start中获取GameEntity，确保ent属性已经设置
          var gameSceneViewComp = this.node.getComponent(_crd && GameSceneViewComp === void 0 ? (_reportPossibleCrUseOfGameSceneViewComp({
            error: Error()
          }), GameSceneViewComp) : GameSceneViewComp);

          if (gameSceneViewComp && gameSceneViewComp.ent) {
            this.gameEntity = gameSceneViewComp.ent;
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\u2705 ItemInteractionManager\u83B7\u53D6\u5230GameEntity: " + (this.gameEntity ? '成功' : '失败'));
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');
          }
        }

        onDestroy() {
          input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
          input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this); // 🧹 清理射线检测缓存

          this.raycastCache.clear();
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');
        }
        /**
         * 🔍 检查是否可以处理点击
         */


        canProcessClick() {
          var currentTime = Date.now(); // 防抖检查

          if (currentTime - this.lastClickTime < this.clickDelay) {
            // console.log('🛡️ 点击过快，触发防抖保护');
            return false;
          } // 处理状态检查


          if (this.isProcessingClick) {
            // console.log('🛡️ 正在处理点击，跳过重复处理');
            return false;
          }

          return true;
        }
        /**
         * 🎯 智能物品选择逻辑
         */


        intelligentItemSelection(item) {
          if (!this.gameEntity || !this.gameEntity.interactionManager) {
            console.warn('⚠️ GameEntity或InteractionManager未准备好');
            return false;
          }

          var itemNode = item.node;
          var interactionManager = this.gameEntity.interactionManager; // 🔍 检查物品是否在收集槽管理器中

          if (interactionManager.slotManager) {
            var slot = interactionManager.slotManager.getSlotByItem(itemNode);

            if (slot) {
              // 物品在收集槽中
              if (slot.index >= 7) {
                // 在额外槽位(7-9)，可以选中移动到主槽位
                // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);
                return true;
              } else {
                // 在主槽位(0-6)，不能选中
                // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);
                return false;
              }
            }
          } // 🔍 检查物品是否在场景中可选择


          var isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());

          if (!isSelectable) {
            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);
            return false;
          } // 🔍 检查收集槽是否已满


          if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {
            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);
            return false;
          }

          return true;
        }
        /**
         * 🚀 整合优化的射线检测 - 高性能精确检测
         */


        detectItemAtPosition(event) {
          if (!this.gameEntity) {
            return null;
          }

          var currentX = event.getLocationX();
          var currentY = event.getLocationY();
          var now = Date.now(); // 🎯 计算移动距离

          var deltaX = Math.abs(currentX - this.lastRaycastPosition.x);
          var deltaY = Math.abs(currentY - this.lastRaycastPosition.y);
          var distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY); // 🎯 性能优化：移动距离小且时间间隔短，返回上次结果

          if (distance < this.moveDistanceThreshold && now - this.lastRaycastTime < this.raycastThrottleDelay && this.lastDetectedItem) {
            return this.lastDetectedItem;
          } // 🎯 长按检测优化


          var raycastDelay = this.isLongPressing ? this.longPressRaycastDelay : this.raycastThrottleDelay;

          if (now - this.lastRaycastTime < raycastDelay) {
            return this.lastDetectedItem;
          } // 🎯 执行射线检测


          var detectedItem = this.performOptimizedRaycast(currentX, currentY); // 🎯 更新缓存

          this.lastRaycastPosition.x = currentX;
          this.lastRaycastPosition.y = currentY;
          this.lastRaycastTime = now;
          this.lastDetectedItem = detectedItem;
          return detectedItem;
        }
        /**
         * 🎯 优化的射线检测 - 整合所有检测逻辑
         */


        performOptimizedRaycast(x, y) {
          var camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;

          if (!camera) {
            return null;
          }

          var ray = camera.screenPointToRay(x, y);
          var raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA; // 🎯 执行精确射线检测 - 只检测最前面的可见物品

          if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
            var result = PhysicsSystem.instance.raycastClosestResult;
            var hitNode = result.collider.node;
            var itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
              error: Error()
            }), ItemSceneViewComp) : ItemSceneViewComp);

            if (itemComp && itemComp.ItemModel) {
              return itemComp;
            }
          }

          return null;
        }
        /**
         * 🎯 简化多层射线检测 - 只使用单点射线，逐层检测
         */


        performSimpleMultiLayerRaycast(x, y) {
          var camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;
          var ray = camera.screenPointToRay(x, y);
          var raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA;
          var allItems = [];
          var maxLayers = 3; // 最多检测3层

          var disabledColliders = [];

          try {
            var _loop = function _loop() {
              if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                var result = PhysicsSystem.instance.raycastClosestResult;
                var hitNode = result.collider.node;
                var itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                  error: Error()
                }), ItemSceneViewComp) : ItemSceneViewComp);

                if (itemComp && itemComp.ItemModel) {
                  // 避免重复添加同一个物品
                  if (!allItems.find(item => item.node === hitNode)) {
                    allItems.push(itemComp);
                  } // 临时禁用这个碰撞体，以便检测下一层


                  var collider = result.collider;

                  if (collider.enabled) {
                    collider.enabled = false;
                    disabledColliders.push(collider);
                  }
                } else {
                  return 0; // break
                  // 没有找到有效物品，停止检测
                }
              } else {
                return 0; // break
                // 没有更多碰撞，停止检测
              }
            },
                _ret;

            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (var layer = 0; layer < maxLayers; layer++) {
              _ret = _loop();
              if (_ret === 0) break;
            }
          } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
              if (collider && collider.isValid) {
                collider.enabled = true;
              }
            });
          }

          return allItems;
        }
        /**
         * 🎯 改进的层级切换 - 需要持续慢速移动才触发
         */


        handleSimpleLayerSwitching(items, moveDistance) {
          if (items.length <= 1) {
            return items[0] || null;
          }

          var now = Date.now();
          var isSlowMove = moveDistance < this.slowMoveThreshold; // 🎯 如果不是慢速移动，直接返回顶层物品

          if (!isSlowMove) {
            this.exitSlowMoveMode();
            return items[0];
          } // 🎯 开始记录慢速移动时间


          if (!this.isInSlowMoveMode) {
            this.isInSlowMoveMode = true;
            this.slowMoveStartTime = now;
            this.currentItemIndex = 0;
            this.candidateItems = items.map(item => ({
              item,
              score: 100
            })); // 不立即开始切换，先返回顶层物品

            return items[0];
          } // 🎯 检查是否持续慢速移动足够长时间


          var slowMoveDuration = now - this.slowMoveStartTime;

          if (slowMoveDuration < this.slowMoveRequiredTime) {
            // 还没有持续足够长时间，继续返回顶层物品
            return items[0];
          } // 🎯 现在开始层级切换


          if (slowMoveDuration >= this.slowMoveRequiredTime) {
            // 第一次进入切换模式
            if (this.lastItemSwitchTime === 0) {
              this.lastItemSwitchTime = now;
              console.log("\uD83C\uDFAF \u6301\u7EED\u6162\u901F\u79FB\u52A8" + this.slowMoveRequiredTime + "ms\uFF0C\u5F00\u59CB\u5C42\u7EA7\u5207\u6362 (\u5171" + items.length + "\u5C42)");
            } // 检查是否应该切换到下一层


            if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {
              var _items$this$currentIt;

              this.currentItemIndex = (this.currentItemIndex + 1) % items.length;
              this.lastItemSwitchTime = now;
              console.log("\uD83D\uDD04 \u5207\u6362\u5230\u7B2C" + (this.currentItemIndex + 1) + "\u5C42: " + (((_items$this$currentIt = items[this.currentItemIndex].node) == null ? void 0 : _items$this$currentIt.name) || '未知'));
            }
          } // 🎯 检查是否超时


          if (slowMoveDuration > this.maxSlowMoveTime) {
            this.exitSlowMoveMode();
            return items[0]; // 返回顶层物品
          }

          return items[this.currentItemIndex] || items[0];
        }
        /**
         * 🎯 合并和评分物品
         */


        mergeAndScoreItems(raycastItems, screenItems, touchX, touchY) {
          var candidates = new Map();
          var camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera; // 处理射线检测结果（高优先级）

          for (var item of raycastItems) {
            var worldPos = item.node.worldPosition;
            var screenPos = camera.worldToScreen(worldPos);
            var distance = Math.sqrt(Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2));
            candidates.set(item.node.uuid, {
              item,
              score: 100 - distance * 0.5 // 射线检测基础分100，距离越近分数越高

            });
          } // 处理屏幕距离检测结果（低优先级）


          for (var _item of screenItems) {
            if (!candidates.has(_item.node.uuid)) {
              var _worldPos = _item.node.worldPosition;

              var _screenPos = camera.worldToScreen(_worldPos);

              var _distance = Math.sqrt(Math.pow(_screenPos.x - touchX, 2) + Math.pow(_screenPos.y - touchY, 2));

              candidates.set(_item.node.uuid, {
                item: _item,
                score: 50 - _distance * 0.3 // 屏幕距离基础分50

              });
            }
          }

          return Array.from(candidates.values());
        }
        /**
         * 🎯 选择最佳物品
         */


        selectBestItem(candidates) {
          if (candidates.length === 0) {
            return null;
          } // 按分数排序，选择最高分的物品


          candidates.sort((a, b) => b.score - a.score);
          var bestCandidate = candidates[0]; // 如果有多个候选物品，可以在这里实现慢速移动时的切换逻辑

          if (candidates.length > 1) {
            return this.handleMultipleCandidates(candidates);
          }

          return bestCandidate.item;
        }
        /**
         * 🎯 处理多个候选物品
         */


        handleMultipleCandidates(candidates) {
          var _candidates$this$curr;

          var now = Date.now(); // 检查是否为慢速移动模式

          if (!this.isInSlowMoveMode) {
            this.isInSlowMoveMode = true;
            this.slowMoveStartTime = now;
            this.candidateItems = candidates;
            this.currentItemIndex = 0;
            console.log("\uD83C\uDFAF \u68C0\u6D4B\u5230" + candidates.length + "\u4E2A\u5019\u9009\u7269\u54C1\uFF0C\u8FDB\u5165\u6162\u901F\u9009\u62E9\u6A21\u5F0F");
          } // 检查是否超时


          if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {
            this.exitSlowMoveMode();
            return candidates[0].item; // 返回最高分物品
          } // 检查是否应该切换


          if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {
            var _currentItem$item$nod;

            this.currentItemIndex = (this.currentItemIndex + 1) % candidates.length;
            this.lastItemSwitchTime = now;
            var currentItem = candidates[this.currentItemIndex];
            console.log("\uD83D\uDD04 \u5207\u6362\u5230\u5019\u9009\u7269\u54C1: " + (((_currentItem$item$nod = currentItem.item.node) == null ? void 0 : _currentItem$item$nod.name) || '未知') + " (" + (this.currentItemIndex + 1) + "/" + candidates.length + ")");
          }

          return ((_candidates$this$curr = candidates[this.currentItemIndex]) == null ? void 0 : _candidates$this$curr.item) || candidates[0].item;
        }
        /**
         * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品
         */


        performMultiLayerRaycast(x, y) {
          var startTime = performance.now();
          var camera = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).camera.CameraModel.camera;
          var ray = camera.screenPointToRay(x, y);
          var raycastMask = (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX | (_crd && PHY_GROUP === void 0 ? (_reportPossibleCrUseOfPHY_GROUP({
            error: Error()
          }), PHY_GROUP) : PHY_GROUP).ITEM_BOX_EXTRA;
          var allItems = [];
          var maxLayers = 3; // 最多检测3层，平衡性能和功能

          var disabledColliders = [];

          try {
            var _loop2 = function _loop2() {
              if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {
                var result = PhysicsSystem.instance.raycastClosestResult;
                var hitNode = result.collider.node;
                var itemComp = hitNode.getComponent(_crd && ItemSceneViewComp === void 0 ? (_reportPossibleCrUseOfItemSceneViewComp({
                  error: Error()
                }), ItemSceneViewComp) : ItemSceneViewComp);

                if (itemComp && itemComp.ItemModel) {
                  // 避免重复添加同一个物品
                  if (!allItems.find(item => item.node === hitNode)) {
                    allItems.push(itemComp);
                  } // 临时禁用这个碰撞体，以便检测下一层


                  var collider = result.collider;

                  if (collider.enabled) {
                    collider.enabled = false;
                    disabledColliders.push(collider);
                  }
                } else {
                  return 0; // break
                  // 没有找到有效物品，停止检测
                }
              } else {
                return 0; // break
                // 没有更多碰撞，停止检测
              }
            },
                _ret2;

            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层
            for (var layer = 0; layer < maxLayers; layer++) {
              _ret2 = _loop2();
              if (_ret2 === 0) break;
            }
          } finally {
            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）
            disabledColliders.forEach(collider => {
              if (collider && collider.isValid) {
                collider.enabled = true;
              }
            });
          } // 🎯 记录性能数据


          var endTime = performance.now();
          var duration = endTime - startTime;

          if (duration > 3) {
            // 多层检测阈值稍高
            console.warn("\u26A0\uFE0F \u591A\u5C42\u5C04\u7EBF\u68C0\u6D4B\u8017\u65F6: " + duration.toFixed(2) + "ms\uFF0C\u68C0\u6D4B\u5230" + allItems.length + "\u4E2A\u7269\u54C1");
          }

          return allItems;
        }
        /**
         * 🎯 退出慢速移动模式
         */


        exitSlowMoveMode() {
          if (this.isInSlowMoveMode) {
            this.isInSlowMoveMode = false;
            this.currentItemIndex = 0;
            this.candidateItems = [];
            this.lastItemSwitchTime = 0; // 重置切换时间

            console.log('🏁 退出慢速移动模式，回到顶层');
          }
        }
        /**
         * 🎯 暂停物品切换 - 选择物品后暂停一段时间
         */


        pauseLayerSwitching() {
          this.exitSlowMoveMode();
          this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒

          console.log("\u23F8\uFE0F \u7269\u54C1\u5DF2\u9009\u62E9\uFF0C\u6682\u505C\u7269\u54C1\u5207\u6362 1000ms");
        }
        /**
         * 🎯 清理过期缓存
         */


        cleanupCache() {
          if (this.raycastCache.size > 50) {
            var now = Date.now();
            var keysToDelete = [];
            this.raycastCache.forEach((value, key) => {
              if (now - value.timestamp > this.raycastCacheTimeout * 2) {
                keysToDelete.push(key);
              }
            });
            keysToDelete.forEach(key => this.raycastCache.delete(key));
          }
        }
        /**
         * 🎯 性能报告（如果需要）
         */


        reportPerformanceIfNeeded() {
          var now = Date.now();

          if (now - this.lastPerformanceReport > 10000) {
            // 每10秒报告一次
            var cacheHitRate = this.raycastCount > 0 ? this.cacheHitCount / this.raycastCount * 100 : 0;

            if (this.raycastCount > 0) {
              console.log("\uD83C\uDFAF \u5C04\u7EBF\u68C0\u6D4B\u6027\u80FD\u62A5\u544A: \u603B\u6B21\u6570=" + this.raycastCount + ", \u7F13\u5B58\u547D\u4E2D\u7387=" + cacheHitRate.toFixed(1) + "%");

              if (cacheHitRate < 30) {
                console.warn("\u26A0\uFE0F \u7F13\u5B58\u547D\u4E2D\u7387\u8F83\u4F4E: " + cacheHitRate.toFixed(1) + "%\uFF0C\u5EFA\u8BAE\u4F18\u5316\u7F13\u5B58\u7B56\u7565");
              }
            }

            this.lastPerformanceReport = now;
          }
        }
        /**
         * 应用触摸效果到物品
         */


        applyTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = true;

            if (typeof itemComp.onTouch === 'function') {
              itemComp.onTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);

          }
        }
        /**
         * 取消物品的触摸效果
         */


        cancelTouchEffect(itemComp) {
          if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {
            itemComp.ItemModel.touching = false;

            if (typeof itemComp.onCancelTouch === 'function') {
              itemComp.onCancelTouch();
            } // 高频交互日志改为trace级别，减少日志噪音
            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);

          }
        }
        /**
         * 处理触摸开始事件 - 添加长按检测
         */


        onTouchStart(event) {
          var now = Date.now(); // 🚀 初始化移动位置缓存

          this.lastMovePosition.x = event.getLocationX();
          this.lastMovePosition.y = event.getLocationY();
          this.lastMoveTime = now; // 🎯 初始化长按检测

          this.isLongPressing = false;
          this.longPressStartTime = now; // 高频交互日志注释掉，减少日志噪音
          // oops.log.logTrace('🎯 触摸开始');

          var hitItem = this.detectItemAtPosition(event);

          if (hitItem) {
            this.currentTouchedItem = hitItem;
            this.applyTouchEffect(hitItem);
          } else {// oops.log.logTrace('🎯 触摸开始：未击中任何物品');
          }
        }
        /**
         * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测
         */


        onTouchMove(event) {
          var currentTime = Date.now();
          var currentX = event.getLocationX();
          var currentY = event.getLocationY(); // 🎯 时间节流：限制检测频率到30FPS

          if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {
            return;
          } // 🎯 距离阈值：只有移动足够距离才触发检测


          var deltaX = Math.abs(currentX - this.lastMovePosition.x);
          var deltaY = Math.abs(currentY - this.lastMovePosition.y);
          var distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

          if (distance < this.moveDistanceThreshold) {
            return;
          } // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率


          if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {
            // 更新位置但不执行射线检测，减少性能消耗
            this.lastMoveTime = currentTime;
            this.lastMovePosition.x = currentX;
            this.lastMovePosition.y = currentY;
            return;
          } // 更新缓存


          this.lastMoveTime = currentTime;
          this.lastMovePosition.x = currentX;
          this.lastMovePosition.y = currentY; // 🎯 执行射线检测（现在频率大大降低）

          var hitItem = this.detectItemAtPosition(event); // 如果当前触摸的物品和检测到的物品不同，需要切换

          if (this.currentTouchedItem !== hitItem) {
            // 取消之前物品的触摸效果
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } // 应用新物品的触摸效果


            if (hitItem) {
              this.applyTouchEffect(hitItem); // 高频交互日志注释掉，减少日志噪音
              // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);
            }

            this.currentTouchedItem = hitItem;
          }
        }
        /**
         * 处理触摸结束事件 - 智能版本
         */


        onTouchEnd(event) {
          // 🛡️ 防抖和状态检查
          if (!this.canProcessClick()) {
            // 清除触摸状态但不处理选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            }

            this.currentTouchedItem = null;
            return;
          } // 只有在物品上松手才算选中


          var hitItem = this.detectItemAtPosition(event);

          if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {
            var _this$currentTouchedI;

            // 在同一个物品上松手，检查是否可以选中
            this.cancelTouchEffect(this.currentTouchedItem);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\u2705 \u68C0\u6D4B\u5230\u7269\u54C1\u9009\u62E9: " + (((_this$currentTouchedI = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI.name) || '未知物品')); // 🎯 智能选择检查

            if (this.intelligentItemSelection(this.currentTouchedItem)) {
              // 设置处理状态
              this.isProcessingClick = true;
              this.lastClickTime = Date.now(); // 触发选择逻辑

              if (this.gameEntity && this.gameEntity.interactionManager) {
                try {
                  var _this$currentTouchedI2;

                  this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness("\uD83C\uDFAF \u6210\u529F\u89E6\u53D1\u7269\u54C1\u9009\u62E9: " + (((_this$currentTouchedI2 = this.currentTouchedItem.node) == null ? void 0 : _this$currentTouchedI2.name) || '未知物品')); // 🎯 选择物品后暂停层级切换

                  this.pauseLayerSwitching();
                } catch (error) {
                  console.error('❌ 物品选择过程中发生错误:', error);
                }
              } // 延迟重置处理状态


              setTimeout(() => {
                this.isProcessingClick = false;
              }, this.clickDelay);
            } else {}
          } else {
            // 不在原物品上松手，取消选择
            if (this.currentTouchedItem) {
              this.cancelTouchEffect(this.currentTouchedItem);
            } else {// oops.log.logTrace('❌ 在空白区域松手，无选择');
            }
          } // 清除当前触摸状态


          this.currentTouchedItem = null;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cc9c89e02d4318577b41826c41affe2e4583b9e6.js.map