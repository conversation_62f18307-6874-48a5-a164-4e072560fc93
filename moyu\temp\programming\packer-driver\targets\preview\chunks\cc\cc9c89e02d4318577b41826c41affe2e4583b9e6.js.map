{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "PhysicsSystem", "oops", "PHY_GROUP", "smc", "ItemSceneViewComp", "GameSceneViewComp", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "raycastCache", "Map", "raycastCacheTimeout", "lastRaycastPosition", "raycastPositionThreshold", "currentBestItem", "itemSwitchDelay", "lastItemSwitchTime", "candidate<PERSON><PERSON>s", "currentItemIndex", "slowMoveThreshold", "isInSlowMoveMode", "slowMoveStartTime", "maxSlowMoveTime", "slowMoveRequiredTime", "raycastCount", "cacheHitCount", "lastPerformanceReport", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "clear", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "camera", "CameraModel", "currentX", "getLocationX", "currentY", "getLocationY", "deltaX", "Math", "abs", "deltaY", "distance", "sqrt", "allItems", "performSimpleMultiLayerRaycast", "length", "exitSlowMoveMode", "handleSimpleLayerSwitching", "ray", "screenPointToRay", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "maxLayers", "disabledColliders", "instance", "raycastClosest", "result", "raycastClosestResult", "hitNode", "collider", "itemComp", "ItemModel", "find", "push", "enabled", "layer", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "items", "moveDistance", "isSlowMove", "map", "score", "slowMoveDuration", "name", "mergeAndScoreItems", "raycastItems", "screenItems", "touchX", "touchY", "candidates", "worldPos", "worldPosition", "screenPos", "worldToScreen", "pow", "set", "uuid", "Array", "from", "values", "selectBestItem", "sort", "a", "b", "bestCandidate", "handleMultipleCandidates", "currentItem", "performMultiLayerRaycast", "startTime", "performance", "endTime", "duration", "toFixed", "pauseLayerSwitching", "cleanupCache", "size", "keysToDelete", "value", "key", "timestamp", "delete", "reportPerformanceIfNeeded", "cacheHitRate", "applyTouchEffect", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "hitItem", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,a,OAAAA,a;;AACjDC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;wCAEaY,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CX,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CY,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,EAbU;AAaN;AAE5C;AAfkD,eAgB1CC,YAhB0C,GAiB9C,IAAIC,GAAJ,EAjB8C;AAAA,eAkB1CC,mBAlB0C,GAkBZ,GAlBY;AAkBP;AAlBO,eAmB1CC,mBAnB0C,GAmBM;AAAEN,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAnBN;AAAA,eAoB1CM,wBApB0C,GAoBP,CApBO;AAoBJ;AAE9C;AAtBkD,eAuB1CC,eAvB0C,GAuBE,IAvBF;AAuBQ;AAvBR,eAwB1CC,eAxB0C,GAwBhB,GAxBgB;AAwBX;AAxBW,eAyB1CC,kBAzB0C,GAyBb,CAzBa;AAAA,eA0B1CC,cA1B0C,GA0B0B,EA1B1B;AA0B8B;AA1B9B,eA2B1CC,gBA3B0C,GA2Bf,CA3Be;AA2BZ;AAEtC;AA7BkD,eA8B1CC,iBA9B0C,GA8Bd,CA9Bc;AA8BX;AA9BW,eA+B1CC,gBA/B0C,GA+Bd,KA/Bc;AAAA,eAgC1CC,iBAhC0C,GAgCd,CAhCc;AAAA,eAiC1CC,eAjC0C,GAiChB,IAjCgB;AAiCV;AAjCU,eAkC1CC,oBAlC0C,GAkCX,GAlCW;AAkCN;AAE5C;AApCkD,eAqC1CC,YArC0C,GAqCnB,CArCmB;AAAA,eAsC1CC,aAtC0C,GAsClB,CAtCkB;AAAA,eAuC1CC,qBAvC0C,GAuCV,CAvCU;AAAA;;AAyClDC,QAAAA,MAAM,GAAG;AACLxC,UAAAA,KAAK,CAACyC,EAAN,CAASxC,KAAK,CAACyC,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACA5C,UAAAA,KAAK,CAACyC,EAAN,CAASxC,KAAK,CAACyC,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACA9C,UAAAA,KAAK,CAACyC,EAAN,CAASxC,KAAK,CAACyC,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,cAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAK5C,UAAL,GAAkByC,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,kEAC8C,KAAKvC,UAAL,GAAkB,IAAlB,GAAyB,IADvE;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAKsC,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACRzD,UAAAA,KAAK,CAAC0D,GAAN,CAAUzD,KAAK,CAACyC,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACA5C,UAAAA,KAAK,CAAC0D,GAAN,CAAUzD,KAAK,CAACyC,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACA9C,UAAAA,KAAK,CAAC0D,GAAN,CAAUzD,KAAK,CAACyC,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD,EAHQ,CAKR;;AACA,eAAK1B,YAAL,CAAkBqC,KAAlB;AACA;AAAA;AAAA,4BAAKV,GAAL,CAASC,WAAT,CAAqB,uCAArB;AACH;AAED;AACJ;AACA;;;AACYU,QAAAA,eAAe,GAAY;AAC/B,cAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAKhD,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYiD,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAKtD,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgBuD,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,cAAMC,QAAQ,GAAGJ,IAAI,CAACZ,IAAtB;AACA,cAAMa,kBAAkB,GAAG,KAAKvD,UAAL,CAAgBuD,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,gBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,cAAMC,YAAY,GAAG,KAAK/D,UAAL,CAAgBgE,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAKtE,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH;;AAED,cAAMuE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,cAAME,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,cAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAXsE,CAatE;;AACA,cAAMC,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAK3D,mBAAL,CAAyBN,CAA7C,CAAf;AACA,cAAMwE,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAK7D,mBAAL,CAAyBL,CAA7C,CAAf;AACA,cAAMwE,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB,CAhBsE,CAkBtE;;AACA,cAAMG,QAAQ,GAAG,KAAKC,8BAAL,CAAoCX,QAApC,EAA8CE,QAA9C,CAAjB,CAnBsE,CAqBtE;;AACA,eAAK7D,mBAAL,CAAyBN,CAAzB,GAA6BiE,QAA7B;AACA,eAAK3D,mBAAL,CAAyBL,CAAzB,GAA6BkE,QAA7B,CAvBsE,CAyBtE;;AACA,cAAIQ,QAAQ,CAACE,MAAT,KAAoB,CAAxB,EAA2B;AACvB,iBAAKC,gBAAL;AACA,mBAAO,IAAP;AACH,WA7BqE,CA+BtE;;;AACA,cAAIH,QAAQ,CAACE,MAAT,KAAoB,CAAxB,EAA2B;AACvB,iBAAKC,gBAAL;AACA,mBAAOH,QAAQ,CAAC,CAAD,CAAf;AACH,WAnCqE,CAqCtE;;;AACA,iBAAO,KAAKI,0BAAL,CAAgCJ,QAAhC,EAA0CF,QAA1C,CAAP;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,8BAA8B,CAAC5E,CAAD,EAAYC,CAAZ,EAA4C;AAC9E,cAAM8D,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,cAAMiB,GAAG,GAAGjB,MAAM,CAACkB,gBAAP,CAAwBjF,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,cAAMiF,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,cAAMV,QAA6B,GAAG,EAAtC;AACA,cAAMW,SAAS,GAAG,CAAlB,CAN8E,CAMzD;;AACrB,cAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AAAA,yCAEgD;AAC5C,kBAAIxG,aAAa,CAACyG,QAAd,CAAuBC,cAAvB,CAAsCT,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,oBAAMQ,MAAM,GAAG3G,aAAa,CAACyG,QAAd,CAAuBG,oBAAtC;AACA,oBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB3D,IAAhC;AACA,oBAAM4D,QAAQ,GAAGF,OAAO,CAACzD,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAI2D,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACpB,QAAQ,CAACqB,IAAT,CAAclD,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAc0D,OAApC,CAAL,EAAmD;AAC/CjB,oBAAAA,QAAQ,CAACsB,IAAT,CAAcH,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,sBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACK,OAAb,EAAsB;AAClBL,oBAAAA,QAAQ,CAACK,OAAT,GAAmB,KAAnB;AACAX,oBAAAA,iBAAiB,CAACU,IAAlB,CAAuBJ,QAAvB;AACH;AACJ,iBAZD,MAYO;AAAA;AACI;AACV;AACJ,eApBD,MAoBO;AAAA;AACI;AACV;AACJ,aA1BD;AAAA;;AACA;AACA,iBAAK,IAAIM,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGb,SAA5B,EAAuCa,KAAK,EAA5C;AAAA;AAAA,8BAmBY;AAnBZ;AAyBH,WA3BD,SA2BU;AACN;AACAZ,YAAAA,iBAAiB,CAACa,OAAlB,CAA0BP,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACQ,OAAzB,EAAkC;AAC9BR,gBAAAA,QAAQ,CAACK,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH;;AAED,iBAAOvB,QAAP;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,0BAA0B,CAC9BuB,KAD8B,EAE9BC,YAF8B,EAGN;AACxB,cAAID,KAAK,CAACzB,MAAN,IAAgB,CAApB,EAAuB;AACnB,mBAAOyB,KAAK,CAAC,CAAD,CAAL,IAAY,IAAnB;AACH;;AAED,cAAM1D,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,cAAM4D,UAAU,GAAGD,YAAY,GAAG,KAAK1F,iBAAvC,CANwB,CAQxB;;AACA,cAAI,CAAC2F,UAAL,EAAiB;AACb,iBAAK1B,gBAAL;AACA,mBAAOwB,KAAK,CAAC,CAAD,CAAZ;AACH,WAZuB,CAcxB;;;AACA,cAAI,CAAC,KAAKxF,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyB6B,GAAzB;AACA,iBAAKhC,gBAAL,GAAwB,CAAxB;AACA,iBAAKD,cAAL,GAAsB2F,KAAK,CAACG,GAAN,CAAU3D,IAAI,KAAK;AAAEA,cAAAA,IAAF;AAAQ4D,cAAAA,KAAK,EAAE;AAAf,aAAL,CAAd,CAAtB,CAJwB,CAKxB;;AACA,mBAAOJ,KAAK,CAAC,CAAD,CAAZ;AACH,WAtBuB,CAwBxB;;;AACA,cAAMK,gBAAgB,GAAG/D,GAAG,GAAG,KAAK7B,iBAApC;;AACA,cAAI4F,gBAAgB,GAAG,KAAK1F,oBAA5B,EAAkD;AAC9C;AACA,mBAAOqF,KAAK,CAAC,CAAD,CAAZ;AACH,WA7BuB,CA+BxB;;;AACA,cAAIK,gBAAgB,IAAI,KAAK1F,oBAA7B,EAAmD;AAC/C;AACA,gBAAI,KAAKP,kBAAL,KAA4B,CAAhC,EAAmC;AAC/B,mBAAKA,kBAAL,GAA0BkC,GAA1B;AACAI,cAAAA,OAAO,CAAClB,GAAR,uDACgB,KAAKb,oBADrB,4DACwDqF,KAAK,CAACzB,MAD9D;AAGH,aAP8C,CAS/C;;;AACA,gBAAIjC,GAAG,GAAG,KAAKlC,kBAAX,GAAgC,KAAKD,eAAzC,EAA0D;AAAA;;AACtD,mBAAKG,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8B0F,KAAK,CAACzB,MAA5D;AACA,mBAAKnE,kBAAL,GAA0BkC,GAA1B;AAEAI,cAAAA,OAAO,CAAClB,GAAR,4CACc,KAAKlB,gBAAL,GAAwB,CADtC,kBAC6C,0BAAA0F,KAAK,CAAC,KAAK1F,gBAAN,CAAL,CAA6BsB,IAA7B,2CAAmC0E,IAAnC,KAA2C,IADxF;AAGH;AACJ,WAlDuB,CAoDxB;;;AACA,cAAID,gBAAgB,GAAG,KAAK3F,eAA5B,EAA6C;AACzC,iBAAK8D,gBAAL;AACA,mBAAOwB,KAAK,CAAC,CAAD,CAAZ,CAFyC,CAExB;AACpB;;AAED,iBAAOA,KAAK,CAAC,KAAK1F,gBAAN,CAAL,IAAgC0F,KAAK,CAAC,CAAD,CAA5C;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,kBAAkB,CACtBC,YADsB,EAEtBC,WAFsB,EAGtBC,MAHsB,EAItBC,MAJsB,EAK2B;AACjD,cAAMC,UAAU,GAAG,IAAI9G,GAAJ,EAAnB;AACA,cAAM2D,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC,CAFiD,CAIjD;;AACA,eAAK,IAAMjB,IAAX,IAAmBgE,YAAnB,EAAiC;AAC7B,gBAAMK,QAAQ,GAAGrE,IAAI,CAACZ,IAAL,CAAUkF,aAA3B;AACA,gBAAMC,SAAS,GAAGtD,MAAM,CAACuD,aAAP,CAAqBH,QAArB,CAAlB;AACA,gBAAM1C,QAAQ,GAAGH,IAAI,CAACI,IAAL,CACbJ,IAAI,CAACiD,GAAL,CAASF,SAAS,CAACrH,CAAV,GAAcgH,MAAvB,EAA+B,CAA/B,IAAoC1C,IAAI,CAACiD,GAAL,CAASF,SAAS,CAACpH,CAAV,GAAcgH,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;AAIAC,YAAAA,UAAU,CAACM,GAAX,CAAe1E,IAAI,CAACZ,IAAL,CAAUuF,IAAzB,EAA+B;AAC3B3E,cAAAA,IAD2B;AAE3B4D,cAAAA,KAAK,EAAE,MAAMjC,QAAQ,GAAG,GAFG,CAEE;;AAFF,aAA/B;AAIH,WAhBgD,CAkBjD;;;AACA,eAAK,IAAM3B,KAAX,IAAmBiE,WAAnB,EAAgC;AAC5B,gBAAI,CAACG,UAAU,CAACxD,GAAX,CAAeZ,KAAI,CAACZ,IAAL,CAAUuF,IAAzB,CAAL,EAAqC;AACjC,kBAAMN,SAAQ,GAAGrE,KAAI,CAACZ,IAAL,CAAUkF,aAA3B;;AACA,kBAAMC,UAAS,GAAGtD,MAAM,CAACuD,aAAP,CAAqBH,SAArB,CAAlB;;AACA,kBAAM1C,SAAQ,GAAGH,IAAI,CAACI,IAAL,CACbJ,IAAI,CAACiD,GAAL,CAASF,UAAS,CAACrH,CAAV,GAAcgH,MAAvB,EAA+B,CAA/B,IAAoC1C,IAAI,CAACiD,GAAL,CAASF,UAAS,CAACpH,CAAV,GAAcgH,MAAvB,EAA+B,CAA/B,CADvB,CAAjB;;AAIAC,cAAAA,UAAU,CAACM,GAAX,CAAe1E,KAAI,CAACZ,IAAL,CAAUuF,IAAzB,EAA+B;AAC3B3E,gBAAAA,IAAI,EAAJA,KAD2B;AAE3B4D,gBAAAA,KAAK,EAAE,KAAKjC,SAAQ,GAAG,GAFI,CAEC;;AAFD,eAA/B;AAIH;AACJ;;AAED,iBAAOiD,KAAK,CAACC,IAAN,CAAWT,UAAU,CAACU,MAAX,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,cAAc,CAClBX,UADkB,EAEM;AACxB,cAAIA,UAAU,CAACrC,MAAX,KAAsB,CAA1B,EAA6B;AACzB,mBAAO,IAAP;AACH,WAHuB,CAKxB;;;AACAqC,UAAAA,UAAU,CAACY,IAAX,CAAgB,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAACtB,KAAF,GAAUqB,CAAC,CAACrB,KAAtC;AAEA,cAAMuB,aAAa,GAAGf,UAAU,CAAC,CAAD,CAAhC,CARwB,CAUxB;;AACA,cAAIA,UAAU,CAACrC,MAAX,GAAoB,CAAxB,EAA2B;AACvB,mBAAO,KAAKqD,wBAAL,CAA8BhB,UAA9B,CAAP;AACH;;AAED,iBAAOe,aAAa,CAACnF,IAArB;AACH;AAED;AACJ;AACA;;;AACYoF,QAAAA,wBAAwB,CAC5BhB,UAD4B,EAEJ;AAAA;;AACxB,cAAMtE,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CADwB,CAGxB;;AACA,cAAI,CAAC,KAAK9B,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyB6B,GAAzB;AACA,iBAAKjC,cAAL,GAAsBuG,UAAtB;AACA,iBAAKtG,gBAAL,GAAwB,CAAxB;AACAoC,YAAAA,OAAO,CAAClB,GAAR,qCAAqBoF,UAAU,CAACrC,MAAhC;AACH,WAVuB,CAYxB;;;AACA,cAAIjC,GAAG,GAAG,KAAK7B,iBAAX,GAA+B,KAAKC,eAAxC,EAAyD;AACrD,iBAAK8D,gBAAL;AACA,mBAAOoC,UAAU,CAAC,CAAD,CAAV,CAAcpE,IAArB,CAFqD,CAE1B;AAC9B,WAhBuB,CAkBxB;;;AACA,cAAIF,GAAG,GAAG,KAAKlC,kBAAX,GAAgC,KAAKD,eAAzC,EAA0D;AAAA;;AACtD,iBAAKG,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8BsG,UAAU,CAACrC,MAAjE;AACA,iBAAKnE,kBAAL,GAA0BkC,GAA1B;AAEA,gBAAMuF,WAAW,GAAGjB,UAAU,CAAC,KAAKtG,gBAAN,CAA9B;AACAoC,YAAAA,OAAO,CAAClB,GAAR,gEACmB,0BAAAqG,WAAW,CAACrF,IAAZ,CAAiBZ,IAAjB,2CAAuB0E,IAAvB,KAA+B,IADlD,YAC2D,KAAKhG,gBAAL,GAAwB,CADnF,UACwFsG,UAAU,CAACrC,MADnG;AAGH;;AAED,iBAAO,0BAAAqC,UAAU,CAAC,KAAKtG,gBAAN,CAAV,2CAAmCkC,IAAnC,KAA2CoE,UAAU,CAAC,CAAD,CAAV,CAAcpE,IAAhE;AACH;AAED;AACJ;AACA;;;AACYsF,QAAAA,wBAAwB,CAACpI,CAAD,EAAYC,CAAZ,EAA4C;AACxE,cAAMoI,SAAS,GAAGC,WAAW,CAAC1F,GAAZ,EAAlB;AACA,cAAMmB,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,cAAMiB,GAAG,GAAGjB,MAAM,CAACkB,gBAAP,CAAwBjF,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,cAAMiF,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,cAAMV,QAA6B,GAAG,EAAtC;AACA,cAAMW,SAAS,GAAG,CAAlB,CAPwE,CAOnD;;AACrB,cAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AAAA,2CAEgD;AAC5C,kBAAIxG,aAAa,CAACyG,QAAd,CAAuBC,cAAvB,CAAsCT,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,oBAAMQ,MAAM,GAAG3G,aAAa,CAACyG,QAAd,CAAuBG,oBAAtC;AACA,oBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB3D,IAAhC;AACA,oBAAM4D,QAAQ,GAAGF,OAAO,CAACzD,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAI2D,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACpB,QAAQ,CAACqB,IAAT,CAAclD,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAc0D,OAApC,CAAL,EAAmD;AAC/CjB,oBAAAA,QAAQ,CAACsB,IAAT,CAAcH,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,sBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACK,OAAb,EAAsB;AAClBL,oBAAAA,QAAQ,CAACK,OAAT,GAAmB,KAAnB;AACAX,oBAAAA,iBAAiB,CAACU,IAAlB,CAAuBJ,QAAvB;AACH;AACJ,iBAZD,MAYO;AAAA;AACI;AACV;AACJ,eApBD,MAoBO;AAAA;AACI;AACV;AACJ,aA1BD;AAAA;;AACA;AACA,iBAAK,IAAIM,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGb,SAA5B,EAAuCa,KAAK,EAA5C;AAAA;AAAA,+BAmBY;AAnBZ;AAyBH,WA3BD,SA2BU;AACN;AACAZ,YAAAA,iBAAiB,CAACa,OAAlB,CAA0BP,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACQ,OAAzB,EAAkC;AAC9BR,gBAAAA,QAAQ,CAACK,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH,WA5CuE,CA8CxE;;;AACA,cAAMqC,OAAO,GAAGD,WAAW,CAAC1F,GAAZ,EAAhB;AACA,cAAM4F,QAAQ,GAAGD,OAAO,GAAGF,SAA3B;;AAEA,cAAIG,QAAQ,GAAG,CAAf,EAAkB;AACd;AACAxF,YAAAA,OAAO,CAACC,IAAR,qEACoBuF,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CADpB,kCACgD9D,QAAQ,CAACE,MADzD;AAGH;;AAED,iBAAOF,QAAP;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,gBAAgB,GAAS;AAC7B,cAAI,KAAKhE,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,GAAwB,KAAxB;AACA,iBAAKF,gBAAL,GAAwB,CAAxB;AACA,iBAAKD,cAAL,GAAsB,EAAtB;AACA,iBAAKD,kBAAL,GAA0B,CAA1B,CAJuB,CAIM;;AAC7BsC,YAAAA,OAAO,CAAClB,GAAR,CAAY,kBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACY4G,QAAAA,mBAAmB,GAAS;AAChC,eAAK5D,gBAAL;AACA,eAAKpE,kBAAL,GAA0BiC,IAAI,CAACC,GAAL,KAAa,IAAvC,CAFgC,CAEa;;AAC7CI,UAAAA,OAAO,CAAClB,GAAR;AACH;AAED;AACJ;AACA;;;AACY6G,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKxI,YAAL,CAAkByI,IAAlB,GAAyB,EAA7B,EAAiC;AAC7B,gBAAMhG,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,gBAAMiG,YAAsB,GAAG,EAA/B;AAEA,iBAAK1I,YAAL,CAAkBiG,OAAlB,CAA0B,CAAC0C,KAAD,EAAQC,GAAR,KAAgB;AACtC,kBAAInG,GAAG,GAAGkG,KAAK,CAACE,SAAZ,GAAwB,KAAK3I,mBAAL,GAA2B,CAAvD,EAA0D;AACtDwI,gBAAAA,YAAY,CAAC5C,IAAb,CAAkB8C,GAAlB;AACH;AACJ,aAJD;AAMAF,YAAAA,YAAY,CAACzC,OAAb,CAAqB2C,GAAG,IAAI,KAAK5I,YAAL,CAAkB8I,MAAlB,CAAyBF,GAAzB,CAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACYG,QAAAA,yBAAyB,GAAS;AACtC,cAAMtG,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;;AACA,cAAIA,GAAG,GAAG,KAAKxB,qBAAX,GAAmC,KAAvC,EAA8C;AAC1C;AACA,gBAAM+H,YAAY,GACd,KAAKjI,YAAL,GAAoB,CAApB,GAAyB,KAAKC,aAAL,GAAqB,KAAKD,YAA3B,GAA2C,GAAnE,GAAyE,CAD7E;;AAGA,gBAAI,KAAKA,YAAL,GAAoB,CAAxB,EAA2B;AACvB8B,cAAAA,OAAO,CAAClB,GAAR,wFACwB,KAAKZ,YAD7B,yCACoDiI,YAAY,CAACV,OAAb,CAAqB,CAArB,CADpD;;AAIA,kBAAIU,YAAY,GAAG,EAAnB,EAAuB;AACnBnG,gBAAAA,OAAO,CAACC,IAAR,+DACmBkG,YAAY,CAACV,OAAb,CAAqB,CAArB,CADnB;AAGH;AACJ;;AAED,iBAAKrH,qBAAL,GAA6BwB,GAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACYwG,QAAAA,gBAAgB,CAACtD,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAACD,QAAQ,CAACC,SAAT,CAAmBsD,QAA1D,EAAoE;AAChEvD,YAAAA,QAAQ,CAACC,SAAT,CAAmBsD,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAOvD,QAAQ,CAACwD,OAAhB,KAA4B,UAAhC,EAA4C;AACxCxD,cAAAA,QAAQ,CAACwD,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAACzD,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkCD,QAAQ,CAACC,SAAT,CAAmBsD,QAAzD,EAAmE;AAC/DvD,YAAAA,QAAQ,CAACC,SAAT,CAAmBsD,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAOvD,QAAQ,CAAC0D,aAAhB,KAAkC,UAAtC,EAAkD;AAC9C1D,cAAAA,QAAQ,CAAC0D,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACY/H,QAAAA,YAAY,CAACqC,KAAD,EAAoB;AACpC;AACA,eAAK/D,gBAAL,CAAsBC,CAAtB,GAA0B8D,KAAK,CAACI,YAAN,EAA1B;AACA,eAAKnE,gBAAL,CAAsBE,CAAtB,GAA0B6D,KAAK,CAACM,YAAN,EAA1B;AACA,eAAKvE,YAAL,GAAoB8C,IAAI,CAACC,GAAL,EAApB,CAJoC,CAMpC;;AACA,eAAKkC,gBAAL,GAPoC,CASpC;AACA;;AAEA,cAAM2E,OAAO,GAAG,KAAK5F,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAI2F,OAAJ,EAAa;AACT,iBAAKhK,kBAAL,GAA0BgK,OAA1B;AACA,iBAAKL,gBAAL,CAAsBK,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACY9H,QAAAA,WAAW,CAACmC,KAAD,EAAoB;AACnC,cAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,cAAMqB,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,cAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAI1B,WAAW,GAAG,KAAK7C,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,cAAMuE,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKlE,gBAAL,CAAsBC,CAA1C,CAAf;AACA,cAAMwE,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKpE,gBAAL,CAAsBE,CAA1C,CAAf;AACA,cAAMwE,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAIC,QAAQ,GAAG,KAAKvE,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,cAAI,KAAKT,kBAAL,IAA2BgF,QAAQ,GAAG,KAAKvE,qBAAL,GAA6B,CAAvE,EAA0E;AACtE;AACA,iBAAKL,YAAL,GAAoB6C,WAApB;AACA,iBAAK3C,gBAAL,CAAsBC,CAAtB,GAA0BiE,QAA1B;AACA,iBAAKlE,gBAAL,CAAsBE,CAAtB,GAA0BkE,QAA1B;AACA;AACH,WA1BkC,CA4BnC;;;AACA,eAAKtE,YAAL,GAAoB6C,WAApB;AACA,eAAK3C,gBAAL,CAAsBC,CAAtB,GAA0BiE,QAA1B;AACA,eAAKlE,gBAAL,CAAsBE,CAAtB,GAA0BkE,QAA1B,CA/BmC,CAiCnC;;AACA,cAAMsF,OAAO,GAAG,KAAK5F,oBAAL,CAA0BC,KAA1B,CAAhB,CAlCmC,CAoCnC;;AACA,cAAI,KAAKrE,kBAAL,KAA4BgK,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAKhK,kBAAT,EAA6B;AACzB,mBAAK8J,iBAAL,CAAuB,KAAK9J,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAIgK,OAAJ,EAAa;AACT,mBAAKL,gBAAL,CAAsBK,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAKhK,kBAAL,GAA0BgK,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACY5H,QAAAA,UAAU,CAACiC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAKhD,kBAAT,EAA6B;AACzB,mBAAK8J,iBAAL,CAAuB,KAAK9J,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,cAAMgK,OAAO,GAAG,KAAK5F,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAKrE,kBAAL,IAA2BgK,OAAO,KAAK,KAAKhK,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAK8J,iBAAL,CAAuB,KAAK9J,kBAA5B;AAEA;AAAA;AAAA,8BAAKqC,GAAL,CAASC,WAAT,0DACkB,+BAAKtC,kBAAL,CAAwByC,IAAxB,2CAA8B0E,IAA9B,KAAsC,MADxD,GAJgE,CAQhE;;AACA,gBAAI,KAAK/D,wBAAL,CAA8B,KAAKpD,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqBiD,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAKpD,UAAL,IAAmB,KAAKA,UAAL,CAAgBuD,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAKvD,UAAL,CAAgBuD,kBAAhB,CAAmC2G,UAAnC,CAA8C,KAAKjK,kBAAL,CAAwByC,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,sEACoB,gCAAKtC,kBAAL,CAAwByC,IAAxB,4CAA8B0E,IAA9B,KAAsC,MAD1D,GAFA,CAMA;;AACA,uBAAK8B,mBAAL;AACH,iBARD,CAQE,OAAOiB,KAAP,EAAc;AACZ3G,kBAAAA,OAAO,CAAC2G,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAlBuD,CAoBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKhK,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aAxBD,MAwBO,CACN;AACJ,WAnCD,MAmCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAK8J,iBAAL,CAAuB,KAAK9J,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WAxDiC,CA0DlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AAlsBiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\n\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流 - 增强版\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测\n\n    // 🎯 射线检测缓存优化\n    private raycastCache: Map<string, { items: ItemSceneViewComp[]; timestamp: number }> =\n        new Map();\n    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算\n    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private raycastPositionThreshold: number = 8; // 射线检测位置阈值\n\n    // 🎯 物品切换控制\n    private currentBestItem: ItemSceneViewComp | null = null; // 当前最佳物品\n    private itemSwitchDelay: number = 400; // 物品切换延迟400ms\n    private lastItemSwitchTime: number = 0;\n    private candidateItems: Array<{ item: ItemSceneViewComp; score: number }> = []; // 候选物品及评分\n    private currentItemIndex: number = 0; // 当前物品索引\n\n    // 🎯 慢速移动优化\n    private slowMoveThreshold: number = 1; // 极小移动阈值1px\n    private isInSlowMoveMode: boolean = false;\n    private slowMoveStartTime: number = 0;\n    private maxSlowMoveTime: number = 3000; // 3秒超时\n    private slowMoveRequiredTime: number = 800; // 需要持续慢速移动800ms才触发层级切换\n\n    // 🎯 简单性能监控\n    private raycastCount: number = 0;\n    private cacheHitCount: number = 0;\n    private lastPerformanceReport: number = 0;\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n\n        // 🧹 清理射线检测缓存\n        this.raycastCache.clear();\n        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🚀 简化射线检测 - 专注解决慢速移动问题\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 计算移动距离\n        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);\n        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        // 🎯 总是执行射线检测，获取当前位置的所有物品\n        const allItems = this.performSimpleMultiLayerRaycast(currentX, currentY);\n\n        // 更新位置记录\n        this.lastRaycastPosition.x = currentX;\n        this.lastRaycastPosition.y = currentY;\n\n        // 🎯 如果没有检测到物品，返回null\n        if (allItems.length === 0) {\n            this.exitSlowMoveMode();\n            return null;\n        }\n\n        // 🎯 如果只有一个物品，直接返回\n        if (allItems.length === 1) {\n            this.exitSlowMoveMode();\n            return allItems[0];\n        }\n\n        // 🎯 多个物品时，处理层级切换\n        return this.handleSimpleLayerSwitching(allItems, distance);\n    }\n\n    /**\n     * 🎯 简化多层射线检测 - 只使用单点射线，逐层检测\n     */\n    private performSimpleMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 改进的层级切换 - 需要持续慢速移动才触发\n     */\n    private handleSimpleLayerSwitching(\n        items: ItemSceneViewComp[],\n        moveDistance: number\n    ): ItemSceneViewComp | null {\n        if (items.length <= 1) {\n            return items[0] || null;\n        }\n\n        const now = Date.now();\n        const isSlowMove = moveDistance < this.slowMoveThreshold;\n\n        // 🎯 如果不是慢速移动，直接返回顶层物品\n        if (!isSlowMove) {\n            this.exitSlowMoveMode();\n            return items[0];\n        }\n\n        // 🎯 开始记录慢速移动时间\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.currentItemIndex = 0;\n            this.candidateItems = items.map(item => ({ item, score: 100 }));\n            // 不立即开始切换，先返回顶层物品\n            return items[0];\n        }\n\n        // 🎯 检查是否持续慢速移动足够长时间\n        const slowMoveDuration = now - this.slowMoveStartTime;\n        if (slowMoveDuration < this.slowMoveRequiredTime) {\n            // 还没有持续足够长时间，继续返回顶层物品\n            return items[0];\n        }\n\n        // 🎯 现在开始层级切换\n        if (slowMoveDuration >= this.slowMoveRequiredTime) {\n            // 第一次进入切换模式\n            if (this.lastItemSwitchTime === 0) {\n                this.lastItemSwitchTime = now;\n                console.log(\n                    `🎯 持续慢速移动${this.slowMoveRequiredTime}ms，开始层级切换 (共${items.length}层)`\n                );\n            }\n\n            // 检查是否应该切换到下一层\n            if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n                this.currentItemIndex = (this.currentItemIndex + 1) % items.length;\n                this.lastItemSwitchTime = now;\n\n                console.log(\n                    `🔄 切换到第${this.currentItemIndex + 1}层: ${items[this.currentItemIndex].node?.name || '未知'}`\n                );\n            }\n        }\n\n        // 🎯 检查是否超时\n        if (slowMoveDuration > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return items[0]; // 返回顶层物品\n        }\n\n        return items[this.currentItemIndex] || items[0];\n    }\n\n    /**\n     * 🎯 合并和评分物品\n     */\n    private mergeAndScoreItems(\n        raycastItems: ItemSceneViewComp[],\n        screenItems: ItemSceneViewComp[],\n        touchX: number,\n        touchY: number\n    ): Array<{ item: ItemSceneViewComp; score: number }> {\n        const candidates = new Map<string, { item: ItemSceneViewComp; score: number }>();\n        const camera = smc.camera.CameraModel.camera;\n\n        // 处理射线检测结果（高优先级）\n        for (const item of raycastItems) {\n            const worldPos = item.node.worldPosition;\n            const screenPos = camera.worldToScreen(worldPos);\n            const distance = Math.sqrt(\n                Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n            );\n\n            candidates.set(item.node.uuid, {\n                item,\n                score: 100 - distance * 0.5, // 射线检测基础分100，距离越近分数越高\n            });\n        }\n\n        // 处理屏幕距离检测结果（低优先级）\n        for (const item of screenItems) {\n            if (!candidates.has(item.node.uuid)) {\n                const worldPos = item.node.worldPosition;\n                const screenPos = camera.worldToScreen(worldPos);\n                const distance = Math.sqrt(\n                    Math.pow(screenPos.x - touchX, 2) + Math.pow(screenPos.y - touchY, 2)\n                );\n\n                candidates.set(item.node.uuid, {\n                    item,\n                    score: 50 - distance * 0.3, // 屏幕距离基础分50\n                });\n            }\n        }\n\n        return Array.from(candidates.values());\n    }\n\n    /**\n     * 🎯 选择最佳物品\n     */\n    private selectBestItem(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        if (candidates.length === 0) {\n            return null;\n        }\n\n        // 按分数排序，选择最高分的物品\n        candidates.sort((a, b) => b.score - a.score);\n\n        const bestCandidate = candidates[0];\n\n        // 如果有多个候选物品，可以在这里实现慢速移动时的切换逻辑\n        if (candidates.length > 1) {\n            return this.handleMultipleCandidates(candidates);\n        }\n\n        return bestCandidate.item;\n    }\n\n    /**\n     * 🎯 处理多个候选物品\n     */\n    private handleMultipleCandidates(\n        candidates: Array<{ item: ItemSceneViewComp; score: number }>\n    ): ItemSceneViewComp | null {\n        const now = Date.now();\n\n        // 检查是否为慢速移动模式\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.candidateItems = candidates;\n            this.currentItemIndex = 0;\n            console.log(`🎯 检测到${candidates.length}个候选物品，进入慢速选择模式`);\n        }\n\n        // 检查是否超时\n        if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return candidates[0].item; // 返回最高分物品\n        }\n\n        // 检查是否应该切换\n        if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n            this.currentItemIndex = (this.currentItemIndex + 1) % candidates.length;\n            this.lastItemSwitchTime = now;\n\n            const currentItem = candidates[this.currentItemIndex];\n            console.log(\n                `🔄 切换到候选物品: ${currentItem.item.node?.name || '未知'} (${this.currentItemIndex + 1}/${candidates.length})`\n            );\n        }\n\n        return candidates[this.currentItemIndex]?.item || candidates[0].item;\n    }\n\n    /**\n     * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品\n     */\n    private performMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const startTime = performance.now();\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层，平衡性能和功能\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        // 🎯 记录性能数据\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n\n        if (duration > 3) {\n            // 多层检测阈值稍高\n            console.warn(\n                `⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 退出慢速移动模式\n     */\n    private exitSlowMoveMode(): void {\n        if (this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = false;\n            this.currentItemIndex = 0;\n            this.candidateItems = [];\n            this.lastItemSwitchTime = 0; // 重置切换时间\n            console.log('🏁 退出慢速移动模式，回到顶层');\n        }\n    }\n\n    /**\n     * 🎯 暂停物品切换 - 选择物品后暂停一段时间\n     */\n    private pauseLayerSwitching(): void {\n        this.exitSlowMoveMode();\n        this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒\n        console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);\n    }\n\n    /**\n     * 🎯 清理过期缓存\n     */\n    private cleanupCache(): void {\n        if (this.raycastCache.size > 50) {\n            const now = Date.now();\n            const keysToDelete: string[] = [];\n\n            this.raycastCache.forEach((value, key) => {\n                if (now - value.timestamp > this.raycastCacheTimeout * 2) {\n                    keysToDelete.push(key);\n                }\n            });\n\n            keysToDelete.forEach(key => this.raycastCache.delete(key));\n        }\n    }\n\n    /**\n     * 🎯 性能报告（如果需要）\n     */\n    private reportPerformanceIfNeeded(): void {\n        const now = Date.now();\n        if (now - this.lastPerformanceReport > 10000) {\n            // 每10秒报告一次\n            const cacheHitRate =\n                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;\n\n            if (this.raycastCount > 0) {\n                console.log(\n                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`\n                );\n\n                if (cacheHitRate < 30) {\n                    console.warn(\n                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`\n                    );\n                }\n            }\n\n            this.lastPerformanceReport = now;\n        }\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件\n     */\n    private onTouchStart(event: EventTouch) {\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = Date.now();\n\n        // 🎯 触摸开始时重置慢速移动模式\n        this.exitSlowMoveMode();\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到30FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率\n        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {\n            // 更新位置但不执行射线检测，减少性能消耗\n            this.lastMoveTime = currentTime;\n            this.lastMovePosition.x = currentX;\n            this.lastMovePosition.y = currentY;\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n\n                        // 🎯 选择物品后暂停层级切换\n                        this.pauseLayerSwitching();\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}