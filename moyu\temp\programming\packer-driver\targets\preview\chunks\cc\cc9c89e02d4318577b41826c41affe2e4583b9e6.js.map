{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "PhysicsSystem", "oops", "PHY_GROUP", "smc", "ItemSceneViewComp", "GameSceneViewComp", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "raycastCache", "Map", "raycastCacheTimeout", "lastRaycastPosition", "raycastPositionThreshold", "currentDetectedItems", "currentItemIndex", "lastDetectionPosition", "detectionRadius", "itemSwitchDelay", "lastItemSwitchTime", "slowMoveThreshold", "isInSlowMoveMode", "slowMoveStartTime", "maxSlowMoveTime", "useExpandedDetection", "expandedRadius", "raycastCount", "cacheHitCount", "lastPerformanceReport", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "clear", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "camera", "CameraModel", "currentX", "getLocationX", "currentY", "getLocationY", "deltaX", "Math", "abs", "deltaY", "distance", "sqrt", "isSlowMove", "cache<PERSON>ey", "floor", "cached", "get", "timestamp", "reportPerformanceIfNeeded", "items", "length", "handleItemSwitching", "allItems", "performExpandedAreaDetection", "set", "cleanupCache", "exitSlowMoveMode", "centerX", "centerY", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "foundItems", "Set", "detectionPoints", "startTime", "performance", "point", "ray", "screenPointToRay", "instance", "raycastClosest", "result", "raycastClosestResult", "hitNode", "collider", "itemComp", "ItemModel", "uuid", "push", "add", "endTime", "duration", "toFixed", "currentItem", "name", "performMultiLayerRaycast", "maxLayers", "disabledColliders", "find", "enabled", "layer", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "pauseLayerSwitching", "size", "keysToDelete", "value", "key", "delete", "cacheHitRate", "applyTouchEffect", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "hitItem", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,a,OAAAA,a;;AACjDC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;wCAEaY,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CX,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CY,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,EAbU;AAaN;AAE5C;AAfkD,eAgB1CC,YAhB0C,GAiB9C,IAAIC,GAAJ,EAjB8C;AAAA,eAkB1CC,mBAlB0C,GAkBZ,GAlBY;AAkBP;AAlBO,eAmB1CC,mBAnB0C,GAmBM;AAAEN,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAnBN;AAAA,eAoB1CM,wBApB0C,GAoBP,CApBO;AAoBJ;AAE9C;AAtBkD,eAuB1CC,oBAvB0C,GAuBE,EAvBF;AAuBM;AAvBN,eAwB1CC,gBAxB0C,GAwBf,CAxBe;AAwBZ;AAxBY,eAyB1CC,qBAzB0C,GAyBQ;AAAEV,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAzBR;AAAA,eA0B1CU,eA1B0C,GA0BhB,EA1BgB;AA0BZ;AA1BY,eA2B1CC,eA3B0C,GA2BhB,GA3BgB;AA2BX;AA3BW,eA4B1CC,kBA5B0C,GA4Bb,CA5Ba;AA8BlD;AA9BkD,eA+B1CC,iBA/B0C,GA+Bd,CA/Bc;AA+BX;AA/BW,eAgC1CC,gBAhC0C,GAgCd,KAhCc;AAAA,eAiC1CC,iBAjC0C,GAiCd,CAjCc;AAAA,eAkC1CC,eAlC0C,GAkChB,IAlCgB;AAkCV;AAExC;AApCkD,eAqC1CC,oBArC0C,GAqCV,IArCU;AAqCJ;AArCI,eAsC1CC,cAtC0C,GAsCjB,EAtCiB;AAsCb;AAErC;AAxCkD,eAyC1CC,YAzC0C,GAyCnB,CAzCmB;AAAA,eA0C1CC,aA1C0C,GA0ClB,CA1CkB;AAAA,eA2C1CC,qBA3C0C,GA2CV,CA3CU;AAAA;;AA6ClDC,QAAAA,MAAM,GAAG;AACL1C,UAAAA,KAAK,CAAC2C,EAAN,CAAS1C,KAAK,CAAC2C,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACA9C,UAAAA,KAAK,CAAC2C,EAAN,CAAS1C,KAAK,CAAC2C,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACAhD,UAAAA,KAAK,CAAC2C,EAAN,CAAS1C,KAAK,CAAC2C,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,cAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAK9C,UAAL,GAAkB2C,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,kEAC8C,KAAKzC,UAAL,GAAkB,IAAlB,GAAyB,IADvE;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAKwC,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR3D,UAAAA,KAAK,CAAC4D,GAAN,CAAU3D,KAAK,CAAC2C,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACA9C,UAAAA,KAAK,CAAC4D,GAAN,CAAU3D,KAAK,CAAC2C,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACAhD,UAAAA,KAAK,CAAC4D,GAAN,CAAU3D,KAAK,CAAC2C,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD,EAHQ,CAKR;;AACA,eAAK5B,YAAL,CAAkBuC,KAAlB;AACA;AAAA;AAAA,4BAAKV,GAAL,CAASC,WAAT,CAAqB,uCAArB;AACH;AAED;AACJ;AACA;;;AACYU,QAAAA,eAAe,GAAY;AAC/B,cAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAKlD,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYmD,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAKxD,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgByD,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,cAAMC,QAAQ,GAAGJ,IAAI,CAACZ,IAAtB;AACA,cAAMa,kBAAkB,GAAG,KAAKzD,UAAL,CAAgByD,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,gBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,cAAMC,YAAY,GAAG,KAAKjE,UAAL,CAAgBkE,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAKxE,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH;;AAED,cAAMyE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,cAAME,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,cAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAXsE,CAatE;;AACA,cAAMC,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAK7D,mBAAL,CAAyBN,CAA7C,CAAf;AACA,cAAM0E,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAK/D,mBAAL,CAAyBL,CAA7C,CAAf;AACA,cAAM0E,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;AACA,cAAMG,UAAU,GAAGF,QAAQ,GAAG,KAAK7D,iBAAnC,CAjBsE,CAmBtE;;AACA,cAAMgE,QAAQ,GAAMN,IAAI,CAACO,KAAL,CAAWZ,QAAQ,GAAG,KAAK5D,wBAA3B,CAAN,SAA8DiE,IAAI,CAACO,KAAL,CAAWV,QAAQ,GAAG,KAAK9D,wBAA3B,CAA5E;AACA,cAAMyE,MAAM,GAAG,KAAK7E,YAAL,CAAkB8E,GAAlB,CAAsBH,QAAtB,CAAf;;AAEA,cAAIE,MAAM,IAAInC,IAAI,CAACC,GAAL,KAAakC,MAAM,CAACE,SAApB,GAAgC,KAAK7E,mBAAnD,EAAwE;AACpE,iBAAKgB,aAAL;AACA,iBAAK8D,yBAAL,GAFoE,CAIpE;;AACA,gBAAIN,UAAU,IAAIG,MAAM,CAACI,KAAP,CAAaC,MAAb,GAAsB,CAAxC,EAA2C;AACvC,mBAAK7E,oBAAL,GAA4BwE,MAAM,CAACI,KAAnC;AACA,qBAAO,KAAKE,mBAAL,EAAP;AACH;;AAED,mBAAON,MAAM,CAACI,KAAP,CAAa,CAAb,KAAmB,IAA1B;AACH,WAlCqE,CAoCtE;;;AACA,cAAMG,QAAQ,GAAG,KAAKC,4BAAL,CAAkCrB,QAAlC,EAA4CE,QAA5C,CAAjB,CArCsE,CAuCtE;;AACA,eAAKlE,YAAL,CAAkBsF,GAAlB,CAAsBX,QAAtB,EAAgC;AAAEM,YAAAA,KAAK,EAAEG,QAAT;AAAmBL,YAAAA,SAAS,EAAErC,IAAI,CAACC,GAAL;AAA9B,WAAhC,EAxCsE,CA0CtE;;AACA,eAAK4C,YAAL,GA3CsE,CA6CtE;;AACA,eAAKpF,mBAAL,CAAyBN,CAAzB,GAA6BmE,QAA7B;AACA,eAAK7D,mBAAL,CAAyBL,CAAzB,GAA6BoE,QAA7B,CA/CsE,CAiDtE;;AACA,cAAIQ,UAAU,IAAIU,QAAQ,CAACF,MAAT,GAAkB,CAApC,EAAuC;AACnC,iBAAK7E,oBAAL,GAA4B+E,QAA5B;AACA,mBAAO,KAAKD,mBAAL,EAAP;AACH,WAHD,MAGO;AACH;AACA,iBAAKK,gBAAL;AACH;;AAED,iBAAOJ,QAAQ,CAAC,CAAD,CAAR,IAAe,IAAtB;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,4BAA4B,CAACI,OAAD,EAAkBC,OAAlB,EAAwD;AACxF,cAAM5B,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,cAAM6B,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AACA,cAAMV,QAA6B,GAAG,EAAtC;AACA,cAAMW,UAAU,GAAG,IAAIC,GAAJ,EAAnB,CAJwF,CAIlD;AAEtC;;AACA,cAAMC,eAAe,GAAG,CACpB;AAAEpG,YAAAA,CAAC,EAAE4F,OAAL;AAAc3F,YAAAA,CAAC,EAAE4F;AAAjB,WADoB,EACQ;AAC5B;AAAE7F,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAKzE,cAApB;AAAoClB,YAAAA,CAAC,EAAE4F;AAAvC,WAFoB,EAE8B;AAClD;AAAE7F,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAKzE,cAApB;AAAoClB,YAAAA,CAAC,EAAE4F;AAAvC,WAHoB,EAG8B;AAClD;AAAE7F,YAAAA,CAAC,EAAE4F,OAAL;AAAc3F,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAK1E;AAAhC,WAJoB,EAI8B;AAClD;AAAEnB,YAAAA,CAAC,EAAE4F,OAAL;AAAc3F,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAK1E;AAAhC,WALoB,EAK8B;AAClD;AAAEnB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAKzE,cAApB;AAAoClB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAK1E;AAAtD,WANoB,EAMoD;AACxE;AAAEnB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAKzE,cAApB;AAAoClB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAK1E;AAAtD,WAPoB,EAOoD;AACxE;AAAEnB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAKzE,cAApB;AAAoClB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAK1E;AAAtD,WARoB,EAQoD;AACxE;AAAEnB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAKzE,cAApB;AAAoClB,YAAAA,CAAC,EAAE4F,OAAO,GAAG,KAAK1E;AAAtD,WAToB,CASoD;AATpD,WAAxB;AAYA,cAAMkF,SAAS,GAAGC,WAAW,CAACxD,GAAZ,EAAlB,CAnBwF,CAqBxF;;AACA,eAAK,IAAMyD,KAAX,IAAoBH,eAApB,EAAqC;AACjC,gBAAMI,GAAG,GAAGvC,MAAM,CAACwC,gBAAP,CAAwBF,KAAK,CAACvG,CAA9B,EAAiCuG,KAAK,CAACtG,CAAvC,CAAZ;;AAEA,gBAAIlB,aAAa,CAAC2H,QAAd,CAAuBC,cAAvB,CAAsCH,GAAtC,EAA2CV,WAA3C,CAAJ,EAA6D;AACzD,kBAAMc,MAAM,GAAG7H,aAAa,CAAC2H,QAAd,CAAuBG,oBAAtC;AACA,kBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB3E,IAAhC;AACA,kBAAM4E,QAAQ,GAAGF,OAAO,CAACzE,YAAR;AAAA;AAAA,yDAAjB;;AAEA,kBAAI2E,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAACf,UAAU,CAACtC,GAAX,CAAekD,OAAO,CAACI,IAAvB,CAAvC,EAAqE;AACjE3B,gBAAAA,QAAQ,CAAC4B,IAAT,CAAcH,QAAd;AACAd,gBAAAA,UAAU,CAACkB,GAAX,CAAeN,OAAO,CAACI,IAAvB;AACH;AACJ;AACJ,WAnCuF,CAqCxF;;;AACA,cAAMG,OAAO,GAAGf,WAAW,CAACxD,GAAZ,EAAhB;AACA,cAAMwE,QAAQ,GAAGD,OAAO,GAAGhB,SAA3B;AACA,eAAKjF,YAAL;;AAEA,cAAIkG,QAAQ,GAAG,CAAf,EAAkB;AACdpE,YAAAA,OAAO,CAACC,IAAR,qEACoBmE,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CADpB,kCACgDhC,QAAQ,CAACF,MADzD;AAGH;;AAED,iBAAOE,QAAP;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,mBAAmB,GAA6B;AACpD,cAAI,KAAK9E,oBAAL,CAA0B6E,MAA1B,IAAoC,CAAxC,EAA2C;AACvC,mBAAO,KAAK7E,oBAAL,CAA0B,CAA1B,KAAgC,IAAvC;AACH;;AAED,cAAMsC,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CALoD,CAOpD;;AACA,cAAI,CAAC,KAAK/B,gBAAV,EAA4B;AACxB,iBAAKA,gBAAL,GAAwB,IAAxB;AACA,iBAAKC,iBAAL,GAAyB8B,GAAzB;AACA,iBAAKrC,gBAAL,GAAwB,CAAxB;AACAyC,YAAAA,OAAO,CAAClB,GAAR,uGAAgC,KAAKxB,oBAAL,CAA0B6E,MAA1D;AACH,WAbmD,CAepD;;;AACA,cAAIvC,GAAG,GAAG,KAAK9B,iBAAX,GAA+B,KAAKC,eAAxC,EAAyD;AACrD,iBAAK0E,gBAAL;AACA,mBAAO,KAAKnF,oBAAL,CAA0B,CAA1B,CAAP;AACH,WAnBmD,CAqBpD;;;AACA,cAAIsC,GAAG,GAAG,KAAKjC,kBAAX,GAAgC,KAAKD,eAAzC,EAA0D;AAAA;;AACtD,iBAAKH,gBAAL,GAAwB,CAAC,KAAKA,gBAAL,GAAwB,CAAzB,IAA8B,KAAKD,oBAAL,CAA0B6E,MAAhF;AACA,iBAAKxE,kBAAL,GAA0BiC,GAA1B;AAEA,gBAAM0E,WAAW,GAAG,KAAKhH,oBAAL,CAA0B,KAAKC,gBAA/B,CAApB;AACAyC,YAAAA,OAAO,CAAClB,GAAR,oDACiB,sBAAAwF,WAAW,CAACpF,IAAZ,uCAAkBqF,IAAlB,KAA0B,IAD3C,YACoD,KAAKhH,gBAAL,GAAwB,CAD5E,UACiF,KAAKD,oBAAL,CAA0B6E,MAD3G;AAGH;;AAED,iBAAO,KAAK7E,oBAAL,CAA0B,KAAKC,gBAA/B,KAAoD,KAAKD,oBAAL,CAA0B,CAA1B,CAA3D;AACH;AAED;AACJ;AACA;;;AACYkH,QAAAA,wBAAwB,CAAC1H,CAAD,EAAYC,CAAZ,EAA4C;AACxE,cAAMoG,SAAS,GAAGC,WAAW,CAACxD,GAAZ,EAAlB;AACA,cAAMmB,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,cAAMuC,GAAG,GAAGvC,MAAM,CAACwC,gBAAP,CAAwBzG,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,cAAM6F,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,cAAMV,QAA6B,GAAG,EAAtC;AACA,cAAMoC,SAAS,GAAG,CAAlB,CAPwE,CAOnD;;AACrB,cAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AAAA,yCAEgD;AAC5C,kBAAI7I,aAAa,CAAC2H,QAAd,CAAuBC,cAAvB,CAAsCH,GAAtC,EAA2CV,WAA3C,CAAJ,EAA6D;AACzD,oBAAMc,MAAM,GAAG7H,aAAa,CAAC2H,QAAd,CAAuBG,oBAAtC;AACA,oBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgB3E,IAAhC;AACA,oBAAM4E,QAAQ,GAAGF,OAAO,CAACzE,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAI2E,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAAC1B,QAAQ,CAACsC,IAAT,CAAc7E,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAc0E,OAApC,CAAL,EAAmD;AAC/CvB,oBAAAA,QAAQ,CAAC4B,IAAT,CAAcH,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,sBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACe,OAAb,EAAsB;AAClBf,oBAAAA,QAAQ,CAACe,OAAT,GAAmB,KAAnB;AACAF,oBAAAA,iBAAiB,CAACT,IAAlB,CAAuBJ,QAAvB;AACH;AACJ,iBAZD,MAYO;AAAA;AACI;AACV;AACJ,eApBD,MAoBO;AAAA;AACI;AACV;AACJ,aA1BD;AAAA;;AACA;AACA,iBAAK,IAAIgB,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGJ,SAA5B,EAAuCI,KAAK,EAA5C;AAAA;AAAA,8BAmBY;AAnBZ;AAyBH,WA3BD,SA2BU;AACN;AACAH,YAAAA,iBAAiB,CAACI,OAAlB,CAA0BjB,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACkB,OAAzB,EAAkC;AAC9BlB,gBAAAA,QAAQ,CAACe,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH,WA5CuE,CA8CxE;;;AACA,cAAMT,OAAO,GAAGf,WAAW,CAACxD,GAAZ,EAAhB;AACA,cAAMwE,QAAQ,GAAGD,OAAO,GAAGhB,SAA3B;;AAEA,cAAIiB,QAAQ,GAAG,CAAf,EAAkB;AACd;AACApE,YAAAA,OAAO,CAACC,IAAR,qEACoBmE,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CADpB,kCACgDhC,QAAQ,CAACF,MADzD;AAGH;;AAED,iBAAOE,QAAP;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,gBAAgB,GAAS;AAC7B,cAAI,KAAK5E,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,GAAwB,KAAxB;AACA,iBAAKN,gBAAL,GAAwB,CAAxB;AACA,iBAAKD,oBAAL,GAA4B,EAA5B;AACA0C,YAAAA,OAAO,CAAClB,GAAR,CAAY,kBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYkG,QAAAA,mBAAmB,GAAS;AAChC,eAAKvC,gBAAL;AACA,eAAK9E,kBAAL,GAA0BgC,IAAI,CAACC,GAAL,KAAa,IAAvC,CAFgC,CAEa;;AAC7CI,UAAAA,OAAO,CAAClB,GAAR;AACH;AAED;AACJ;AACA;;;AACY0D,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKvF,YAAL,CAAkBgI,IAAlB,GAAyB,EAA7B,EAAiC;AAC7B,gBAAMrF,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,gBAAMsF,YAAsB,GAAG,EAA/B;AAEA,iBAAKjI,YAAL,CAAkB6H,OAAlB,CAA0B,CAACK,KAAD,EAAQC,GAAR,KAAgB;AACtC,kBAAIxF,GAAG,GAAGuF,KAAK,CAACnD,SAAZ,GAAwB,KAAK7E,mBAAL,GAA2B,CAAvD,EAA0D;AACtD+H,gBAAAA,YAAY,CAACjB,IAAb,CAAkBmB,GAAlB;AACH;AACJ,aAJD;AAMAF,YAAAA,YAAY,CAACJ,OAAb,CAAqBM,GAAG,IAAI,KAAKnI,YAAL,CAAkBoI,MAAlB,CAAyBD,GAAzB,CAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACYnD,QAAAA,yBAAyB,GAAS;AACtC,cAAMrC,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;;AACA,cAAIA,GAAG,GAAG,KAAKxB,qBAAX,GAAmC,KAAvC,EAA8C;AAC1C;AACA,gBAAMkH,YAAY,GACd,KAAKpH,YAAL,GAAoB,CAApB,GAAyB,KAAKC,aAAL,GAAqB,KAAKD,YAA3B,GAA2C,GAAnE,GAAyE,CAD7E;;AAGA,gBAAI,KAAKA,YAAL,GAAoB,CAAxB,EAA2B;AACvB8B,cAAAA,OAAO,CAAClB,GAAR,wFACwB,KAAKZ,YAD7B,yCACoDoH,YAAY,CAACjB,OAAb,CAAqB,CAArB,CADpD;;AAIA,kBAAIiB,YAAY,GAAG,EAAnB,EAAuB;AACnBtF,gBAAAA,OAAO,CAACC,IAAR,+DACmBqF,YAAY,CAACjB,OAAb,CAAqB,CAArB,CADnB;AAGH;AACJ;;AAED,iBAAKjG,qBAAL,GAA6BwB,GAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACY2F,QAAAA,gBAAgB,CAACzB,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAACD,QAAQ,CAACC,SAAT,CAAmByB,QAA1D,EAAoE;AAChE1B,YAAAA,QAAQ,CAACC,SAAT,CAAmByB,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAO1B,QAAQ,CAAC2B,OAAhB,KAA4B,UAAhC,EAA4C;AACxC3B,cAAAA,QAAQ,CAAC2B,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAAC5B,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkCD,QAAQ,CAACC,SAAT,CAAmByB,QAAzD,EAAmE;AAC/D1B,YAAAA,QAAQ,CAACC,SAAT,CAAmByB,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAO1B,QAAQ,CAAC6B,aAAhB,KAAkC,UAAtC,EAAkD;AAC9C7B,cAAAA,QAAQ,CAAC6B,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYlH,QAAAA,YAAY,CAACqC,KAAD,EAAoB;AACpC;AACA,eAAKjE,gBAAL,CAAsBC,CAAtB,GAA0BgE,KAAK,CAACI,YAAN,EAA1B;AACA,eAAKrE,gBAAL,CAAsBE,CAAtB,GAA0B+D,KAAK,CAACM,YAAN,EAA1B;AACA,eAAKzE,YAAL,GAAoBgD,IAAI,CAACC,GAAL,EAApB,CAJoC,CAMpC;;AACA,eAAK6C,gBAAL,GAPoC,CASpC;AACA;;AAEA,cAAMmD,OAAO,GAAG,KAAK/E,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAI8E,OAAJ,EAAa;AACT,iBAAKrJ,kBAAL,GAA0BqJ,OAA1B;AACA,iBAAKL,gBAAL,CAAsBK,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACYjH,QAAAA,WAAW,CAACmC,KAAD,EAAoB;AACnC,cAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,cAAMqB,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,cAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAI1B,WAAW,GAAG,KAAK/C,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,cAAMyE,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKpE,gBAAL,CAAsBC,CAA1C,CAAf;AACA,cAAM0E,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKtE,gBAAL,CAAsBE,CAA1C,CAAf;AACA,cAAM0E,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAIC,QAAQ,GAAG,KAAKzE,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,cAAI,KAAKT,kBAAL,IAA2BkF,QAAQ,GAAG,KAAKzE,qBAAL,GAA6B,CAAvE,EAA0E;AACtE;AACA,iBAAKL,YAAL,GAAoB+C,WAApB;AACA,iBAAK7C,gBAAL,CAAsBC,CAAtB,GAA0BmE,QAA1B;AACA,iBAAKpE,gBAAL,CAAsBE,CAAtB,GAA0BoE,QAA1B;AACA;AACH,WA1BkC,CA4BnC;;;AACA,eAAKxE,YAAL,GAAoB+C,WAApB;AACA,eAAK7C,gBAAL,CAAsBC,CAAtB,GAA0BmE,QAA1B;AACA,eAAKpE,gBAAL,CAAsBE,CAAtB,GAA0BoE,QAA1B,CA/BmC,CAiCnC;;AACA,cAAMyE,OAAO,GAAG,KAAK/E,oBAAL,CAA0BC,KAA1B,CAAhB,CAlCmC,CAoCnC;;AACA,cAAI,KAAKvE,kBAAL,KAA4BqJ,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAKrJ,kBAAT,EAA6B;AACzB,mBAAKmJ,iBAAL,CAAuB,KAAKnJ,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAIqJ,OAAJ,EAAa;AACT,mBAAKL,gBAAL,CAAsBK,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAKrJ,kBAAL,GAA0BqJ,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACY/G,QAAAA,UAAU,CAACiC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAKlD,kBAAT,EAA6B;AACzB,mBAAKmJ,iBAAL,CAAuB,KAAKnJ,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,cAAMqJ,OAAO,GAAG,KAAK/E,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAKvE,kBAAL,IAA2BqJ,OAAO,KAAK,KAAKrJ,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAKmJ,iBAAL,CAAuB,KAAKnJ,kBAA5B;AAEA;AAAA;AAAA,8BAAKuC,GAAL,CAASC,WAAT,0DACkB,+BAAKxC,kBAAL,CAAwB2C,IAAxB,2CAA8BqF,IAA9B,KAAsC,MADxD,GAJgE,CAQhE;;AACA,gBAAI,KAAK1E,wBAAL,CAA8B,KAAKtD,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqBmD,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAKtD,UAAL,IAAmB,KAAKA,UAAL,CAAgByD,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAKzD,UAAL,CAAgByD,kBAAhB,CAAmC8F,UAAnC,CAA8C,KAAKtJ,kBAAL,CAAwB2C,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,sEACoB,gCAAKxC,kBAAL,CAAwB2C,IAAxB,4CAA8BqF,IAA9B,KAAsC,MAD1D,GAFA,CAMA;;AACA,uBAAKS,mBAAL;AACH,iBARD,CAQE,OAAOc,KAAP,EAAc;AACZ9F,kBAAAA,OAAO,CAAC8F,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAlBuD,CAoBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKrJ,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aAxBD,MAwBO,CACN;AACJ,WAnCD,MAmCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAKmJ,iBAAL,CAAuB,KAAKnJ,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WAxDiC,CA0DlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AAtlBiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\n\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流 - 增强版\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测\n\n    // 🎯 射线检测缓存优化\n    private raycastCache: Map<string, { items: ItemSceneViewComp[]; timestamp: number }> =\n        new Map();\n    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算\n    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private raycastPositionThreshold: number = 8; // 射线检测位置阈值\n\n    // 🎯 智能区域检测系统\n    private currentDetectedItems: ItemSceneViewComp[] = []; // 当前检测到的所有物品\n    private currentItemIndex: number = 0; // 当前选中的物品索引\n    private lastDetectionPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private detectionRadius: number = 25; // 检测半径25px\n    private itemSwitchDelay: number = 600; // 物品切换延迟600ms\n    private lastItemSwitchTime: number = 0;\n\n    // 🎯 慢速移动检测\n    private slowMoveThreshold: number = 8; // 慢速移动阈值8px\n    private isInSlowMoveMode: boolean = false;\n    private slowMoveStartTime: number = 0;\n    private maxSlowMoveTime: number = 4000; // 最大慢速移动时间4秒\n\n    // 🎯 区域检测优化\n    private useExpandedDetection: boolean = true; // 使用扩展检测\n    private expandedRadius: number = 15; // 扩展检测半径15px\n\n    // 🎯 简单性能监控\n    private raycastCount: number = 0;\n    private cacheHitCount: number = 0;\n    private lastPerformanceReport: number = 0;\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n\n        // 🧹 清理射线检测缓存\n        this.raycastCache.clear();\n        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🚀 优化版射线检测 - 带缓存和智能跳过\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 检查是否为慢速移动（可能想要切换到下层模型）\n        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);\n        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n        const isSlowMove = distance < this.slowMoveThreshold;\n\n        // 🎯 检查缓存\n        const cacheKey = `${Math.floor(currentX / this.raycastPositionThreshold)}_${Math.floor(currentY / this.raycastPositionThreshold)}`;\n        const cached = this.raycastCache.get(cacheKey);\n\n        if (cached && Date.now() - cached.timestamp < this.raycastCacheTimeout) {\n            this.cacheHitCount++;\n            this.reportPerformanceIfNeeded();\n\n            // 🎯 如果是慢速移动且有多个物品，使用缓存的物品进行切换\n            if (isSlowMove && cached.items.length > 1) {\n                this.currentDetectedItems = cached.items;\n                return this.handleItemSwitching();\n            }\n\n            return cached.items[0] || null;\n        }\n\n        // 🎯 执行扩展区域检测\n        const allItems = this.performExpandedAreaDetection(currentX, currentY);\n\n        // 🎯 更新缓存\n        this.raycastCache.set(cacheKey, { items: allItems, timestamp: Date.now() });\n\n        // 🎯 清理过期缓存\n        this.cleanupCache();\n\n        // 更新最后检测位置\n        this.lastRaycastPosition.x = currentX;\n        this.lastRaycastPosition.y = currentY;\n\n        // 🎯 返回合适的物品\n        if (isSlowMove && allItems.length > 1) {\n            this.currentDetectedItems = allItems;\n            return this.handleItemSwitching();\n        } else {\n            // 🎯 快速移动时退出慢速模式，回到顶层\n            this.exitSlowMoveMode();\n        }\n\n        return allItems[0] || null;\n    }\n\n    /**\n     * 🎯 扩展区域检测 - 使用多点射线检测更大区域\n     */\n    private performExpandedAreaDetection(centerX: number, centerY: number): ItemSceneViewComp[] {\n        const camera = smc.camera.CameraModel.camera;\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n        const allItems: ItemSceneViewComp[] = [];\n        const foundItems = new Set<string>(); // 避免重复，使用节点名称\n\n        // 🎯 检测点配置：中心点 + 周围8个点\n        const detectionPoints = [\n            { x: centerX, y: centerY }, // 中心点\n            { x: centerX - this.expandedRadius, y: centerY }, // 左\n            { x: centerX + this.expandedRadius, y: centerY }, // 右\n            { x: centerX, y: centerY - this.expandedRadius }, // 上\n            { x: centerX, y: centerY + this.expandedRadius }, // 下\n            { x: centerX - this.expandedRadius, y: centerY - this.expandedRadius }, // 左上\n            { x: centerX + this.expandedRadius, y: centerY - this.expandedRadius }, // 右上\n            { x: centerX - this.expandedRadius, y: centerY + this.expandedRadius }, // 左下\n            { x: centerX + this.expandedRadius, y: centerY + this.expandedRadius }, // 右下\n        ];\n\n        const startTime = performance.now();\n\n        // 🎯 对每个检测点进行射线检测\n        for (const point of detectionPoints) {\n            const ray = camera.screenPointToRay(point.x, point.y);\n\n            if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                const result = PhysicsSystem.instance.raycastClosestResult;\n                const hitNode = result.collider.node;\n                const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                if (itemComp && itemComp.ItemModel && !foundItems.has(hitNode.uuid)) {\n                    allItems.push(itemComp);\n                    foundItems.add(hitNode.uuid);\n                }\n            }\n        }\n\n        // 🎯 记录性能\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        this.raycastCount++;\n\n        if (duration > 4) {\n            console.warn(\n                `⚠️ 扩展区域检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 智能物品切换 - 在当前区域的物品间切换\n     */\n    private handleItemSwitching(): ItemSceneViewComp | null {\n        if (this.currentDetectedItems.length <= 1) {\n            return this.currentDetectedItems[0] || null;\n        }\n\n        const now = Date.now();\n\n        // 🎯 检查是否刚进入慢速移动模式\n        if (!this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = true;\n            this.slowMoveStartTime = now;\n            this.currentItemIndex = 0;\n            console.log(`🎯 进入慢速移动模式，当前区域有${this.currentDetectedItems.length}个物品`);\n        }\n\n        // 🎯 检查是否超时，自动退出\n        if (now - this.slowMoveStartTime > this.maxSlowMoveTime) {\n            this.exitSlowMoveMode();\n            return this.currentDetectedItems[0];\n        }\n\n        // 🎯 检查是否应该切换到下一个物品\n        if (now - this.lastItemSwitchTime > this.itemSwitchDelay) {\n            this.currentItemIndex = (this.currentItemIndex + 1) % this.currentDetectedItems.length;\n            this.lastItemSwitchTime = now;\n\n            const currentItem = this.currentDetectedItems[this.currentItemIndex];\n            console.log(\n                `🔄 切换到物品: ${currentItem.node?.name || '未知'} (${this.currentItemIndex + 1}/${this.currentDetectedItems.length})`\n            );\n        }\n\n        return this.currentDetectedItems[this.currentItemIndex] || this.currentDetectedItems[0];\n    }\n\n    /**\n     * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品\n     */\n    private performMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const startTime = performance.now();\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层，平衡性能和功能\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        // 🎯 记录性能数据\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n\n        if (duration > 3) {\n            // 多层检测阈值稍高\n            console.warn(\n                `⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 退出慢速移动模式\n     */\n    private exitSlowMoveMode(): void {\n        if (this.isInSlowMoveMode) {\n            this.isInSlowMoveMode = false;\n            this.currentItemIndex = 0;\n            this.currentDetectedItems = [];\n            console.log('🏁 退出慢速移动模式，回到顶层');\n        }\n    }\n\n    /**\n     * 🎯 暂停物品切换 - 选择物品后暂停一段时间\n     */\n    private pauseLayerSwitching(): void {\n        this.exitSlowMoveMode();\n        this.lastItemSwitchTime = Date.now() + 1000; // 暂停1秒\n        console.log(`⏸️ 物品已选择，暂停物品切换 1000ms`);\n    }\n\n    /**\n     * 🎯 清理过期缓存\n     */\n    private cleanupCache(): void {\n        if (this.raycastCache.size > 50) {\n            const now = Date.now();\n            const keysToDelete: string[] = [];\n\n            this.raycastCache.forEach((value, key) => {\n                if (now - value.timestamp > this.raycastCacheTimeout * 2) {\n                    keysToDelete.push(key);\n                }\n            });\n\n            keysToDelete.forEach(key => this.raycastCache.delete(key));\n        }\n    }\n\n    /**\n     * 🎯 性能报告（如果需要）\n     */\n    private reportPerformanceIfNeeded(): void {\n        const now = Date.now();\n        if (now - this.lastPerformanceReport > 10000) {\n            // 每10秒报告一次\n            const cacheHitRate =\n                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;\n\n            if (this.raycastCount > 0) {\n                console.log(\n                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`\n                );\n\n                if (cacheHitRate < 30) {\n                    console.warn(\n                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`\n                    );\n                }\n            }\n\n            this.lastPerformanceReport = now;\n        }\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件\n     */\n    private onTouchStart(event: EventTouch) {\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = Date.now();\n\n        // 🎯 触摸开始时重置慢速移动模式\n        this.exitSlowMoveMode();\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到30FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率\n        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {\n            // 更新位置但不执行射线检测，减少性能消耗\n            this.lastMoveTime = currentTime;\n            this.lastMovePosition.x = currentX;\n            this.lastMovePosition.y = currentY;\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n\n                        // 🎯 选择物品后暂停层级切换\n                        this.pauseLayerSwitching();\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}