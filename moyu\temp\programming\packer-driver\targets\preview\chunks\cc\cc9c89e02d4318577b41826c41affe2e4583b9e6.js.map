{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/ItemInteractionManager.ts"], "names": ["_decorator", "Component", "input", "Input", "PhysicsSystem", "oops", "PHY_GROUP", "smc", "ItemSceneViewComp", "GameSceneViewComp", "ccclass", "property", "ItemInteractionManager", "gameEntity", "currentTouchedItem", "lastClickTime", "clickDelay", "isProcessingClick", "lastMoveTime", "moveThrottleDelay", "lastMovePosition", "x", "y", "moveDistanceThreshold", "raycastCache", "Map", "raycastCacheTimeout", "lastRaycastPosition", "raycastPositionThreshold", "currentLayerIndex", "layerSwitchDelay", "lastLayerSwitchTime", "slowMoveThreshold", "slowMoveDetectionTime", "raycastCount", "cacheHitCount", "lastPerformanceReport", "onLoad", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "log", "logBusiness", "start", "gameSceneViewComp", "node", "getComponent", "ent", "logError", "onDestroy", "off", "clear", "canProcessClick", "currentTime", "Date", "now", "intelligentItemSelection", "item", "interactionManager", "console", "warn", "itemNode", "slotManager", "slot", "getSlotByItem", "index", "isSelectable", "GameModel", "allItemsToPick", "has", "getItemId", "canAddItem", "detectItemAtPosition", "event", "camera", "CameraModel", "currentX", "getLocationX", "currentY", "getLocationY", "deltaX", "Math", "abs", "deltaY", "distance", "sqrt", "isSlowMove", "cache<PERSON>ey", "floor", "cached", "get", "timestamp", "reportPerformanceIfNeeded", "items", "length", "handleLayerSwitching", "allItems", "performMultiLayerRaycast", "set", "cleanupCache", "startTime", "performance", "ray", "screenPointToRay", "raycastMask", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "maxLayers", "disabledColliders", "instance", "raycastClosest", "result", "raycastClosestResult", "hitNode", "collider", "itemComp", "ItemModel", "find", "push", "enabled", "layer", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "endTime", "duration", "toFixed", "name", "size", "keysToDelete", "value", "key", "delete", "cacheHitRate", "applyTouchEffect", "touching", "onTouch", "cancelTouchEffect", "onCancelTouch", "hitItem", "chooseItem", "error", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,a,OAAAA,a;;AACjDC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;wCAEaY,sB,WADZF,OAAO,CAAC,wBAAD,C,gBAAR,MACaE,sBADb,SAC4CX,SAD5C,CACsD;AAAA;AAAA;AAAA,eAC1CY,UAD0C,GACV,IADU;AAAA,eAE1CC,kBAF0C,GAEK,IAFL;AAIlD;AAJkD,eAK1CC,aAL0C,GAKlB,CALkB;AAAA,eAM1CC,UAN0C,GAMrB,GANqB;AAMhB;AANgB,eAO1CC,iBAP0C,GAOb,KAPa;AASlD;AATkD,eAU1CC,YAV0C,GAUnB,CAVmB;AAAA,eAW1CC,iBAX0C,GAWd,EAXc;AAWV;AAXU,eAY1CC,gBAZ0C,GAYG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAZH;AAAA,eAa1CC,qBAb0C,GAaV,EAbU;AAaN;AAE5C;AAfkD,eAgB1CC,YAhB0C,GAiB9C,IAAIC,GAAJ,EAjB8C;AAAA,eAkB1CC,mBAlB0C,GAkBZ,GAlBY;AAkBP;AAlBO,eAmB1CC,mBAnB0C,GAmBM;AAAEN,YAAAA,CAAC,EAAE,CAAC,CAAN;AAASC,YAAAA,CAAC,EAAE,CAAC;AAAb,WAnBN;AAAA,eAoB1CM,wBApB0C,GAoBP,CApBO;AAoBJ;AAE9C;AAtBkD,eAuB1CC,iBAvB0C,GAuBd,CAvBc;AAuBX;AAvBW,eAwB1CC,gBAxB0C,GAwBf,GAxBe;AAwBV;AAxBU,eAyB1CC,mBAzB0C,GAyBZ,CAzBY;AAAA,eA0B1CC,iBA1B0C,GA0Bd,CA1Bc;AA0BX;AA1BW,eA2B1CC,qBA3B0C,GA2BV,GA3BU;AA2BL;AAE7C;AA7BkD,eA8B1CC,YA9B0C,GA8BnB,CA9BmB;AAAA,eA+B1CC,aA/B0C,GA+BlB,CA/BkB;AAAA,eAgC1CC,qBAhC0C,GAgCV,CAhCU;AAAA;;AAkClDC,QAAAA,MAAM,GAAG;AACLnC,UAAAA,KAAK,CAACoC,EAAN,CAASnC,KAAK,CAACoC,SAAN,CAAgBC,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACAvC,UAAAA,KAAK,CAACoC,EAAN,CAASnC,KAAK,CAACoC,SAAN,CAAgBG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACAzC,UAAAA,KAAK,CAACoC,EAAN,CAASnC,KAAK,CAACoC,SAAN,CAAgBK,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,cAAMC,iBAAiB,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAA1B;;AACA,cAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,GAA3C,EAAgD;AAC5C,iBAAKvC,UAAL,GAAkBoC,iBAAiB,CAACG,GAApC;AACA;AAAA;AAAA,8BAAKN,GAAL,CAASC,WAAT,kEAC8C,KAAKlC,UAAL,GAAkB,IAAlB,GAAyB,IADvE;AAGH,WALD,MAKO;AACH;AAAA;AAAA,8BAAKiC,GAAL,CAASO,QAAT,CAAkB,0DAAlB;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACRpD,UAAAA,KAAK,CAACqD,GAAN,CAAUpD,KAAK,CAACoC,SAAN,CAAgBC,WAA1B,EAAuC,KAAKC,YAA5C,EAA0D,IAA1D;AACAvC,UAAAA,KAAK,CAACqD,GAAN,CAAUpD,KAAK,CAACoC,SAAN,CAAgBG,UAA1B,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACAzC,UAAAA,KAAK,CAACqD,GAAN,CAAUpD,KAAK,CAACoC,SAAN,CAAgBK,SAA1B,EAAqC,KAAKC,UAA1C,EAAsD,IAAtD,EAHQ,CAKR;;AACA,eAAKrB,YAAL,CAAkBgC,KAAlB;AACA;AAAA;AAAA,4BAAKV,GAAL,CAASC,WAAT,CAAqB,uCAArB;AACH;AAED;AACJ;AACA;;;AACYU,QAAAA,eAAe,GAAY;AAC/B,cAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB,CAD+B,CAG/B;;AACA,cAAIF,WAAW,GAAG,KAAK3C,aAAnB,GAAmC,KAAKC,UAA5C,EAAwD;AACpD;AACA,mBAAO,KAAP;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACY4C,QAAAA,wBAAwB,CAACC,IAAD,EAAmC;AAC/D,cAAI,CAAC,KAAKjD,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgBkD,kBAAzC,EAA6D;AACzDC,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA,mBAAO,KAAP;AACH;;AAED,cAAMC,QAAQ,GAAGJ,IAAI,CAACZ,IAAtB;AACA,cAAMa,kBAAkB,GAAG,KAAKlD,UAAL,CAAgBkD,kBAA3C,CAP+D,CAS/D;;AACA,cAAIA,kBAAkB,CAACI,WAAvB,EAAoC;AAChC,gBAAMC,IAAI,GAAGL,kBAAkB,CAACI,WAAnB,CAA+BE,aAA/B,CAA6CH,QAA7C,CAAb;;AAEA,gBAAIE,IAAJ,EAAU;AACN;AACA,kBAAIA,IAAI,CAACE,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH;AACA;AACA,uBAAO,KAAP;AACH;AACJ;AACJ,WAzB8D,CA2B/D;;;AACA,cAAMC,YAAY,GAAG,KAAK1D,UAAL,CAAgB2D,SAAhB,CAA0BC,cAA1B,CAAyCC,GAAzC,CAA6CZ,IAAI,CAACa,SAAL,EAA7C,CAArB;;AACA,cAAI,CAACJ,YAAL,EAAmB;AACf;AACA,mBAAO,KAAP;AACH,WAhC8D,CAkC/D;;;AACA,cAAIR,kBAAkB,CAACI,WAAnB,IAAkC,CAACJ,kBAAkB,CAACI,WAAnB,CAA+BS,UAA/B,EAAvC,EAAoF;AAChF;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,CAACC,KAAD,EAA8C;AACtE,cAAI,CAAC,KAAKjE,UAAV,EAAsB;AAClB,mBAAO,IAAP;AACH;;AAED,cAAMkE,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT,mBAAO,IAAP;AACH;;AAED,cAAME,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,cAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAXsE,CAatE;;AACA,cAAMC,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAKtD,mBAAL,CAAyBN,CAA7C,CAAf;AACA,cAAMmE,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAKxD,mBAAL,CAAyBL,CAA7C,CAAf;AACA,cAAMmE,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;AACA,cAAMG,UAAU,GAAGF,QAAQ,GAAG,KAAKzD,iBAAnC,CAjBsE,CAmBtE;;AACA,cAAM4D,QAAQ,GAAMN,IAAI,CAACO,KAAL,CAAWZ,QAAQ,GAAG,KAAKrD,wBAA3B,CAAN,SAA8D0D,IAAI,CAACO,KAAL,CAAWV,QAAQ,GAAG,KAAKvD,wBAA3B,CAA5E;AACA,cAAMkE,MAAM,GAAG,KAAKtE,YAAL,CAAkBuE,GAAlB,CAAsBH,QAAtB,CAAf;;AAEA,cAAIE,MAAM,IAAInC,IAAI,CAACC,GAAL,KAAakC,MAAM,CAACE,SAApB,GAAgC,KAAKtE,mBAAnD,EAAwE;AACpE,iBAAKS,aAAL;AACA,iBAAK8D,yBAAL,GAFoE,CAIpE;;AACA,gBAAIN,UAAU,IAAIG,MAAM,CAACI,KAAP,CAAaC,MAAb,GAAsB,CAAxC,EAA2C;AACvC,qBAAO,KAAKC,oBAAL,CAA0BN,MAAM,CAACI,KAAjC,CAAP;AACH;;AAED,mBAAOJ,MAAM,CAACI,KAAP,CAAa,CAAb,KAAmB,IAA1B;AACH,WAjCqE,CAmCtE;;;AACA,cAAMG,QAAQ,GAAG,KAAKC,wBAAL,CAA8BrB,QAA9B,EAAwCE,QAAxC,CAAjB,CApCsE,CAsCtE;;AACA,eAAK3D,YAAL,CAAkB+E,GAAlB,CAAsBX,QAAtB,EAAgC;AAAEM,YAAAA,KAAK,EAAEG,QAAT;AAAmBL,YAAAA,SAAS,EAAErC,IAAI,CAACC,GAAL;AAA9B,WAAhC,EAvCsE,CAyCtE;;AACA,eAAK4C,YAAL,GA1CsE,CA4CtE;;AACA,eAAK7E,mBAAL,CAAyBN,CAAzB,GAA6B4D,QAA7B;AACA,eAAKtD,mBAAL,CAAyBL,CAAzB,GAA6B6D,QAA7B,CA9CsE,CAgDtE;;AACA,cAAIQ,UAAU,IAAIU,QAAQ,CAACF,MAAT,GAAkB,CAApC,EAAuC;AACnC,mBAAO,KAAKC,oBAAL,CAA0BC,QAA1B,CAAP;AACH,WAFD,MAEO;AACH;AACA,iBAAKxE,iBAAL,GAAyB,CAAzB;AACH;;AAED,iBAAOwE,QAAQ,CAAC,CAAD,CAAR,IAAe,IAAtB;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,wBAAwB,CAACjF,CAAD,EAAYC,CAAZ,EAA4C;AACxE,cAAMmF,SAAS,GAAGC,WAAW,CAAC9C,GAAZ,EAAlB;AACA,cAAMmB,MAAM,GAAG;AAAA;AAAA,0BAAIA,MAAJ,CAAWC,WAAX,CAAuBD,MAAtC;AACA,cAAM4B,GAAG,GAAG5B,MAAM,CAAC6B,gBAAP,CAAwBvF,CAAxB,EAA2BC,CAA3B,CAAZ;AACA,cAAMuF,WAAW,GAAG;AAAA;AAAA,sCAAUC,IAAV,GAAiB;AAAA;AAAA,sCAAUC,QAA3B,GAAsC;AAAA;AAAA,sCAAUC,cAApE;AAEA,cAAMX,QAA6B,GAAG,EAAtC;AACA,cAAMY,SAAS,GAAG,CAAlB,CAPwE,CAOnD;;AACrB,cAAMC,iBAAwB,GAAG,EAAjC;;AAEA,cAAI;AAAA,yCAEgD;AAC5C,kBAAI9G,aAAa,CAAC+G,QAAd,CAAuBC,cAAvB,CAAsCT,GAAtC,EAA2CE,WAA3C,CAAJ,EAA6D;AACzD,oBAAMQ,MAAM,GAAGjH,aAAa,CAAC+G,QAAd,CAAuBG,oBAAtC;AACA,oBAAMC,OAAO,GAAGF,MAAM,CAACG,QAAP,CAAgBtE,IAAhC;AACA,oBAAMuE,QAAQ,GAAGF,OAAO,CAACpE,YAAR;AAAA;AAAA,2DAAjB;;AAEA,oBAAIsE,QAAQ,IAAIA,QAAQ,CAACC,SAAzB,EAAoC;AAChC;AACA,sBAAI,CAACrB,QAAQ,CAACsB,IAAT,CAAc7D,IAAI,IAAIA,IAAI,CAACZ,IAAL,KAAcqE,OAApC,CAAL,EAAmD;AAC/ClB,oBAAAA,QAAQ,CAACuB,IAAT,CAAcH,QAAd;AACH,mBAJ+B,CAMhC;;;AACA,sBAAMD,QAAQ,GAAGH,MAAM,CAACG,QAAxB;;AACA,sBAAIA,QAAQ,CAACK,OAAb,EAAsB;AAClBL,oBAAAA,QAAQ,CAACK,OAAT,GAAmB,KAAnB;AACAX,oBAAAA,iBAAiB,CAACU,IAAlB,CAAuBJ,QAAvB;AACH;AACJ,iBAZD,MAYO;AAAA;AACI;AACV;AACJ,eApBD,MAoBO;AAAA;AACI;AACV;AACJ,aA1BD;AAAA;;AACA;AACA,iBAAK,IAAIM,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGb,SAA5B,EAAuCa,KAAK,EAA5C;AAAA;AAAA,8BAmBY;AAnBZ;AAyBH,WA3BD,SA2BU;AACN;AACAZ,YAAAA,iBAAiB,CAACa,OAAlB,CAA0BP,QAAQ,IAAI;AAClC,kBAAIA,QAAQ,IAAIA,QAAQ,CAACQ,OAAzB,EAAkC;AAC9BR,gBAAAA,QAAQ,CAACK,OAAT,GAAmB,IAAnB;AACH;AACJ,aAJD;AAKH,WA5CuE,CA8CxE;;;AACA,cAAMI,OAAO,GAAGvB,WAAW,CAAC9C,GAAZ,EAAhB;AACA,cAAMsE,QAAQ,GAAGD,OAAO,GAAGxB,SAA3B;;AAEA,cAAIyB,QAAQ,GAAG,CAAf,EAAkB;AACd;AACAlE,YAAAA,OAAO,CAACC,IAAR,qEACoBiE,QAAQ,CAACC,OAAT,CAAiB,CAAjB,CADpB,kCACgD9B,QAAQ,CAACF,MADzD;AAGH;;AAED,iBAAOE,QAAP;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,oBAAoB,CAACF,KAAD,EAAuD;AAC/E,cAAIA,KAAK,CAACC,MAAN,IAAgB,CAApB,EAAuB;AACnB,mBAAOD,KAAK,CAAC,CAAD,CAAL,IAAY,IAAnB;AACH;;AAED,cAAMtC,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ,CAL+E,CAO/E;;AACA,cAAIA,GAAG,GAAG,KAAK7B,mBAAX,GAAiC,KAAKD,gBAA1C,EAA4D;AAAA;;AACxD,iBAAKD,iBAAL,GAAyB,CAAC,KAAKA,iBAAL,GAAyB,CAA1B,IAA+BqE,KAAK,CAACC,MAA9D;AACA,iBAAKpE,mBAAL,GAA2B6B,GAA3B;AAEAI,YAAAA,OAAO,CAAClB,GAAR,4CACc,KAAKjB,iBAAL,GAAyB,CADvC,8BACgD,0BAAAqE,KAAK,CAAC,KAAKrE,iBAAN,CAAL,CAA8BqB,IAA9B,2CAAoCkF,IAApC,KAA4C,IAD5F;AAGH;;AAED,iBAAOlC,KAAK,CAAC,KAAKrE,iBAAN,CAAL,IAAiCqE,KAAK,CAAC,CAAD,CAA7C;AACH;AAED;AACJ;AACA;;;AACYM,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKhF,YAAL,CAAkB6G,IAAlB,GAAyB,EAA7B,EAAiC;AAC7B,gBAAMzE,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;AACA,gBAAM0E,YAAsB,GAAG,EAA/B;AAEA,iBAAK9G,YAAL,CAAkBuG,OAAlB,CAA0B,CAACQ,KAAD,EAAQC,GAAR,KAAgB;AACtC,kBAAI5E,GAAG,GAAG2E,KAAK,CAACvC,SAAZ,GAAwB,KAAKtE,mBAAL,GAA2B,CAAvD,EAA0D;AACtD4G,gBAAAA,YAAY,CAACV,IAAb,CAAkBY,GAAlB;AACH;AACJ,aAJD;AAMAF,YAAAA,YAAY,CAACP,OAAb,CAAqBS,GAAG,IAAI,KAAKhH,YAAL,CAAkBiH,MAAlB,CAAyBD,GAAzB,CAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACYvC,QAAAA,yBAAyB,GAAS;AACtC,cAAMrC,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;;AACA,cAAIA,GAAG,GAAG,KAAKxB,qBAAX,GAAmC,KAAvC,EAA8C;AAC1C;AACA,gBAAMsG,YAAY,GACd,KAAKxG,YAAL,GAAoB,CAApB,GAAyB,KAAKC,aAAL,GAAqB,KAAKD,YAA3B,GAA2C,GAAnE,GAAyE,CAD7E;;AAGA,gBAAI,KAAKA,YAAL,GAAoB,CAAxB,EAA2B;AACvB8B,cAAAA,OAAO,CAAClB,GAAR,wFACwB,KAAKZ,YAD7B,yCACoDwG,YAAY,CAACP,OAAb,CAAqB,CAArB,CADpD;;AAIA,kBAAIO,YAAY,GAAG,EAAnB,EAAuB;AACnB1E,gBAAAA,OAAO,CAACC,IAAR,+DACmByE,YAAY,CAACP,OAAb,CAAqB,CAArB,CADnB;AAGH;AACJ;;AAED,iBAAK/F,qBAAL,GAA6BwB,GAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACY+E,QAAAA,gBAAgB,CAAClB,QAAD,EAAoC;AACxD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkC,CAACD,QAAQ,CAACC,SAAT,CAAmBkB,QAA1D,EAAoE;AAChEnB,YAAAA,QAAQ,CAACC,SAAT,CAAmBkB,QAAnB,GAA8B,IAA9B;;AACA,gBAAI,OAAOnB,QAAQ,CAACoB,OAAhB,KAA4B,UAAhC,EAA4C;AACxCpB,cAAAA,QAAQ,CAACoB,OAAT;AACH,aAJ+D,CAKhE;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,CAACrB,QAAD,EAAoC;AACzD,cAAIA,QAAQ,IAAIA,QAAQ,CAACC,SAArB,IAAkCD,QAAQ,CAACC,SAAT,CAAmBkB,QAAzD,EAAmE;AAC/DnB,YAAAA,QAAQ,CAACC,SAAT,CAAmBkB,QAAnB,GAA8B,KAA9B;;AACA,gBAAI,OAAOnB,QAAQ,CAACsB,aAAhB,KAAkC,UAAtC,EAAkD;AAC9CtB,cAAAA,QAAQ,CAACsB,aAAT;AACH,aAJ8D,CAK/D;AACA;;AACH;AACJ;AAED;AACJ;AACA;;;AACYtG,QAAAA,YAAY,CAACqC,KAAD,EAAoB;AACpC;AACA,eAAK1D,gBAAL,CAAsBC,CAAtB,GAA0ByD,KAAK,CAACI,YAAN,EAA1B;AACA,eAAK9D,gBAAL,CAAsBE,CAAtB,GAA0BwD,KAAK,CAACM,YAAN,EAA1B;AACA,eAAKlE,YAAL,GAAoByC,IAAI,CAACC,GAAL,EAApB,CAJoC,CAMpC;AACA;;AAEA,cAAMoF,OAAO,GAAG,KAAKnE,oBAAL,CAA0BC,KAA1B,CAAhB;;AACA,cAAIkE,OAAJ,EAAa;AACT,iBAAKlI,kBAAL,GAA0BkI,OAA1B;AACA,iBAAKL,gBAAL,CAAsBK,OAAtB;AACH,WAHD,MAGO,CACH;AACH;AACJ;AAED;AACJ;AACA;;;AACYrG,QAAAA,WAAW,CAACmC,KAAD,EAAoB;AACnC,cAAMpB,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;AACA,cAAMqB,QAAQ,GAAGH,KAAK,CAACI,YAAN,EAAjB;AACA,cAAMC,QAAQ,GAAGL,KAAK,CAACM,YAAN,EAAjB,CAHmC,CAKnC;;AACA,cAAI1B,WAAW,GAAG,KAAKxC,YAAnB,GAAkC,KAAKC,iBAA3C,EAA8D;AAC1D;AACH,WARkC,CAUnC;;;AACA,cAAMkE,MAAM,GAAGC,IAAI,CAACC,GAAL,CAASN,QAAQ,GAAG,KAAK7D,gBAAL,CAAsBC,CAA1C,CAAf;AACA,cAAMmE,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASJ,QAAQ,GAAG,KAAK/D,gBAAL,CAAsBE,CAA1C,CAAf;AACA,cAAMmE,QAAQ,GAAGH,IAAI,CAACI,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBG,MAAM,GAAGA,MAArC,CAAjB;;AAEA,cAAIC,QAAQ,GAAG,KAAKlE,qBAApB,EAA2C;AACvC;AACH,WAjBkC,CAmBnC;;;AACA,cAAI,KAAKT,kBAAL,IAA2B2E,QAAQ,GAAG,KAAKlE,qBAAL,GAA6B,CAAvE,EAA0E;AACtE;AACA,iBAAKL,YAAL,GAAoBwC,WAApB;AACA,iBAAKtC,gBAAL,CAAsBC,CAAtB,GAA0B4D,QAA1B;AACA,iBAAK7D,gBAAL,CAAsBE,CAAtB,GAA0B6D,QAA1B;AACA;AACH,WA1BkC,CA4BnC;;;AACA,eAAKjE,YAAL,GAAoBwC,WAApB;AACA,eAAKtC,gBAAL,CAAsBC,CAAtB,GAA0B4D,QAA1B;AACA,eAAK7D,gBAAL,CAAsBE,CAAtB,GAA0B6D,QAA1B,CA/BmC,CAiCnC;;AACA,cAAM6D,OAAO,GAAG,KAAKnE,oBAAL,CAA0BC,KAA1B,CAAhB,CAlCmC,CAoCnC;;AACA,cAAI,KAAKhE,kBAAL,KAA4BkI,OAAhC,EAAyC;AACrC;AACA,gBAAI,KAAKlI,kBAAT,EAA6B;AACzB,mBAAKgI,iBAAL,CAAuB,KAAKhI,kBAA5B;AACH,aAJoC,CAMrC;;;AACA,gBAAIkI,OAAJ,EAAa;AACT,mBAAKL,gBAAL,CAAsBK,OAAtB,EADS,CAET;AACA;AACH;;AAED,iBAAKlI,kBAAL,GAA0BkI,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACYnG,QAAAA,UAAU,CAACiC,KAAD,EAAoB;AAClC;AACA,cAAI,CAAC,KAAKrB,eAAL,EAAL,EAA6B;AACzB;AACA,gBAAI,KAAK3C,kBAAT,EAA6B;AACzB,mBAAKgI,iBAAL,CAAuB,KAAKhI,kBAA5B;AACH;;AACD,iBAAKA,kBAAL,GAA0B,IAA1B;AACA;AACH,WATiC,CAWlC;;;AACA,cAAMkI,OAAO,GAAG,KAAKnE,oBAAL,CAA0BC,KAA1B,CAAhB;;AAEA,cAAI,KAAKhE,kBAAL,IAA2BkI,OAAO,KAAK,KAAKlI,kBAAhD,EAAoE;AAAA;;AAChE;AACA,iBAAKgI,iBAAL,CAAuB,KAAKhI,kBAA5B;AAEA;AAAA;AAAA,8BAAKgC,GAAL,CAASC,WAAT,0DACkB,+BAAKjC,kBAAL,CAAwBoC,IAAxB,2CAA8BkF,IAA9B,KAAsC,MADxD,GAJgE,CAQhE;;AACA,gBAAI,KAAKvE,wBAAL,CAA8B,KAAK/C,kBAAnC,CAAJ,EAA4D;AACxD;AACA,mBAAKG,iBAAL,GAAyB,IAAzB;AACA,mBAAKF,aAAL,GAAqB4C,IAAI,CAACC,GAAL,EAArB,CAHwD,CAKxD;;AACA,kBAAI,KAAK/C,UAAL,IAAmB,KAAKA,UAAL,CAAgBkD,kBAAvC,EAA2D;AACvD,oBAAI;AAAA;;AACA,uBAAKlD,UAAL,CAAgBkD,kBAAhB,CAAmCkF,UAAnC,CAA8C,KAAKnI,kBAAL,CAAwBoC,IAAtE;AACA;AAAA;AAAA,oCAAKJ,GAAL,CAASC,WAAT,sEACoB,gCAAKjC,kBAAL,CAAwBoC,IAAxB,4CAA8BkF,IAA9B,KAAsC,MAD1D;AAGH,iBALD,CAKE,OAAOc,KAAP,EAAc;AACZlF,kBAAAA,OAAO,CAACkF,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACH;AACJ,eAfuD,CAiBxD;;;AACAC,cAAAA,UAAU,CAAC,MAAM;AACb,qBAAKlI,iBAAL,GAAyB,KAAzB;AACH,eAFS,EAEP,KAAKD,UAFE,CAAV;AAGH,aArBD,MAqBO,CACN;AACJ,WAhCD,MAgCO;AACH;AACA,gBAAI,KAAKF,kBAAT,EAA6B;AACzB,mBAAKgI,iBAAL,CAAuB,KAAKhI,kBAA5B;AACH,aAFD,MAEO,CACH;AACH;AACJ,WArDiC,CAuDlC;;;AACA,eAAKA,kBAAL,GAA0B,IAA1B;AACH;;AAzeiD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, input, Input, PhysicsSystem } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PHY_GROUP } from '../../common/ClientConst';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\n\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { GameEntity } from './GameEntity';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 物品交互管理器 - 智能触摸组件版本\n * 负责触摸事件的捕获和处理，与类版本协同工作\n */\n@ccclass('ItemInteractionManager')\nexport class ItemInteractionManager extends Component {\n    private gameEntity: GameEntity | null = null;\n    private currentTouchedItem: ItemSceneViewComp | null = null;\n\n    // 🎯 防抖和状态管理\n    private lastClickTime: number = 0;\n    private clickDelay: number = 100; // 100ms防抖延迟\n    private isProcessingClick: boolean = false;\n\n    // 🚀 性能优化：触摸移动节流 - 增强版\n    private lastMoveTime: number = 0;\n    private moveThrottleDelay: number = 33; // 30FPS限制，约33ms (降低检测频率)\n    private lastMovePosition: { x: number; y: number } = { x: 0, y: 0 };\n    private moveDistanceThreshold: number = 10; // 增加像素阈值到10px，减少不必要的检测\n\n    // 🎯 射线检测缓存优化\n    private raycastCache: Map<string, { items: ItemSceneViewComp[]; timestamp: number }> =\n        new Map();\n    private raycastCacheTimeout: number = 100; // 缓存100ms，减少重复计算\n    private lastRaycastPosition: { x: number; y: number } = { x: -1, y: -1 };\n    private raycastPositionThreshold: number = 8; // 射线检测位置阈值\n\n    // 🎯 多层检测优化 - 针对堆叠模型\n    private currentLayerIndex: number = 0; // 当前检测的层级索引\n    private layerSwitchDelay: number = 200; // 层级切换延迟200ms\n    private lastLayerSwitchTime: number = 0;\n    private slowMoveThreshold: number = 3; // 慢速移动阈值3px\n    private slowMoveDetectionTime: number = 500; // 慢速移动检测时间500ms\n\n    // 🎯 简单性能监控\n    private raycastCount: number = 0;\n    private cacheHitCount: number = 0;\n    private lastPerformanceReport: number = 0;\n\n    onLoad() {\n        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        oops.log.logBusiness('🎯 ItemInteractionManager触摸事件监听已注册');\n    }\n\n    start() {\n        // 在start中获取GameEntity，确保ent属性已经设置\n        const gameSceneViewComp = this.node.getComponent(GameSceneViewComp);\n        if (gameSceneViewComp && gameSceneViewComp.ent) {\n            this.gameEntity = gameSceneViewComp.ent;\n            oops.log.logBusiness(\n                `✅ ItemInteractionManager获取到GameEntity: ${this.gameEntity ? '成功' : '失败'}`\n            );\n        } else {\n            oops.log.logError('❌ ItemInteractionManager无法获取GameEntity或GameSceneViewComp');\n        }\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n\n        // 🧹 清理射线检测缓存\n        this.raycastCache.clear();\n        oops.log.logBusiness('🧹 ItemInteractionManager已清理缓存并注销事件监听');\n    }\n\n    /**\n     * 🔍 检查是否可以处理点击\n     */\n    private canProcessClick(): boolean {\n        const currentTime = Date.now();\n\n        // 防抖检查\n        if (currentTime - this.lastClickTime < this.clickDelay) {\n            // console.log('🛡️ 点击过快，触发防抖保护');\n            return false;\n        }\n\n        // 处理状态检查\n        if (this.isProcessingClick) {\n            // console.log('🛡️ 正在处理点击，跳过重复处理');\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🎯 智能物品选择逻辑\n     */\n    private intelligentItemSelection(item: ItemSceneViewComp): boolean {\n        if (!this.gameEntity || !this.gameEntity.interactionManager) {\n            console.warn('⚠️ GameEntity或InteractionManager未准备好');\n            return false;\n        }\n\n        const itemNode = item.node;\n        const interactionManager = this.gameEntity.interactionManager;\n\n        // 🔍 检查物品是否在收集槽管理器中\n        if (interactionManager.slotManager) {\n            const slot = interactionManager.slotManager.getSlotByItem(itemNode);\n\n            if (slot) {\n                // 物品在收集槽中\n                if (slot.index >= 7) {\n                    // 在额外槽位(7-9)，可以选中移动到主槽位\n                    // console.log(`🔄 额外槽位物品可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return true;\n                } else {\n                    // 在主槽位(0-6)，不能选中\n                    // console.log(`🚫 主槽位物品不可选中: ${itemNode.name} (槽位${slot.index})`);\n                    return false;\n                }\n            }\n        }\n\n        // 🔍 检查物品是否在场景中可选择\n        const isSelectable = this.gameEntity.GameModel.allItemsToPick.has(item.getItemId());\n        if (!isSelectable) {\n            // console.log(`🚫 物品不在可选择列表中: ${itemNode.name}`);\n            return false;\n        }\n\n        // 🔍 检查收集槽是否已满\n        if (interactionManager.slotManager && !interactionManager.slotManager.canAddItem()) {\n            // console.log(`🚫 收集槽已满，无法选择新物品: ${itemNode.name}`);\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 🚀 优化版射线检测 - 带缓存和智能跳过\n     */\n    private detectItemAtPosition(event: EventTouch): ItemSceneViewComp | null {\n        if (!this.gameEntity) {\n            return null;\n        }\n\n        const camera = smc.camera.CameraModel.camera;\n        if (!camera) {\n            return null;\n        }\n\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 检查是否为慢速移动（可能想要切换到下层模型）\n        const deltaX = Math.abs(currentX - this.lastRaycastPosition.x);\n        const deltaY = Math.abs(currentY - this.lastRaycastPosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n        const isSlowMove = distance < this.slowMoveThreshold;\n\n        // 🎯 检查缓存\n        const cacheKey = `${Math.floor(currentX / this.raycastPositionThreshold)}_${Math.floor(currentY / this.raycastPositionThreshold)}`;\n        const cached = this.raycastCache.get(cacheKey);\n\n        if (cached && Date.now() - cached.timestamp < this.raycastCacheTimeout) {\n            this.cacheHitCount++;\n            this.reportPerformanceIfNeeded();\n\n            // 🎯 如果是慢速移动且有多个物品，尝试切换层级\n            if (isSlowMove && cached.items.length > 1) {\n                return this.handleLayerSwitching(cached.items);\n            }\n\n            return cached.items[0] || null;\n        }\n\n        // 🎯 执行多层射线检测\n        const allItems = this.performMultiLayerRaycast(currentX, currentY);\n\n        // 🎯 更新缓存\n        this.raycastCache.set(cacheKey, { items: allItems, timestamp: Date.now() });\n\n        // 🎯 清理过期缓存\n        this.cleanupCache();\n\n        // 更新最后检测位置\n        this.lastRaycastPosition.x = currentX;\n        this.lastRaycastPosition.y = currentY;\n\n        // 🎯 返回合适的物品\n        if (isSlowMove && allItems.length > 1) {\n            return this.handleLayerSwitching(allItems);\n        } else {\n            // 🎯 快速移动时重置层级索引，回到顶层\n            this.currentLayerIndex = 0;\n        }\n\n        return allItems[0] || null;\n    }\n\n    /**\n     * 🎯 执行多层射线检测 - 获取堆叠位置的所有物品\n     */\n    private performMultiLayerRaycast(x: number, y: number): ItemSceneViewComp[] {\n        const startTime = performance.now();\n        const camera = smc.camera.CameraModel.camera;\n        const ray = camera.screenPointToRay(x, y);\n        const raycastMask = PHY_GROUP.ITEM | PHY_GROUP.ITEM_BOX | PHY_GROUP.ITEM_BOX_EXTRA;\n\n        const allItems: ItemSceneViewComp[] = [];\n        const maxLayers = 3; // 最多检测3层，平衡性能和功能\n        const disabledColliders: any[] = [];\n\n        try {\n            // 🎯 逐层检测：临时禁用上层碰撞体来检测下层\n            for (let layer = 0; layer < maxLayers; layer++) {\n                if (PhysicsSystem.instance.raycastClosest(ray, raycastMask)) {\n                    const result = PhysicsSystem.instance.raycastClosestResult;\n                    const hitNode = result.collider.node;\n                    const itemComp = hitNode.getComponent(ItemSceneViewComp);\n\n                    if (itemComp && itemComp.ItemModel) {\n                        // 避免重复添加同一个物品\n                        if (!allItems.find(item => item.node === hitNode)) {\n                            allItems.push(itemComp);\n                        }\n\n                        // 临时禁用这个碰撞体，以便检测下一层\n                        const collider = result.collider;\n                        if (collider.enabled) {\n                            collider.enabled = false;\n                            disabledColliders.push(collider);\n                        }\n                    } else {\n                        break; // 没有找到有效物品，停止检测\n                    }\n                } else {\n                    break; // 没有更多碰撞，停止检测\n                }\n            }\n        } finally {\n            // 🎯 恢复所有被禁用的碰撞体（确保在任何情况下都会执行）\n            disabledColliders.forEach(collider => {\n                if (collider && collider.isValid) {\n                    collider.enabled = true;\n                }\n            });\n        }\n\n        // 🎯 记录性能数据\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n\n        if (duration > 3) {\n            // 多层检测阈值稍高\n            console.warn(\n                `⚠️ 多层射线检测耗时: ${duration.toFixed(2)}ms，检测到${allItems.length}个物品`\n            );\n        }\n\n        return allItems;\n    }\n\n    /**\n     * 🎯 处理层级切换逻辑 - 慢速移动时在堆叠物品间切换\n     */\n    private handleLayerSwitching(items: ItemSceneViewComp[]): ItemSceneViewComp | null {\n        if (items.length <= 1) {\n            return items[0] || null;\n        }\n\n        const now = Date.now();\n\n        // 🎯 检查是否应该切换层级\n        if (now - this.lastLayerSwitchTime > this.layerSwitchDelay) {\n            this.currentLayerIndex = (this.currentLayerIndex + 1) % items.length;\n            this.lastLayerSwitchTime = now;\n\n            console.log(\n                `🔄 切换到第${this.currentLayerIndex + 1}层物品: ${items[this.currentLayerIndex].node?.name || '未知'}`\n            );\n        }\n\n        return items[this.currentLayerIndex] || items[0];\n    }\n\n    /**\n     * 🎯 清理过期缓存\n     */\n    private cleanupCache(): void {\n        if (this.raycastCache.size > 50) {\n            const now = Date.now();\n            const keysToDelete: string[] = [];\n\n            this.raycastCache.forEach((value, key) => {\n                if (now - value.timestamp > this.raycastCacheTimeout * 2) {\n                    keysToDelete.push(key);\n                }\n            });\n\n            keysToDelete.forEach(key => this.raycastCache.delete(key));\n        }\n    }\n\n    /**\n     * 🎯 性能报告（如果需要）\n     */\n    private reportPerformanceIfNeeded(): void {\n        const now = Date.now();\n        if (now - this.lastPerformanceReport > 10000) {\n            // 每10秒报告一次\n            const cacheHitRate =\n                this.raycastCount > 0 ? (this.cacheHitCount / this.raycastCount) * 100 : 0;\n\n            if (this.raycastCount > 0) {\n                console.log(\n                    `🎯 射线检测性能报告: 总次数=${this.raycastCount}, 缓存命中率=${cacheHitRate.toFixed(1)}%`\n                );\n\n                if (cacheHitRate < 30) {\n                    console.warn(\n                        `⚠️ 缓存命中率较低: ${cacheHitRate.toFixed(1)}%，建议优化缓存策略`\n                    );\n                }\n            }\n\n            this.lastPerformanceReport = now;\n        }\n    }\n\n    /**\n     * 应用触摸效果到物品\n     */\n    private applyTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && !itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = true;\n            if (typeof itemComp.onTouch === 'function') {\n                itemComp.onTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`✅ 物品 ${itemComp.node?.name || '未知物品'} 开始触摸发光`);\n        }\n    }\n\n    /**\n     * 取消物品的触摸效果\n     */\n    private cancelTouchEffect(itemComp: ItemSceneViewComp): void {\n        if (itemComp && itemComp.ItemModel && itemComp.ItemModel.touching) {\n            itemComp.ItemModel.touching = false;\n            if (typeof itemComp.onCancelTouch === 'function') {\n                itemComp.onCancelTouch();\n            }\n            // 高频交互日志改为trace级别，减少日志噪音\n            // oops.log.logTrace(`❌ 物品 ${itemComp.node?.name || '未知物品'} 取消触摸发光`);\n        }\n    }\n\n    /**\n     * 处理触摸开始事件\n     */\n    private onTouchStart(event: EventTouch) {\n        // 🚀 初始化移动位置缓存\n        this.lastMovePosition.x = event.getLocationX();\n        this.lastMovePosition.y = event.getLocationY();\n        this.lastMoveTime = Date.now();\n\n        // 高频交互日志注释掉，减少日志噪音\n        // oops.log.logTrace('🎯 触摸开始');\n\n        const hitItem = this.detectItemAtPosition(event);\n        if (hitItem) {\n            this.currentTouchedItem = hitItem;\n            this.applyTouchEffect(hitItem);\n        } else {\n            // oops.log.logTrace('🎯 触摸开始：未击中任何物品');\n        }\n    }\n\n    /**\n     * 🚀 超级优化版触摸移动事件处理 - 智能节流和预测\n     */\n    private onTouchMove(event: EventTouch) {\n        const currentTime = Date.now();\n        const currentX = event.getLocationX();\n        const currentY = event.getLocationY();\n\n        // 🎯 时间节流：限制检测频率到30FPS\n        if (currentTime - this.lastMoveTime < this.moveThrottleDelay) {\n            return;\n        }\n\n        // 🎯 距离阈值：只有移动足够距离才触发检测\n        const deltaX = Math.abs(currentX - this.lastMovePosition.x);\n        const deltaY = Math.abs(currentY - this.lastMovePosition.y);\n        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n\n        if (distance < this.moveDistanceThreshold) {\n            return;\n        }\n\n        // 🎯 智能跳过：如果当前已有触摸物品且移动距离不大，减少检测频率\n        if (this.currentTouchedItem && distance < this.moveDistanceThreshold * 2) {\n            // 更新位置但不执行射线检测，减少性能消耗\n            this.lastMoveTime = currentTime;\n            this.lastMovePosition.x = currentX;\n            this.lastMovePosition.y = currentY;\n            return;\n        }\n\n        // 更新缓存\n        this.lastMoveTime = currentTime;\n        this.lastMovePosition.x = currentX;\n        this.lastMovePosition.y = currentY;\n\n        // 🎯 执行射线检测（现在频率大大降低）\n        const hitItem = this.detectItemAtPosition(event);\n\n        // 如果当前触摸的物品和检测到的物品不同，需要切换\n        if (this.currentTouchedItem !== hitItem) {\n            // 取消之前物品的触摸效果\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n\n            // 应用新物品的触摸效果\n            if (hitItem) {\n                this.applyTouchEffect(hitItem);\n                // 高频交互日志注释掉，减少日志噪音\n                // oops.log.logTrace(`🔄 触摸切换到物品: ${hitItem.node?.name || '未知物品'}`);\n            }\n\n            this.currentTouchedItem = hitItem;\n        }\n    }\n\n    /**\n     * 处理触摸结束事件 - 智能版本\n     */\n    private onTouchEnd(event: EventTouch) {\n        // 🛡️ 防抖和状态检查\n        if (!this.canProcessClick()) {\n            // 清除触摸状态但不处理选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            }\n            this.currentTouchedItem = null;\n            return;\n        }\n\n        // 只有在物品上松手才算选中\n        const hitItem = this.detectItemAtPosition(event);\n\n        if (this.currentTouchedItem && hitItem === this.currentTouchedItem) {\n            // 在同一个物品上松手，检查是否可以选中\n            this.cancelTouchEffect(this.currentTouchedItem);\n\n            oops.log.logBusiness(\n                `✅ 检测到物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n            );\n\n            // 🎯 智能选择检查\n            if (this.intelligentItemSelection(this.currentTouchedItem)) {\n                // 设置处理状态\n                this.isProcessingClick = true;\n                this.lastClickTime = Date.now();\n\n                // 触发选择逻辑\n                if (this.gameEntity && this.gameEntity.interactionManager) {\n                    try {\n                        this.gameEntity.interactionManager.chooseItem(this.currentTouchedItem.node);\n                        oops.log.logBusiness(\n                            `🎯 成功触发物品选择: ${this.currentTouchedItem.node?.name || '未知物品'}`\n                        );\n                    } catch (error) {\n                        console.error('❌ 物品选择过程中发生错误:', error);\n                    }\n                }\n\n                // 延迟重置处理状态\n                setTimeout(() => {\n                    this.isProcessingClick = false;\n                }, this.clickDelay);\n            } else {\n            }\n        } else {\n            // 不在原物品上松手，取消选择\n            if (this.currentTouchedItem) {\n                this.cancelTouchEffect(this.currentTouchedItem);\n            } else {\n                // oops.log.logTrace('❌ 在空白区域松手，无选择');\n            }\n        }\n\n        // 清除当前触摸状态\n        this.currentTouchedItem = null;\n    }\n}\n"]}